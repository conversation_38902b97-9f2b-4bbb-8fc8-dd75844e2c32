<?php
/**
 * @Project Crawls.DauThau.Info
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2022 VINADES.,JSC. All rights reserved
 * @Code Ngọc <PERSON> (<EMAIL>)
 * Tool bóc danh sách: <PERSON>ết quả lựa chọn nhà đầu tư (dự án xã hội hóa)
 * https://vinades.org/dauthau/dauthau.info/-/issues/3407
 */

define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME))));
require NV_ROOTDIR . '/mainfile.php';

$id = (int) $request_mode->get('id', 0); // php kqlcnt_detail.php --id=285003
if ($id > 0) {
    $_tbmdt = $dbcr->query('SELECT * FROM `nv23_lcndt_tbmt_url` WHERE id = ' . $id)->fetch();
} else {
    $uniqid = uniqid('', true);
    $run_time = NV_CURRENTTIME +  7 * 86400;
    $dbcr->query("UPDATE nv23_lcndt_tbmt_url SET url_run_kq='-" . NV_CURRENTTIME . "', uniqid_kq='" . $uniqid . "'  WHERE (`url_run_kq`= 0 OR url_run_kq > " . $run_time . ") AND uniqid_kq='' AND type_project = 'es-bido-notify-social-p' ORDER BY `id` DESC LIMIT 1");
    $_tbmdt = $dbcr->query("SELECT * FROM `nv23_lcndt_tbmt_url` WHERE `uniqid_kq`='" . $uniqid . "' ORDER BY `id` DESC LIMIT 1")->fetch();
}
if (!empty($_tbmdt)) {
    $detail2 = json_decode($_tbmdt['detail2'], true);
    $url = 'https://muasamcong.mpi.gov.vn/web/guest/investor-selection?p_p_id=egpportalinvestorselectionv2_WAR_egpportalinvestorselectionv2&p_p_lifecycle=0&p_p_state=normal&p_p_mode=view&_egpportalinvestorselectionv2_WAR_egpportalinvestorselectionv2_render=detail&id=' . $detail2['pid'] . '&type=es-project-social-p&no=' . $detail2['pno'];
    echo "Bắt đầu bóc url: " . $url ."\n";
    $resultId = gethtml($url, 1);
    echo "Resultid: " . $resultId . "\n";
    if (!empty($resultId)) {

        $dbcr->query("UPDATE nv23_lcndt_tbmt_url SET url_run_kq=" . NV_CURRENTTIME . ", id_result_msc = " . $db->quote($resultId) . " WHERE id = " . $_tbmdt['id']);
        $data = geturlpage($resultId, 1);
        $detail1 = [];
        if (!empty($data)) {
            // Cập nhật hoặc insert vào bảng url kqlcnđt
            $row['locations'] = $detail1['locations'] = $data['locations'] ?? '';
            $row['notifyNo'] = $detail1['notifyNo'] = $data['notifyNo'] ?? '';
            $row['notifyVersion'] = $detail1['notifyVersion'] = $data['notifyVersion'] ?? '';
            $row['type_project'] = $detail1['type'] = 'es-bidr-social-result';
            $row['bid_form'] = $detail1['investForm'] = $detail1['bidForm'] = $detail1['planNoStand'] = '';
            $detail1['isInternet'] = 0;
            $detail1['publicDate'] = $data['publicDate'];
            $detail1['projectName'] = $data['pname'];
            $detail1['bidInvestorName'] = $data['bidInvestorName'];
            $detail1['bidInvestorCode'] = $data['bidInvestorCode'];
            $detail1['notifyNoStand'] = $data['notifyNoStand'];
            $detail1['havingInvestor'] = $data['havingInvestor'];
            $detail1['status'] = $data['status'];
            $detail1['typeOfProjectInvestorSelection'] = 'es-social';
            $org_code_bmt = isset($data['procuringEntityCode']) ? $data['procuringEntityCode'] : '';
            $row['ten_ben_moi_thau'] = isset($data['procuringEntityName']) ? $data['procuringEntityName'] : '';
            $row['solicitor_id'] = get_solicitor_id($row['ten_ben_moi_thau'], $org_code_bmt);
            $row['ten_ben_moi_thau'] = preg_replace("/^[z,Z,1]{1}[0-9]{6}\s*\-\s*(.+)$/", "\\1", $row['ten_ben_moi_thau']);
            $data['notifyNo'] = isset($data['notifyNo']) ? $data['notifyNo'] : '';
            $data['notifyVersion'] = isset($data['notifyVersion']) ? $data['notifyVersion'] : '00';
            $row['so_tbmt'] = $data['notifyNo'] . '-' . $data['notifyVersion'];
            $row['planNo'] = !empty($data['planNo']) ? $data['planNo'] : '';
            $row['pno'] = !empty($data['pno']) ? $data['pno'] : '';
            $row['from_source'] = !empty($data['fromSource']) ? $data['fromSource'] : '';
            $row['ten_du_an'] = !empty($data['pname']) ? $data['pname'] : '';
            $row['data_type'] = !empty($data['dataType']) ? $data['dataType'] : 0;
            $row['loai_thong_bao'] = '';
            $row['loai_hop_dong'] = 'Hợp đồng dự án xã hội hóa';
            $row['ptype'] = 'es-bidp-project-invest-sdd-p';
            $row['contract_id'] = isset($data['contractId']) ? $data['contractId'] : '';
            $row['loai_du_an'] = 'Dự án đầu tư kinh doanh (Dự án chuyên ngành/XHH)';            
            $row['thoi_diem_dang_tai'] = isset($data['publicDate']) ? strtotime($data['publicDate']) : 0;            
            $row['thoi_han_thuc_hien_du_an'] = '';
            $row['muc_tieu_cong_nang_cua_du_an'] = '';
            $row['cac_noi_dung_can_luu_y'] = '';
            $row['ten_nha_dau_tu_trung_thau'] = $detail1['bidInvestorName'] = !empty($data['bidInvestorName']) ? $data['bidInvestorName'] : '';
            $row['so_dkkd'] = $data['bidInvestorCode'] ?? '';
            $row['address_bidder'] = $data['bidInvestorLocation'] ?? '';
            $row['noi_dung_dinh_kem'] = '';
            $row['noi_dung_khac'] = !empty($data['description']) ? $data['description'] : '';
            $row['id_msc'] = $detail1['id'] = isset($data['id']) ? $data['id'] : '';
            $row['cac_dieu_kien_su_dung_dat'] = '';
            $row['gia_dich_vu_phan_von_gop_cua_nha_nuoc'] = '';            
            $row['pid'] = isset($data['pid']) ? $data['pid'] : '';
            $row['number_document'] = isset($data['decisionNo']) ? $data['decisionNo'] : '';
            $row['decision_agency'] = isset($data['decisionAgency']) ? $data['decisionAgency'] : '';
            $row['date_approval'] = isset($data['decisionDate']) ? strtotime($data['decisionDate']) : 0;
            $row['document_approval'] = isset($data['decisionFileName']) ? $data['decisionFileName'] : '';
            $row['decision_file'] = isset($data['decisionFileId']) ? $data['decisionFileId'] : '';
            $row['decision_created'] = isset($data['createdDate']) ? $data['createdDate'] : 0;
            $row['having_investor'] = isset($data['havingInvestor']) ? $data['havingInvestor'] : '';
            $row['plan_negotiating'] = isset($data['planNegotiating']) ? $data['planNegotiating'] : '';
            $row['compensation_value'] = isset($data['compensationValue']) ? $data['compensationValue'] : 0;
            $row['state_budget'] = isset($data['stateBudget']) ? $data['stateBudget'] : 0;
            $row['progres_project'] = isset($data['bidStartPeriod']) ? $data['bidStartPeriod'] : '';
            $row['other_info'] = isset($data['description']) ? $data['description'] : '';

            $_duan = $db->query("SELECT dd_th, total_cost_number FROM `" . NV_PREFIXLANG . "_bidding_project_proposal` WHERE id_msc = " . $db->quote($data['pid']))->fetch();
            $_plan_method = $db->query("SELECT hinhthucluachon, is_domestic FROM `" . NV_PREFIXLANG . "_bidding_plans_project` WHERE id_msc = " . $db->quote($data['planId']))->fetch();
            $_dttbmdt = json_decode($_tbmdt['detail2'], true);

            $row['dia_diem_thuc_hien'] = $_duan['dd_th'] ?? '';
            $row['hinh_thuc_lua_chon_ndt'] =  $_plan_method['hinhthucluachon'] ? convertMethodLCNT($_plan_method['hinhthucluachon']) : '';
            $row['is_domestic'] = $row['data_type'] != 1 ? 1 : ($_dttbmdt['isDomestic'] ?? 0);
            $row['tong_chi_phi_thuc_hien_du_an'] = $data['feeTotal'] ?? $_duan['total_cost_number'];
            if ($data['status'] == '01') {
                $row['trang_thai_thong_bao'] = 'Đã đăng tải';
            } else if ($data['status'] == '03') {
                $row['trang_thai_thong_bao'] = 'Đã hủy';
            } else if ($data['status'] == '04') {
                $row['trang_thai_thong_bao'] = 'Đã kết thúc';
            }
            
            $id_kq = $dbcr->query("SELECT id FROM `nv23_lcndt_kqlcndt_url` WHERE id_msc  = " . $db->quote($data['id']))->fetchColumn();
            $uniqid_kq = uniqid('', true);
            if (!empty($id_kq)) {
                $prepared = $dbcr->prepare("UPDATE nv23_lcndt_kqlcndt_url SET url_run=" . NV_CURRENTTIME . ", location=:location, bid_form=:bid_form, detail1 = :detail1, detail2=:detail2, uniqid=:uniqid WHERE id = " . $id_kq);
                $_kqlcndt_id = $id_kq;
            } else {
                $prepared = $dbcr->prepare("INSERT INTO `nv23_lcndt_kqlcndt_url` (`id_msc`, `url_run`, `notifyno`, `notifyversion`, `type_project`, `location`, `bid_form`, `detail1`, `detail2`, `uniqid`) VALUES (:id_msc, " . NV_CURRENTTIME . ", :notifyno, :notifyversion, :type_project, :location, :bid_form, :detail1, :detail2, :uniqid)");
                $prepared->bindParam(':id_msc', $data['id'], PDO::PARAM_STR);
                $prepared->bindParam(':notifyno', $row['notifyNo'], PDO::PARAM_STR);
                $prepared->bindParam(':notifyversion', $row['notifyVersion'], PDO::PARAM_STR);
                $prepared->bindParam(':type_project', $row['type_project'], PDO::PARAM_STR);
            }
            $prepared->bindParam(':uniqid', $uniqid_kq, PDO::PARAM_STR);
            $prepared->bindParam(':location', $row['locations'], PDO::PARAM_STR);
            $prepared->bindParam(':bid_form', $row['bid_form'], PDO::PARAM_STR);
            $prepared->bindValue(':detail1', json_encode($detail1, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT), PDO::PARAM_STR);
            $prepared->bindValue(':detail2', json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT), PDO::PARAM_STR);
            $prepared->execute();
            if (empty($id_kq)) {
                $_kqlcndt_id = $db->lastInsertId();
            }
            
            if ($row['ten_du_an'] == '' || $row['so_tbmt'] == '' || $row['ten_ben_moi_thau'] == '') {
                echo '<br>' . 'Không đủ thông tin bắt buộc' . '<br>';
                echo '<pre>';
                print_r($row);
                echo '</pre>';

                $dbcr->query("UPDATE nv23_lcndt_kqlcndt_url SET dauthau_info='-3', crawls_info = '" . ($row['so_tbmt'] == '' ? 'so_tbmt' : ($row['ten_du_an'] == 'ten_du_an' ? '' : 'ten_ben_moi_thau')) . "' WHERE id=" . $_kqlcndt_id);
                return false;
            }

            // Thêm mới vào bảng nv4_vi_bidding_result_project
            $time = NV_CURRENTTIME;
            $id_result = $db->query('SELECT id FROM ' . NV_PREFIXLANG . '_bidding_result_project WHERE code=' . $db->quote($row['so_tbmt']))
                ->fetchColumn();
            if ($id_result) {
                $row_old = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_bidding_result_project WHERE id = ' . $id_result)->fetch();
                $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_bidding_result_project SET code= :code, pno=:pno, contract_id=:contract_id,soclictor= :soclictor, solicitor_id= :solicitor_id,title= :title,total_cost= :total_cost,post_time = :post_time,bidder_name= :bidder_name,no_business_licence= :no_business_licence, address_bidder=:address_bidder, hinh_thuc= :hinh_thuc,loai_thong_bao= :loai_thong_bao, loai_hd=:loai_hd, is_domestic=:is_domestic, trang_thai_thong_bao=:trang_thai_thong_bao, type_project=:type_project, data_type=:data_type, from_source=:from_source, loai_du_an =:loai_du_an, dia_diem= :dia_diem,time_todo= :time_todo,muc_tieu= :muc_tieu,dieu_kien= :dieu_kien,document_approval= :document_approval,date_approval=:date_approval, decision_agency=:decision_agency, number_document=:number_document,decision_fileid=:decision_fileid,decision_created=:decision_created,plan_negotiating=:plan_negotiating,compensation_value=:compensation_value,state_budget=:state_budget,progres_project=:progres_project,other_info=:other_info,having_investor=:having_investor,dinh_kem= :dinh_kem, noidung_khac= :noidung_khac, updatetime= :updatetime,von_nha_nuoc= :von_nha_nuoc,content= :content, elasticsearch=0, name_staff=:name_staff WHERE id=' . $id_result);
                $stmt->bindParam(':updatetime', $time, PDO::PARAM_INT);
            } else {
                $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_bidding_result_project (code, pno, id_msc, is_new_msc, contract_id, soclictor, solicitor_id, title, total_cost, post_time, bidder_name, no_business_licence, address_bidder, hinh_thuc, loai_thong_bao, loai_hd, is_domestic, trang_thai_thong_bao, type_project, data_type, from_source, loai_du_an, dia_diem, time_todo, muc_tieu, dieu_kien, document_approval, date_approval, decision_agency, number_document,decision_fileid,decision_created,plan_negotiating,compensation_value,state_budget,progres_project,other_info, having_investor,dinh_kem, noidung_khac, von_nha_nuoc, get_time, content, elasticsearch, name_staff, is_msc_new)
                        VALUES (:code, :pno, :id_msc, 1, :contract_id, :soclictor, :solicitor_id, :title, :total_cost, :post_time, :bidder_name, :no_business_licence, :address_bidder, :hinh_thuc, :loai_thong_bao, :loai_hd, :is_domestic, :trang_thai_thong_bao, :type_project, :data_type, :from_source, :loai_du_an, :dia_diem, :time_todo, :muc_tieu, :dieu_kien, :document_approval, :date_approval, :decision_agency, :number_document,:decision_fileid,:decision_created,:plan_negotiating,:compensation_value,:state_budget,:progres_project,:other_info,:having_investor,:dinh_kem, :noidung_khac, :von_nha_nuoc, :get_time, :content,0, :name_staff, 1)');
                $stmt->bindParam(':id_msc', $row['id_msc'], PDO::PARAM_STR);
                $stmt->bindParam(':get_time', $time, PDO::PARAM_INT);
            }

            $time = NV_CURRENTTIME;
            $stmt->bindParam(':code', $row['so_tbmt'], PDO::PARAM_STR);
            $stmt->bindParam(':pno', $row['pno'], PDO::PARAM_STR);
            $stmt->bindParam(':contract_id', $row['contract_id'], PDO::PARAM_STR);
            $stmt->bindParam(':soclictor', $row['ten_ben_moi_thau'], PDO::PARAM_STR);
            $stmt->bindParam(':solicitor_id', $row['solicitor_id'], PDO::PARAM_INT);
            $stmt->bindParam(':title', $row['ten_du_an'], PDO::PARAM_STR);
            $stmt->bindParam(':total_cost', $row['tong_chi_phi_thuc_hien_du_an'], PDO::PARAM_STR);
            $stmt->bindParam(':post_time', $row['thoi_diem_dang_tai'], PDO::PARAM_INT);
            $stmt->bindParam(':bidder_name', $row['ten_nha_dau_tu_trung_thau'], PDO::PARAM_STR);
            $stmt->bindParam(':no_business_licence', $row['so_dkkd'], PDO::PARAM_STR);
            $stmt->bindParam(':address_bidder', $row['address_bidder'], PDO::PARAM_STR);
            $stmt->bindParam(':hinh_thuc', $row['hinh_thuc_lua_chon_ndt'], PDO::PARAM_INT);
            $stmt->bindParam(':loai_thong_bao', $row['loai_thong_bao'], PDO::PARAM_STR);
            $stmt->bindParam(':loai_hd', $row['loai_hop_dong'], PDO::PARAM_STR);
            $stmt->bindParam(':is_domestic', $row['is_domestic'], PDO::PARAM_INT);
            $stmt->bindParam(':trang_thai_thong_bao', $row['trang_thai_thong_bao'], PDO::PARAM_STR);
            $stmt->bindParam(':type_project', $row['type_project'], PDO::PARAM_STR);
            $stmt->bindParam(':data_type', $row['data_type'], PDO::PARAM_INT);
            $stmt->bindParam(':from_source', $row['from_source'], PDO::PARAM_INT);
            $stmt->bindParam(':loai_du_an', $row['loai_du_an'], PDO::PARAM_STR);
            $stmt->bindParam(':time_todo', $row['thoi_han_thuc_hien_du_an'], PDO::PARAM_STR);
            $stmt->bindParam(':dia_diem', $row['dia_diem_thuc_hien'], PDO::PARAM_STR);
            $stmt->bindParam(':muc_tieu', $row['muc_tieu_cong_nang_cua_du_an'], PDO::PARAM_STR);
            $stmt->bindParam(':dieu_kien', $row['cac_dieu_kien_su_dung_dat'], PDO::PARAM_STR);
            $stmt->bindParam(':document_approval', $row['document_approval'], PDO::PARAM_STR);
            $stmt->bindParam(':date_approval', $row['date_approval'], PDO::PARAM_STR);
            $stmt->bindParam(':decision_agency', $row['decision_agency'], PDO::PARAM_STR);
            $stmt->bindParam(':number_document', $row['number_document'], PDO::PARAM_STR);
            $stmt->bindParam(':decision_fileid', $row['decision_file'], PDO::PARAM_STR);
            $stmt->bindParam(':decision_created', $row['decision_created'], PDO::PARAM_STR);
            $stmt->bindParam(':plan_negotiating', $row['plan_negotiating'], PDO::PARAM_STR);
            $stmt->bindParam(':compensation_value', $row['compensation_value'], PDO::PARAM_INT);
            $stmt->bindParam(':state_budget', $row['state_budget'], PDO::PARAM_INT);
            $stmt->bindParam(':progres_project', $row['progres_project'], PDO::PARAM_STR);
            $stmt->bindParam(':other_info', $row['other_info'], PDO::PARAM_STR);
            $stmt->bindParam(':having_investor', $row['having_investor'], PDO::PARAM_STR);
            $stmt->bindParam(':dinh_kem', $row['noi_dung_dinh_kem'], PDO::PARAM_STR);
            $stmt->bindParam(':noidung_khac', $row['noi_dung_khac'], PDO::PARAM_STR);
            $stmt->bindParam(':von_nha_nuoc', $row['gia_dich_vu_phan_von_gop_cua_nha_nuoc'], PDO::PARAM_STR);
            $content = $row['so_tbmt'] . ' ' . $row['ten_du_an'] . ' ' . $row['ten_ben_moi_thau'];
            $content = change_alias($content);
            $content = str_replace('-', ' ', $content);
            $stmt->bindParam(':content', $content, PDO::PARAM_STR);
            $name_staff = $array_name_staff[array_rand($array_name_staff)];
            $stmt->bindParam(':name_staff', $name_staff, PDO::PARAM_STR);
            $exc = $stmt->execute();
            if ($id_result == 0) {
                $id_result = $db->lastInsertId();
            } else {
                $row_new = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_bidding_result_project WHERE id = ' . $id_result)->fetch();
                $status_insert = insert_log_crawls($id_result, 'KQLCDT', $row_old, $row_new);
                if ($status_insert > 0) {
                    echo("LOG: KQLCDT - ID: " . $id_result . "- OK<br/>\n");
                }
            }

            // cập nhật lại trạng thái của bên mời thầu
            $result = $db->query("SELECT id FROM " . BID_PREFIX_GLOBAL . "_solicitor WHERE id =" . $row['solicitor_id']);
            if ($tmp = $result->fetch()) {
                $db->query("UPDATE " . BID_PREFIX_GLOBAL . "_solicitor SET update_data='0'  WHERE id='" . $tmp['id'] . "'");
            }

            $dbcr->query('UPDATE nv23_lcndt_kqlcndt_url SET dauthau_info=1 WHERE id=' . $_kqlcndt_id);
            echo 'Cập nhật xong: ' . $_kqlcndt_id . " (ID: " . $id_result . ")";
            echo "\n";

            $shortList = isset($data['shortList']) ? $data['shortList'] : [];
            if (!empty($id_result) and !empty($shortList)) {
                foreach ($shortList as $v) {
                    $v['orgcode'] = isset($v['investorCode']) ? $v['investorCode'] : '';
                    $v['joint_venture'] = isset($v['ventureName']) ? $v['ventureName'] : '';
                    $v['result_status'] = isset($v['result']) ? $v['result'] : 0;
                    $v['bidder_name'] = isset($v['investorName']) ? $v['investorName'] : '';
                    $v['bidder_address'] = isset($v['investorLocation']) ? $v['investorLocation'] : '';
                    $v['reason'] = isset($v['reason']) ? $v['reason'] : '';
                    $liendanh = !empty($v['joint_venture']) ? 1 : 0;
                    $stmt1_check = $db->query("SELECT * FROM " . NV_PREFIXLANG . "_bidding_result_project_business WHERE resultid = " . $id_result . " AND orgcode = " . $db->quote($v['orgcode']));
                    $result_bussiness = $stmt1_check->fetch();
                    if (empty($result_bussiness)) {
                        $stmt1 = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_bidding_result_project_business (resultid, orgcode, bidder_name, bidder_address, joint_venture, partnership, result_status, reason, get_time) VALUES (:resultid, :orgcode, :bidder_name, :bidder_address, :joint_venture, :partnership, :result_status, :reason, :get_time)');
                    } else {
                        $stmt1 = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_bidding_result_project_business SET bidder_name=:bidder_name, bidder_address=:bidder_address, joint_venture=:joint_venture, partnership=:partnership, result_status=:result_status, reason=:reason, get_time=:get_time WHERE resultid =:resultid AND orgcode =:orgcode');
                    }
                    $stmt1->bindValue(':get_time', NV_CURRENTTIME, PDO::PARAM_STR);
                    $stmt1->bindParam(':resultid', $id_result, PDO::PARAM_INT);
                    $stmt1->bindParam(':orgcode', $v['orgcode'], PDO::PARAM_STR);
                    $stmt1->bindParam(':bidder_name', $v['bidder_name'], PDO::PARAM_STR);
                    $stmt1->bindParam(':bidder_address', $v['bidder_address'], PDO::PARAM_STR);
                    $stmt1->bindParam(':joint_venture', $v['joint_venture'], PDO::PARAM_STR);
                    $stmt1->bindParam(':partnership', $liendanh, PDO::PARAM_STR);
                    $stmt1->bindParam(':result_status', $v['result_status'], PDO::PARAM_STR);
                    $stmt1->bindParam(':reason', $v['reason'], PDO::PARAM_STR);
                    $stmt1->execute();
                }
            }

            // Lấy thông tin chi tiết hợp đồng
            if (!empty($row['contract_id'])) {
                $data_contract = geturlpagecontract($row, 1);
                if (!empty($data_contract)) {
                    $_contract['contract_no'] =  isset($data_contract['contractNo']) ? $data_contract['contractNo'] : '';
                    $_contract['contract_code'] = isset($data_contract['contractCode']) ? $data_contract['contractCode'] : '';
                    $_contract['id_msc'] = isset($data_contract['id']) ? $data_contract['id'] : '';
                    $_contract['signing_date'] = isset($data_contract['sing_date']) ? strtotime($data_contract['sing_date']) : 0;
                    $_contract['contract_type'] = isset($data_contract['contractType']) ? $data_contract['contractType'] : '';
                    $_contract['trang_thai'] = ($data_contract['status'] == '01') ? 'Đã đăng tải' : (($data_contract['status'] == '03') ? 'Đã hủy' : '');
                    $_contract['effective_date'] = isset($data_contract['eff_date']) ? strtotime($data_contract['eff_date']) : 0;
                    $_contract['pcode'] = isset($data_contract['projectCode']) ? $data_contract['projectCode'] : '';
                    $_contract['pname'] = isset($data_contract['projectName']) ? $data_contract['projectName'] : '';
                    $_contract['type_project'] = isset($data_contract['type']) ? $data_contract['type'] : 0;
                    $_contract['agency_code'] = isset($data_contract['agencyCode']) ? $data_contract['agencyCode'] : '';
                    $_contract['agency_address'] = !empty($data_contract['agencyDetail']) ? $data_contract['agencyDetail']['address'] : '';
                    $_contract['agency_name'] = !empty($data_contract['agencyDetail']) ? $data_contract['agencyDetail']['name'] : '';
                    $_contract['agency_phone'] = !empty($data_contract['agencyDetail']) ? $data_contract['agencyDetail']['phoneNumber'] : '';
                    $_contract['capital'] = isset($data_contract['investorDetail']) ? $data_contract['investorDetail']['capital'] : 0;
                    $_contract['role'] = isset($data_contract['investorDetail']) ? $data_contract['investorDetail']['role'] : 0;
                    $_contract['investor_code'] = !empty($data_contract['investorDetail']) ? $data_contract['investorDetail']['investorCode'] : '';
                    $_contract['investor_code'] = ($_contract['role'] == 1 || $_contract['role'] == '1') ? $_contract['investor_code'] : '';
                    $_contract['investor_name'] = !empty($data_contract['investorDetail']) ? $data_contract['investorDetail']['investorName'] : '';
                    $_contract['investor_phone'] = !empty($data_contract['investorDetail']) ? $data_contract['investorDetail']['phoneNumber'] : '';
                    $_contract['investor_address'] = !empty($data_contract['investorDetail']) ? $data_contract['investorDetail']['investorLocation'] : '';
                    $_project_detail = isset($data_contract['projectDetail']) ? $data_contract['projectDetail'] : [];
                    $_contract['field_type'] = isset($_project_detail['field']) ? $_project_detail['field'] : '';
                    $_contract['project_objectives'] = isset($_project_detail['projectObjectives']) ? $_project_detail['projectObjectives'] : '';
                    $_contract['project_scale'] = isset($_project_detail['projectScale']) ? $_project_detail['projectScale'] : '';
                    $_contract['project_address'] = isset($_project_detail['address']) ? $_project_detail['address'] : '';
                    $_contract['land_area'] = isset($_project_detail['landArea']) ? $_project_detail['landArea'] : '';
                    $_contract['scheduled_info'] = isset($_project_detail['scheduledInfo']) ? $_project_detail['scheduledInfo'] : '';
                    $_contract['project_total_fund'] = isset($_project_detail['projectTotalFund']) ? $_project_detail['projectTotalFund'] : 0;
                    $_contract['land_use_fees'] = isset($_project_detail['landUseFees']) ? $_project_detail['landUseFees'] : '';
                    $_contract['plan_negotiating'] = isset($_project_detail['planNegotiating']) ? $_project_detail['planNegotiating'] : '';
                    $_contract['compensation_value'] = isset($_project_detail['compensationValue']) ? $_project_detail['compensationValue'] : 0;
                    $_contract['state_budget'] = isset($_project_detail['stateBudget']) ? $_project_detail['stateBudget'] : 0;
                    $_contract['progres_project'] = isset($_project_detail['progressProject']) ? $_project_detail['progressProject'] : '';
                    $_contract['other_info'] = isset($_project_detail['otherInfo']) ? $_project_detail['otherInfo'] : '';
                    $_contract['addtime'] = isset($data_contract['publicDate']) ? strtotime($data_contract['publicDate']) : 0;
                    $_contract['contract_duration'] = isset($data_contract['contractDuration']) ? $data_contract['contractDuration'] : '';
                    $_contract['transfer_time'] = isset($data_contract['transferTime']) ? $data_contract['transferTime'] : '';
                    $_contract['expected_progress'] = isset($data_contract['expectedProgress']) ? $data_contract['expectedProgress'] : '';
                    $_contract['state_capital'] = isset($_project_detail['stateCapital']) ? $_project_detail['stateCapital'] : '';
                    $_contract['capital_ratio'] = isset($data_contract['investorDetail']) ? (!empty($data_contract['investorDetail']['capitalRatio']) ? $data_contract['investorDetail']['capitalRatio'] : 0) : 0;
                    $updatetime = $gettime = NV_CURRENTTIME;
                    // Lấy Id của hợp đồng đã có trong csdl
                    $id_hd = 0;
                    $id_hd = $db->query('SELECT id FROM ' . NV_PREFIXLANG . '_bidding_contract WHERE contract_code=' . $db->quote($_contract['contract_code']))
                        ->fetchColumn();
                    $update_data_hd = $id_hd > 0 ? 1 : 0;
                    if ($update_data_hd == 0) {
                        $stmt1 = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_bidding_contract
                        (contract_no, contract_code, id_msc, trang_thai, signing_date, contract_type, contract_duration, transfer_time, expected_progress, state_capital, effective_date, pcode, pname, type_project,agency_code, agency_address, agency_name, agency_phone, investor_code, investor_name, investor_phone, investor_address, capital_ratio, capital, role,field_type, project_objectives, project_scale, project_address, land_area, scheduled_info,project_total_fund,land_use_fees,plan_negotiating,compensation_value,state_budget,progres_project,other_info,addtime,gettime, update_time) VALUES
                        (:contract_no, :contract_code, :id_msc, :trang_thai, :signing_date, :contract_type, :contract_duration, :transfer_time, :expected_progress, :state_capital, :effective_date, :pcode, :pname, :type_project,:agency_code, :agency_address, :agency_name, :agency_phone, :investor_code, :investor_name, :investor_phone, :investor_address, :capital_ratio, :capital, :role,:field_type, :project_objectives, :project_scale, :project_address, :land_area,:scheduled_info,:project_total_fund,:land_use_fees,:plan_negotiating,:compensation_value,:state_budget,:progres_project,:other_info,:addtime, :gettime, 0)');
                        $stmt1->bindParam(':contract_code', $_contract['contract_code'], PDO::PARAM_STR);
                        $stmt1->bindParam(':id_msc', $_contract['id_msc'], PDO::PARAM_STR);
                        $stmt1->bindValue(':gettime', $gettime, PDO::PARAM_INT);
                    } else {
                        $row_hd_old = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_bidding_contract WHERE id = ' . $id_hd)->fetch();
                        $stmt1 = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_bidding_contract SET contract_no=:contract_no, trang_thai=:trang_thai, signing_date=:signing_date, contract_type=:contract_type, contract_duration=:contract_duration, transfer_time=:transfer_time, expected_progress=:expected_progress, state_capital=:state_capital, effective_date=:effective_date, pcode=:pcode, pname=:pname, type_project=:type_project,agency_code=:agency_code, agency_address=:agency_address, agency_name=:agency_name, agency_phone=:agency_phone, investor_code=:investor_code, investor_name=:investor_name, investor_phone=:investor_phone,
                        investor_address=:investor_address, capital_ratio=:capital_ratio,capital=:capital, role=:role,field_type=:field_type, project_objectives=:project_objectives,project_scale=:project_scale, project_address=:project_address, land_area=:land_area, scheduled_info=:scheduled_info,project_total_fund=:project_total_fund, land_use_fees=:land_use_fees, plan_negotiating=:plan_negotiating,compensation_value=:compensation_value,state_budget=:state_budget,progres_project=:progres_project,other_info=:other_info,addtime=:addtime,update_time=:update_time, translator=0 WHERE id=' . $id_hd);
                        $stmt1->bindValue(':update_time', $updatetime, PDO::PARAM_INT);
                    }
                    $stmt1->bindParam(':contract_no', $_contract['contract_no'], PDO::PARAM_STR);
                    $stmt1->bindParam(':pname', $_contract['pname'], PDO::PARAM_STR);
                    $stmt1->bindParam(':trang_thai', $_contract['trang_thai'], PDO::PARAM_STR);
                    $stmt1->bindParam(':signing_date', $_contract['signing_date'], PDO::PARAM_INT);
                    $stmt1->bindParam(':contract_type', $_contract['contract_type'], PDO::PARAM_STR);
                    $stmt1->bindParam(':contract_duration', $_contract['contract_duration'], PDO::PARAM_STR);
                    $stmt1->bindParam(':transfer_time', $_contract['transfer_time'], PDO::PARAM_STR);
                    $stmt1->bindParam(':expected_progress', $_contract['expected_progress'], PDO::PARAM_STR);
                    $stmt1->bindParam(':state_capital', $_contract['state_capital'], PDO::PARAM_STR);
                    $stmt1->bindParam(':effective_date', $_contract['effective_date'], PDO::PARAM_INT);
                    $stmt1->bindParam(':pcode', $_contract['pcode'], PDO::PARAM_STR);
                    $stmt1->bindParam(':pname', $_contract['pname'], PDO::PARAM_STR);
                    $stmt1->bindParam(':type_project', $_contract['type_project'], PDO::PARAM_INT);
                    $stmt1->bindParam(':agency_code', $_contract['agency_code'], PDO::PARAM_STR);
                    $stmt1->bindParam(':agency_address', $_contract['agency_address'], PDO::PARAM_STR);
                    $stmt1->bindParam(':agency_name', $_contract['agency_name'], PDO::PARAM_STR);
                    $stmt1->bindParam(':agency_phone', $_contract['agency_phone'], PDO::PARAM_STR);
                    $stmt1->bindParam(':investor_code', $_contract['investor_code'], PDO::PARAM_STR);
                    $stmt1->bindParam(':investor_name', $_contract['investor_name'], PDO::PARAM_STR);
                    $stmt1->bindParam(':investor_phone', $_contract['investor_phone'], PDO::PARAM_STR);
                    $stmt1->bindParam(':investor_address', $_contract['investor_address'], PDO::PARAM_STR);
                    $stmt1->bindParam(':capital_ratio', $_contract['capital_ratio'], PDO::PARAM_INT);
                    $stmt1->bindParam(':capital', $_contract['capital'], PDO::PARAM_INT);
                    $stmt1->bindParam(':role', $_contract['role'], PDO::PARAM_INT);
                    $stmt1->bindParam(':field_type', $_contract['field_type'], PDO::PARAM_STR);
                    $stmt1->bindParam(':project_objectives', $_contract['project_objectives'], PDO::PARAM_STR);
                    $stmt1->bindParam(':project_scale', $_contract['project_scale'], PDO::PARAM_STR);
                    $stmt1->bindParam(':project_address', $_contract['project_address'], PDO::PARAM_STR);
                    $stmt1->bindParam(':land_area', $_contract['land_area'], PDO::PARAM_STR);
                    $stmt1->bindParam(':scheduled_info', $_contract['scheduled_info'], PDO::PARAM_STR);
                    $stmt1->bindParam(':project_total_fund', $_contract['project_total_fund'], PDO::PARAM_INT);
                    $stmt1->bindParam(':land_use_fees', $_contract['land_use_fees'], PDO::PARAM_STR);
                    $stmt1->bindParam(':plan_negotiating', $_contract['plan_negotiating'], PDO::PARAM_STR);
                    $stmt1->bindParam(':compensation_value', $_contract['compensation_value'], PDO::PARAM_STR);
                    $stmt1->bindParam(':state_budget', $_contract['state_budget'], PDO::PARAM_STR);
                    $stmt1->bindParam(':progres_project', $_contract['progres_project'], PDO::PARAM_STR);
                    $stmt1->bindParam(':other_info', $_contract['other_info'], PDO::PARAM_STR);
                    $stmt1->bindValue(':addtime', $_contract['addtime'], PDO::PARAM_INT);
                    $stmt1->execute();
                    if ($update_data_hd == 0) {
                        $id_hd = $db->lastInsertId();
                        if ($id_hd > 0) {
                            echo("THÊM HỢP ĐỒNG - ID: " . $id_hd . "- OK<br/>\n");
                        }
                    } else {
                        $row_hd_new = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_bidding_contract WHERE id = ' . $id_hd)->fetch();
                        $status_insert_hd = insert_log_crawls($id_hd, 'CONTRACT', $row_hd_old, $row_hd_new);
                        if ($status_insert_hd > 0) {
                            echo("LOG: CONTRACT - ID: " . $id_hd . "- OK<br/>\n");
                        }
                    }
                }
            }
        }
    } else {
        $dbcr->query("UPDATE nv23_lcndt_tbmt_url SET url_run_kq=" . NV_CURRENTTIME . ", uniqid_kq = ''  WHERE id = " . $_tbmdt['id']);
    }
} else {
    echo "No data";
}

function geturlpagecontract($_row, $reload = 1)
{
    global $dbcr, $num_run;
    ++$num_run;
    $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-investor-selection-v2/services/econsign/detail-contract';
    $body = '{"id":"' . $_row['contract_id'] . '"}';
    $referer = 'https://muasamcong.mpi.gov.vn/web/guest/investor-selection?p_p_id=egpportalinvestorselectionv2_WAR_egpportalinvestorselectionv2&p_p_lifecycle=0&p_p_state=normal&p_p_mode=view&_egpportalinvestorselectionv2_WAR_egpportalinvestorselectionv2_render=detail&id=' . $_row['pid'] . '&type=' . $_row['ptype'] . '&no=' . $_row['pno'];
    $arr_agent = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:104.0) Gecko/20100101 Firefox/104.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/105.0.1343.42',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 9.0; SAMSUNG SM-F900U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.77 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ];

    srand((float) microtime() * 10000000);
    $rand = array_rand($arr_agent);
    $agent = $arr_agent[$rand];
    $_proxy = $dbcr->query("SELECT * FROM `nv4_proxy` WHERE `status`=2 ORDER BY `lasttime` ASC LIMIT 1")->fetch();
    $ch = curl_init();

    if (isset($_proxy['proxy'])) {
        $dbcr->query("UPDATE `nv4_proxy` SET `lasttime` = " . time() . ", number_run=number_run+1 WHERE `id` = " . $_proxy['id']);
        echo $_proxy['proxy'] . "\n";
        curl_setopt($ch, CURLOPT_PROXY, $_proxy['proxy']);
        if (isset($_proxy['proxyuserpwd']) and !empty($_proxy['proxyuserpwd'])) {
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, $_proxy['proxyuserpwd']);
        }
        curl_setopt($ch, CURLOPT_PROXYTYPE, $_proxy['proxytype']);
        curl_setopt($ch, CURLOPT_PROXYPORT, $_proxy['proxyport']);
    }

    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
    curl_setopt($ch, CURLOPT_REFERER, $referer);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json'
    ));

    $json = curl_exec($ch);
    $info = curl_getinfo($ch);
    curl_close($ch);
    $data = json_decode($json, true);
    if (isset($data['ctMainInvestorM'])) {
        return $data['ctMainInvestorM'];
    } elseif ($reload and $num_run < 5) {
        return geturlpagecontract($_row, 1);
    } elseif ($reload) {
        return geturlpagecontract($_row, 0);
    }
    return [];
}

function geturlpage($resultId, $reload = 1)
{
    global $dbcr, $num_run;
    ++$num_run;
    $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-investor-selection-v2/services/ebidsdd/bidr-social-result/get-detail';
    $body = '{"id":"' . $resultId . '"}';

    //$referer = 'https://muasamcong.mpi.gov.vn/en/web/guest/investor-selection?p_p_id=egpportalinvestorselectionv2_WAR_egpportalinvestorselectionv2&p_p_lifecycle=0&p_p_state=normal&p_p_mode=view&_egpportalinvestorselectionv2_WAR_egpportalinvestorselectionv2_render=detail&id=' . $_kqlcndt['id_msc'] . '&type=' . $_type . '&no=' . $_kqlcndt['notifyno'];
    $arr_agent = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:104.0) Gecko/20100101 Firefox/104.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/105.0.1343.42',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 9.0; SAMSUNG SM-F900U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.77 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36'
    ];

    srand((float) microtime() * 10000000);
    $rand = array_rand($arr_agent);
    $agent = $arr_agent[$rand];
    $_proxy = $dbcr->query("SELECT * FROM `nv4_proxy` WHERE `status`=2 ORDER BY `lasttime` ASC LIMIT 1")->fetch();
    $ch = curl_init();

    if (isset($_proxy['proxy'])) {
        $dbcr->query("UPDATE `nv4_proxy` SET `lasttime` = " . time() . ", number_run=number_run+1 WHERE `id` = " . $_proxy['id']);
        echo $_proxy['proxy'] . "\n";
        curl_setopt($ch, CURLOPT_PROXY, $_proxy['proxy']);
        if (isset($_proxy['proxyuserpwd']) and !empty($_proxy['proxyuserpwd'])) {
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, $_proxy['proxyuserpwd']);
        }
        curl_setopt($ch, CURLOPT_PROXYTYPE, $_proxy['proxytype']);
        curl_setopt($ch, CURLOPT_PROXYPORT, $_proxy['proxyport']);
    }

    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
    //curl_setopt($ch, CURLOPT_REFERER, $referer);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json'
    ));

    $json = curl_exec($ch);
    curl_close($ch);
    $data = json_decode($json, true);
    if (isset($data['notifyNo'])) {
        return $data;
    } elseif ($reload and $num_run < 5) {
        return geturlpage($resultId, 1);
    } elseif ($reload) {
        return geturlpage($resultId, 0);
    }
    return [];
}

function gethtml($url, $reload = 1)
{
    global $dbcr;
    srand((float) microtime() * 10000000);
    $_proxy = $dbcr->query("SELECT * FROM `nv4_proxy` WHERE `status`=2 ORDER BY `lasttime` ASC LIMIT 1")->fetch();
    $ch = curl_init();
    if (isset($_proxy['proxy'])) {
        $dbcr->query("UPDATE `nv4_proxy` SET `lasttime` = " . time() . ", number_run=number_run+1 WHERE `id` = " . $_proxy['id']);
        echo $_proxy['proxy'] . "\n";
        curl_setopt($ch, CURLOPT_PROXY, $_proxy['proxy']);
        if (isset($_proxy['proxyuserpwd']) and !empty($_proxy['proxyuserpwd'])) {
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, $_proxy['proxyuserpwd']);
        }
        curl_setopt($ch, CURLOPT_PROXYTYPE, $_proxy['proxytype']);
        curl_setopt($ch, CURLOPT_PROXYPORT, $_proxy['proxyport']);
    }

    $agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36';
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);

    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    $html = curl_exec($ch);
    curl_close($ch);
    
    if (empty($html)) {
        $errorMessage = "Không có nội dung HTML";
    } else {
        // Đọc dữ liệu HTML
        $dom = new DOMDocument();
        libxml_use_internal_errors(true);
        if (!$dom->loadHTML($html)) {
            $errorMessage = "Không đọc được trang để lấy HTML";
        }
    }
    $resultId = '';
    if (isset($errorMessage)) {
        echo $errorMessage . "\n";
        if ($reload) {
            echo "Đang thử lại lần nữa\n";
            sleep(1);
            return gethtml(0);
        }
    } else {
        if (preg_match('/responseProcessXHH:\s*({.*?"response":\s*{.*?}})/s', $html, $matches)) {
            $response = json_decode($matches[1], true);
            $resultId = $response['response']['resultId'];
        }
    }
    return $resultId;
}
