<?php
/* Thêm tên file vào CSDL */
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME))));
require NV_ROOTDIR . '/mainfile.php';

$folder_business = NV_CONSOLE_DIR . '/business/';
foreach(glob($folder_business . '*') as $file)
{
    if(!is_dir($file)) {
        $file_name = basename($file);
        echo $file_name."\n";
        // Kiểm tra xem đã có trong bảng nv4_vi_businesslistings_old_file chưa
        if (!empty($file_name)) {
            $id_file = $db->query('SELECT id FROM ' . BUSINESS_PREFIX_GLOBAL . '_old_file WHERE file_name = ' . $db->quote($file_name))->fetchColumn();

            if (empty($id_file)) {
                // insert vào bảng nv4_vi_businesslistings_old_file
                $stmt = $db->prepare('INSERT INTO ' . BUSINESS_PREFIX_GLOBAL . '_old_file (file_name) VALUES (:file_name)');
                $stmt->bindParam(':file_name', $file_name, PDO::PARAM_STR);
                $exc = $stmt->execute();
                if ($exc) {
                    $last_id_file = $db->lastInsertId();
                    echo '- Insert file into ' . BUSINESS_PREFIX_GLOBAL . '_old_file success: id: ' . $last_id_file . ', file_name: ' . $file_name . "\n";
                } else {
                    echo '-- Lỗi insert: file_name: ' . $file_name;
                }
            } else {
                echo '--- id file đã tồn tại trên hệ thống: id ' . $id_file . ', file_name: ' . $file_name . "\n";;
            }
        }

    }
}
die('Finish' . "\n");
