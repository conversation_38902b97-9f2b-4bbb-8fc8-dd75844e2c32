#!/bin/sh

DIR_PATH=$(dirname ${BASH_SOURCE[0]})

if [ -f "$DIR_PATH/logs/every_hour.txt" ]; then
    if [ -n "$(find $DIR_PATH/logs/ -type f -name every_hour.txt -mmin +120)" ]; then
        # 120 phút mà còn file này thì xóa để chạy đè tiến trình
        rm -f $DIR_PATH/logs/every_hour.txt
    else
        echo "Đang bận";
        exit
    fi
fi

echo "$(date +%Y-%m-%d-%T)" > "$DIR_PATH/logs/every_hour.txt"

START=$(date +%s)
crawls_logs="$DIR_PATH/logs/every_hour_$(date +%Y-%m-%d).txt"
echo "$(date +%Y-%m-%d-%T)" >> $crawls_logs

echo "----- Danh sách tổ chức, cá nhân vi phạm ------"
basetime=$(date +%s%N)
php $DIR_PATH/vipham_url.php
php $DIR_PATH/vipham_detail.php
echo "$(echo "scale=4;($(date +%s%N) - ${basetime})/(1*10^09)" | bc) seconds vipham_detail" >> $crawls_logs

echo "----- Danh sách Bên mời thấu, lấy các trang đầu ------"
basetime=$(date +%s%N)
php $DIR_PATH/benmoithau_url.php
echo "$(echo "scale=4;($(date +%s%N) - ${basetime})/(1*10^09)" | bc) seconds benmoithau_url" >> $crawls_logs

basetime=$(date +%s%N)
for i in {1..10}
do
	sleep 1
	ruledetail=$(php $DIR_PATH/benmoithau_detail.php)
	echo $ruledetail
	if [[ "$ruledetail" == *"No Data"* ]]; then
		break
	fi
	ruledetail=$(php $DIR_PATH/benmoithau_save.php)
	echo "$(date +%Y-%m-%d-%T)" >> "$DIR_PATH/logs/every_hour.txt"
done
echo "$(echo "scale=4;($(date +%s%N) - ${basetime})/(1*10^09)" | bc) seconds benmoithau_detail" >> $crawls_logs

echo "----- Danh sách nhà thầu,  lấy các trang đầu ------"
basetime=$(date +%s%N)
php $DIR_PATH/nhathau_url.php
echo "$(echo "scale=4;($(date +%s%N) - ${basetime})/(1*10^09)" | bc) seconds nhathau_url" >> $crawls_logs

echo "----- Nhà Thầu detail------"
basetime=$(date +%s%N)
php $DIR_PATH/business_auto_recrawl.php

for i in {1..10}
do
	ruledetail=$(php $DIR_PATH/nhathau_detail.php)
	echo $ruledetail
	if [[ "$ruledetail" == *"No Data"* ]]; then
		break
	fi
	php $DIR_PATH/nhathau_to_business.php
	echo "$(date +%Y-%m-%d-%T)" >> "$DIR_PATH/logs/every_hour.txt"
	sleep 1
done
echo "$(echo "scale=4;($(date +%s%N) - ${basetime})/(1*10^09)" | bc) seconds nhathau_detail" >> $crawls_logs

echo "---- danh mục công bố dự án MSC mới  ----";
basetime=$(date +%s%N)
php $DIR_PATH/lcndt_duan_congbo_url.php
echo "$(echo "scale=4;($(date +%s%N) - ${basetime})/(1*10^09)" | bc) seconds lcndt_duan_congbo_url" >> $crawls_logs

echo "----- Lựa chọn nhà đầu tư ------"
basetime=$(date +%s%N)
php $DIR_PATH/lcndt_khlcndt_url.php
echo "$(echo "scale=4;($(date +%s%N) - ${basetime})/(1*10^09)" | bc) seconds lcndt_khlcndt_url" >> $crawls_logs

basetime=$(date +%s%N)
php $DIR_PATH/lcndt_khlcndt_detail.php
echo "$(echo "scale=4;($(date +%s%N) - ${basetime})/(1*10^09)" | bc) seconds lcndt_khlcndt_detail" >> $crawls_logs

basetime=$(date +%s%N)
php $DIR_PATH/lcndt_tbmt_url.php
echo "$(echo "scale=4;($(date +%s%N) - ${basetime})/(1*10^09)" | bc) seconds lcndt_tbmt_url" >> $crawls_logs

basetime=$(date +%s%N)
php $DIR_PATH/lcndt_tbmt_detail.php
echo "$(echo "scale=4;($(date +%s%N) - ${basetime})/(1*10^09)" | bc) seconds lcndt_tbmt_detail" >> $crawls_logs

echo "----- Bóc tin yêu cầu làm rõ HSMT------"
basetime=$(date +%s%N)
php $DIR_PATH/tbmt_url.php --mode=clarify
echo "$(echo "scale=4;($(date +%s%N) - ${basetime})/(1*10^09)" | bc) seconds tbmt_url.php --mode=clarify" >> $crawls_logs

rm -f "$DIR_PATH/logs/every_hour.txt"
echo "Total time : $(($(date +%s) - $START))" >> $crawls_logs
echo "--------------------END---------------------------" >> $crawls_logs
