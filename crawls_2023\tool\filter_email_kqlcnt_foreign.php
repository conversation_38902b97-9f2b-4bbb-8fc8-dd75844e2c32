<?php
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
require NV_ROOTDIR . '/functions/save_email_kqlcnt.php';

set_time_limit(0);

$file_run = NV_ROOTDIR . '/crawls_2023/filter_email_kqlcnt_foreign_run.txt';
$id = 9999999999;
$file_id = NV_ROOTDIR . '/crawls_2023/filter_email_kqlcnt_foreign_id.txt';
if (file_exists($file_id)) {
    $id = file_get_contents($file_id);
    $id = intval($id);
}

$limit = 50;
try {
    $sql = 'SELECT * FROM nv23_crawls_kqlcnt WHERE id < ' . $id . ' ORDER BY id DESC LIMIT ' . $limit;
    $query = $dbcr->query($sql);
    $count = $query->rowCount();
    if ($count > 0) {
        while ($row = $query->fetch()) {
            $arr_detail = json_decode($row['detail'], true);
            $detail = isset($arr_detail['bideContractorInputResultDTO']) ? $arr_detail['bideContractorInputResultDTO'] : [];

            $save_status = 0;
            if (!empty($detail)) {
                $save_status = save_email_kqlcnt($detail, 0, 1);
                echo $save_status . '|';
            }
            if ($save_status == 500) {
                file_put_contents($file_run, 0, LOCK_EX);
                exit();
            } else {
                file_put_contents($file_run, $count, LOCK_EX);
                file_put_contents($file_id, $row['id'], LOCK_EX);
            }
        }
    } else {
        file_put_contents($file_run, 0, LOCK_EX);
    }
} catch (Exception $e) {
    file_put_contents($file_run, 0, LOCK_EX);
    print_r($e);
    die($e->getMessage());
}
echo 'Update trong: ' . number_format((microtime(true) - NV_START_TIME), 3, '.', '');
die;
