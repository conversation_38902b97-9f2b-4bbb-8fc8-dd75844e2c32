<?php

/**
 * @Project Crawls.DauThau.Net
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2025 VINADES.,JSC. All rights reserved
 * Tool cập nhật địa chỉ, id tỉnh, quận, xã của YCBG từ bảng nv4_bidding_solicitor
 */

// Xac dinh thu muc goc cua site
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

$filename = NV_ROOTDIR . '/tools_2025/update_address_ycbg_' . NV_LANG_DATA . '.log';

if (file_exists($filename)) {
    $last_id = file_get_contents($filename);
    $last_id = intval($last_id);
} else {
    $last_id = 0;
}

echo ("=====Bắt đầu chạy===== \n");
echo "last id ycbg: " . number_format($last_id) . "\n";

$query = $db->query("SELECT id, investor_code FROM " . NV_PREFIXLANG . "_bidding_ycbg WHERE id > " . $last_id . " ORDER BY id ASC LIMIT 100");
if ($query->rowCount() == 0) {
    echo "Đã chạy hết!!\n";
    exit(1);
}

$count = 0;
$array_address = [];
while ($row = $query->fetch()) {
   $array_address[$row['id']] = $row['investor_code'];
   $last_id = $row['id'];
}

if (!empty($array_address)) {
    $list_investor_code = [];
    foreach ($array_address as $investor_code) {
        $list_investor_code[] = $db->quote($investor_code);
    }
    $sql_solicitor = 'SELECT org_code, address, id_province, id_district, id_ward FROM ' . $config['prefix'] . '_bidding_solicitor WHERE org_code IN (' . implode(',', $list_investor_code) . ')';
    $query_solicitor = $db->query($sql_solicitor);
    while ($row = $query_solicitor->fetch()) {
        foreach ($array_address as $ycbg_id => $investor_code) {
            if ($investor_code == $row['org_code']) {
                $db->query("UPDATE " . NV_PREFIXLANG . "_bidding_ycbg SET address_ycbg = " . $db->quote($row['address']) . ", id_province = " . intval($row['id_province']) . ", id_district = " . intval($row['id_district']) . ", id_ward = " . intval($row['id_ward']) . " WHERE id = " . $ycbg_id);
                echo "Đã cập nhật YCBG id = " . $ycbg_id . " với investor_code = " . $row['org_code'] . "\n";
                $count++;
            }
        }
    }
}

file_put_contents($filename, $last_id);
echo "Đã cập nhật " . $count . " bản ghi\n";
echo "\nThời gian thực hiện = " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";
echo "Thực hiện xong\n";
