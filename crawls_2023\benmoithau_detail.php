<?php

define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

$i = 0;
$uniqid = uniqid('', true);
$dbcr->query("UPDATE nv22_benmoithau SET url_run='-" . NV_CURRENTTIME . "', count_url=count_url+1, uniqid = " . $dbcr->quote($uniqid) . " WHERE uniqid = '' and url_run=0 ORDER BY `id` ASC LIMIT 5");
$query = $dbcr->query("SELECT * FROM `nv22_benmoithau` WHERE uniqid = " . $dbcr->quote($uniqid));
while ($row = $query->fetch()) {
    ++$i;
    echo $row['id'] . " ";
    $referer = 'https://muasamcong.mpi.gov.vn/web/guest/bid-solicitor-approval?p_p_id=egpportalbidsolicitorapproved_WAR_egpportalbidsolicitorapproved&p_p_lifecycle=0&p_p_state=normal&p_p_mode=view&_egpportalbidsolicitorapproved_WAR_egpportalbidsolicitorapproved_render=detail&tendererCode=' . $row['orgcode'] . '&startPendingDate=' . $row['startpendingdate'] . '&status=' . $row['status'] . '&effectRoleDate=' . $row['effroledate'];
    $data = geturlpage($row['orgcode'], $referer, 1);
    if (isset($data['orgInfo'])) {
        echo $data['orgInfo']['orgCode']."\n";
        $dbcr->exec("UPDATE nv22_benmoithau SET dauthau_info=0, url_run=" . time() . ", uniqid='', detail2=" . $dbcr->quote(json_encode($data['orgInfo'], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT)) . " WHERE id=" . $row['id']);
    }
}
$query->closeCursor();

if (empty($i)) {
    die('No Data');
}

function geturlpage($orgcode, $referer, $reload = 1)
{
    global $dbcr, $num_run;
    ++$num_run;

    $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-bid-solicitor-approved/services/um/org/get-detail-info';
    $body = '{
        "orgCode": "' . $orgcode . '"
      }';

    $arr_agent = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:104.0) Gecko/20100101 Firefox/104.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/105.0.1343.42',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 9.0; SAMSUNG SM-F900U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.77 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ];
    srand((float) microtime() * 10000000);
    $rand = array_rand($arr_agent);
    $agent = $arr_agent[$rand];

    $_proxy = $dbcr->query("SELECT * FROM `nv4_proxy` WHERE `status`=2 ORDER BY `lasttime` ASC LIMIT 1")->fetch();
    $ch = curl_init();
    if (isset($_proxy['proxy'])) {
        $dbcr->query("UPDATE `nv4_proxy` SET `lasttime` = " . time() . ", number_run=number_run+1 WHERE `id` = " . $_proxy['id']);
        echo $_proxy['proxy'] . "\n";
        curl_setopt($ch, CURLOPT_PROXY, $_proxy['proxy']);
        if (isset($_proxy['proxyuserpwd']) and !empty($_proxy['proxyuserpwd'])) {
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, $_proxy['proxyuserpwd']);
        }
        curl_setopt($ch, CURLOPT_PROXYTYPE, $_proxy['proxytype']);
        curl_setopt($ch, CURLOPT_PROXYPORT, $_proxy['proxyport']);
    }
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
    curl_setopt($ch, CURLOPT_REFERER, $referer);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json'
    ));

    $json = curl_exec($ch);
    curl_close($ch);

    $data = json_decode($json, true);
    if (isset($data['orgInfo'])) {
        return $data;
    } elseif ($reload and $num_run < 5) {
        return geturlpage($orgcode, $referer, 1);
    } elseif ($reload) {
        return geturlpage($orgcode, $referer, 0);
    }
    return [];
}
