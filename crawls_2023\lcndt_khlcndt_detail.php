<?php
/**
 * @Project Crawls.DauThau.Info
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2022 VINADES.,JSC. All rights reserved
 * @Code Ngọc <PERSON> (<EMAIL>)
 * Tool bóc danh sách: Thông báo mời đầu tư
 * https://vinades.org/dauthau/dauthau.info/-/issues/1847
 */

define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME))));
require NV_ROOTDIR . '/mainfile.php';

$id = (int) $request_mode->get('id', 0); // php kqlcnt_detail.php --id=285003
if ($id > 0) {
    $_khlcndt = $dbcr->query('SELECT * FROM `nv23_lcndt_khlcndt_url` WHERE id = ' . $id)->fetch();
} else {
    $uniqid = uniqid('', true);
    $dbcr->query("UPDATE nv23_lcndt_khlcndt_url SET url_run='-" . NV_CURRENTTIME . "', count_url=count_url+1, uniqid='" . $uniqid . "'  WHERE `url_run`=0 AND uniqid='' ORDER BY `id` DESC LIMIT 1");
    $_khlcndt = $dbcr->query("SELECT * FROM `nv23_lcndt_khlcndt_url` WHERE `uniqid`='" . $uniqid . "' ORDER BY `id` DESC LIMIT 1")->fetch();
}

if (!empty($_khlcndt)) {
    echo ("id KHLCNDT: " . $_khlcndt['id'] . "\n");
    $num_run = 0;
    $data = geturlpage($_khlcndt, 1);
    if (isset($data['planNo'])) {
        $dbcr->query("UPDATE nv23_lcndt_khlcndt_url SET url_run=" . NV_CURRENTTIME . ", detail2	=" . $dbcr->quote(json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT)) . ", dauthau_info='-1' WHERE id=" . $_khlcndt['id']);
        echo "OK\n";
        getdata($_khlcndt, $data);
    } else {
        if (empty($info['http_code'])) {
            $info['http_code'] = NV_CURRENTTIME;
        }
        $dbcr->query("UPDATE nv23_lcndt_khlcndt_url SET url_run='-" . $info['http_code'] . "', detail2	=" . $dbcr->quote(json_encode($info, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT)) . " WHERE id=" . $_khlcndt['id']);
        echo "NO: planNo\n";
        print_r($info);
    }
    echo "\nThoi gian thuc hien = " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";
} else {
    echo "No Data\n";
}


function getdata($_khlcndt, $detail)
{
    global $db, $dbcr, $array_name_staff;

    $arr_khlcndt = array (
        'es-bidp-plan-project-invest-ppp-p' => 'Dự án PPP',
        'es-bidp-plan-project-invest-sdd-p' => 'Dự án đầu tư có sử dụng đất',
        'es-bidp-plan-project-social-p' => 'Dự án xã hội hóa, chuyên ngành',
        'es-bidp-plan-project-out-law-p' => 'Dự án ngoài phạm vi điều chỉnh'
    );

    $date = [
        'D' => 'Ngày',
        'M' => 'Tháng',
        'Q' => 'Quý',
        'Y' => 'Năm'
    ];

    $method = [
        '1gd1ths' => '1 giai đoạn 1 túi hồ sơ',
        '1gd2ths' => '1 giai đoạn 2 túi hồ sơ'
    ];

    $row['ben_moi_thau'] = isset($detail['procuringEntityName']) ? $detail['procuringEntityName'] : '';
    $org_code_bmt = isset($detail['procuringEntityCode']) ? $detail['procuringEntityCode'] : '';
    $row['solicitor_id'] = get_solicitor_id($row['ben_moi_thau'], $org_code_bmt);
    $row['so_khlcndt'] = $detail['planNo'] . '-' . $detail['planVersion'];
    $row['loai_thong_bao'] = '';
    $row['hinh_thuc_thong_bao'] = ($detail['status'] == '01') ? 'Đã đăng tải' : (($detail['status'] == '03') ? 'Đã hủy' : '');
    $row['ten_khlcndt'] = isset($detail['name']) ? $detail['name'] : '';
    $row['project_name'] = isset($detail['projectName']) ? $detail['projectName'] : '';
    $row['project_no'] = isset($detail['projectNo']) ? $detail['projectNo'] . '-' . $detail['projectVersion'] : '';
    $row['dia_diem_thuc_hien_du_an'] = $_khlcndt['location'];
    $row['content'] = $row['so_khlcndt'] . ' あ ' . $row['ten_khlcndt'] . ' あ ' . $row['ben_moi_thau'];
    $row['content'] = nv_compound_unicode($row['content']);
    $row['ten_khlcndt'] = trim_space($row['ten_khlcndt']);
    $row['loai_du_an'] = isset($arr_khlcndt[$_khlcndt['type_project']]) ? $arr_khlcndt[$_khlcndt['type_project']] : '';
    $row['total_cost_num'] = isset($detail['feeTotal']) ? $detail['feeTotal'] : 0;
    $row['total_cost'] = ($row['total_cost_num'] > 0 ) ? number_format($row['total_cost_num'], 0, '', '.') . ' VNĐ' : '';
    $row['project_plan_id'] = 0;
    $row['thong_bao_lien_quan'] = '';
    $row['hinh_thuc_lua_chon_nha_dau_tu'] = convertMethodLCNT($detail['bidForm']);
    $row['phuong_thuc_lua_chon_nha_dau_tu'] = isset($detail['investorMethod']) ? $method[$detail['investorMethod']] : '';
    $row['thoi_gian_thuc_hien_hop_dong'] = isset($detail['contractPeriod']) ? $detail['contractPeriod'] . ' ' . $date[$detail['contractPeriodUnit']] : '';
    $row['thoi_gian_bat_dau_to_chuc_lcndt'] = isset($detail['startQuarter']) ? $date[$detail['startUnit']] . ' ' . $detail['startQuarter'] . '/' . $detail['startYear'] : '';
    $quyet_dinh = [];
    if ($_khlcndt['type_project'] == 'es-bidp-plan-project-invest-sdd-p') {
        $content = isset($detail['content']) ? json_decode($detail['content'], true) : '';
        if (!empty($content)) {
            $quyet_dinh = [
                'decisionNo' => isset($content['decisionNo']) ? $content['decisionNo'] : '',
                'agencyName' => isset($detail['agencyName']) ? $detail['agencyName'] : '',
                'decisionDate' => isset($content['decisionDate']) ? strtotime($content['decisionDate']) : 0,
                'decisionFileName' => isset($content['decisionFileName']) ? $content['decisionFileName'] : '',
                'decisionFileId' => isset($content['decisionFileId']) ? $content['decisionFileId'] : '',
                'relatedFileId' => isset($content['relatedFileId']) ? $content['relatedFileId'] : '',
                'relatedFileName' => isset($content['relatedFileName']) ? $content['relatedFileName'] : ''
            ];
        }
    } else if ($_khlcndt['type_project'] == 'es-bidp-plan-project-social-p') {
        $quyet_dinh = [
            'decisionNo' => isset($detail['decisionNo']) ? $detail['decisionNo'] : '',
            'agencyName' => isset($detail['agencyName']) ? $detail['agencyName'] : '',
            'decisionDate' => isset($detail['decisionDate']) ? strtotime($detail['decisionDate']) : 0,
            'decisionFileName' => isset($detail['decisionFileName']) ? $detail['decisionFileName'] : '',
            'decisionFileId' => isset($detail['decisionFileId']) ? $detail['decisionFileId'] : '',
            'relatedFileId' => isset($detail['relatedFileId']) ? $detail['relatedFileId'] : '',
            'relatedFileName' => isset($detail['relatedFileName']) ? $detail['relatedFileName'] : ''
        ];
    }

    foreach ($quyet_dinh as $key => $qd) {
        if ($qd == 'null' or $qd == 'N/A') {
            $quyet_dinh[$key] = '';
        }
    }
    $row['quyet_dinh_phe_duyet'] = json_encode($quyet_dinh, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    $row['is_domestic'] = !empty($detail['isDomestic']) ? $detail['isDomestic'] : 0;
    $row['is_internet'] = !empty($detail['isInternet']) ? $detail['isInternet'] : 0;
    $row['createdby'] = isset($detail['createdBy']) ? $detail['createdBy'] : '';
    $row['createddate'] = isset($detail['createdDate']) ? strtotime($detail['createdDate']) : 0;
    $row['don_vi_cong_bo'] = '';
    if (isset($detail['projectInvestSdd'])) {
        $proj = $detail['projectInvestSdd'];
        $row['don_vi_cong_bo'] = isset($proj['investorName']) ? $proj['investorName'] : '';
    }
    $row['chi_phi_boi_thuong'] = isset($detail['cpbthttdc']) ? $detail['cpbthttdc'] : 0;
    $row['chi_phi_boi_thuong_bang_chu'] = ($row['chi_phi_boi_thuong'] > 0 ) ? number_format($row['chi_phi_boi_thuong'], 0, '', '.') . ' VNĐ' : '';
    $row['loai_hop_dong'] = '';
    $row['tien_su_dung_dat'] = ($detail['tsdddk'] > 0 ) ? number_format($detail['tsdddk'], 0, '', '.') . ' VNĐ' : '';
    if ($_khlcndt['type_project'] == 'es-bidp-plan-project-invest-sdd-p') {
        $row['loai_hop_dong'] = 'Hợp đồng dự án đầu tư có sử dụng đất';
    } else if ($_khlcndt['type_project'] == 'es-bidp-plan-project-social-p') {
        $row['loai_hop_dong'] = 'Hợp đồng Dự án chuyên ngành/xã hội hóa';
    }
    $row['note'] = isset($detail['note']) ? $detail['note'] : '';
    $row['id_msc'] = isset($detail['id']) ? $detail['id'] : '';
    $row['addtime'] = isset($detail['publicDate']) ? strtotime($detail['publicDate']) : 0;
    $updatetime = $gettime = NV_CURRENTTIME;
    try {
        $db->beginTransaction();
        try {
            // Lấy Id của khlcndt đã có trong csdl
            $id_plan = 0;
            $id_plan = $db->query('SELECT id FROM ' . NV_PREFIXLANG . '_bidding_plans_project WHERE code=' . $db->quote($row['so_khlcndt']))
                ->fetchColumn();
            $update_data = $id_plan > 0 ? 1 : 0;
            if ($update_data == 0) {
                $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_bidding_plans_project
                            (project_plan_id, code, alias, id_msc, is_new_msc, project_id,createdby, createddate, don_vi_cong_bo, chi_phi_boi_thuong, chi_phi_boi_thuong_bang_chu, tien_su_dung_dat, loai_hop_dong, is_domestic, is_internet, note, type_notify, formly_notify, title, project_name, project_no, solicitor_id, investor, classify, total_cost, total_cost_num, addtime, gettime, updatetime, relate_plan, hinhthucluachon, phuongthuc, thoi_gian_thuc_hien, thoi_gian_bat_dau, quyet_dinh, elasticsearch, content, name_staff) VALUES
                            (:project_plan_id, :code, :alias, :id_msc, 1, :project_id, :createdby, :createddate, :don_vi_cong_bo, :chi_phi_boi_thuong, :chi_phi_boi_thuong_bang_chu, :tien_su_dung_dat, :loai_hop_dong, :is_domestic, :is_internet, :note, :type_notify, :formly_notify, :title, :project_name, :project_no, :solicitor_id, :investor, :classify, :total_cost, :total_cost_num, :addtime, :gettime, 0, :relate_plan, :hinhthucluachon, :phuongthuc, :thoi_gian_thuc_hien, :thoi_gian_bat_dau, :quyet_dinh, 0, :content, :name_staff)');
                $stmt->bindValue(':gettime', $gettime, PDO::PARAM_INT);
                $stmt->bindValue(':alias', nv_clean_alias($row['ten_khlcndt']), PDO::PARAM_STR);
            } else {
                $row_old = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_bidding_plans_project WHERE id = ' . $id_plan)->fetch();
                $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_bidding_plans_project SET project_plan_id=:project_plan_id, code=:code, id_msc=:id_msc, project_id=:project_id, createdby=:createdby, createddate=:createddate, don_vi_cong_bo=:don_vi_cong_bo, chi_phi_boi_thuong=:chi_phi_boi_thuong, chi_phi_boi_thuong_bang_chu=:chi_phi_boi_thuong_bang_chu, tien_su_dung_dat=:tien_su_dung_dat,loai_hop_dong=:loai_hop_dong, is_domestic=:is_domestic, is_internet=:is_internet, note=:note, type_notify=:type_notify, formly_notify=:formly_notify, title=:title, project_name=:project_name,project_no=:project_no, solicitor_id=:solicitor_id, investor=:investor, classify=:classify, total_cost=:total_cost, total_cost_num=:total_cost_num, addtime=:addtime, updatetime=:updatetime, relate_plan=:relate_plan, hinhthucluachon=:hinhthucluachon, phuongthuc=:phuongthuc, thoi_gian_thuc_hien=:thoi_gian_thuc_hien, thoi_gian_bat_dau=:thoi_gian_bat_dau, quyet_dinh=:quyet_dinh, elasticsearch=0, content=:content, name_staff=:name_staff, translator=0 WHERE id=' . $id_plan);
                $stmt->bindValue(':updatetime', $updatetime, PDO::PARAM_INT);
            }

            $row['project_id'] = 0;
            if (isset($row_old['project_id']) and $row_old['project_id'] > 0) {
                $row['project_id'] = $row_old['project_id'];
            }
            if ($row['project_id'] == 0) {
                // xác định project_id
                if (!empty($row['project_no'])) {
                    $project = $db->query('SELECT id FROM ' . NV_PREFIXLANG . '_bidding_project_proposal WHERE code = ' . $db->quote($row['project_no']))
                        ->fetch();
                    if (!empty($project)) {
                        $row['project_id'] = $project['id'];
                    }
                }
            }
            $stmt->bindParam(':project_plan_id', $row['project_plan_id'], PDO::PARAM_STR);
            $stmt->bindParam(':code', $row['so_khlcndt'], PDO::PARAM_STR);
            $stmt->bindParam(':id_msc', $row['id_msc'], PDO::PARAM_STR);
            $stmt->bindParam(':project_id', $row['project_id'], PDO::PARAM_STR);
            $stmt->bindParam(':createdby', $row['createdby'], PDO::PARAM_STR);
            $stmt->bindParam(':createddate', $row['createddate'], PDO::PARAM_INT);
            $stmt->bindParam(':don_vi_cong_bo', $row['don_vi_cong_bo'], PDO::PARAM_STR);
            $stmt->bindParam(':chi_phi_boi_thuong', $row['chi_phi_boi_thuong'], PDO::PARAM_INT);
            $stmt->bindParam(':chi_phi_boi_thuong_bang_chu', $row['chi_phi_boi_thuong_bang_chu'], PDO::PARAM_STR);
            $stmt->bindParam(':tien_su_dung_dat', $row['tien_su_dung_dat'], PDO::PARAM_STR);
            $stmt->bindParam(':loai_hop_dong', $row['loai_hop_dong'], PDO::PARAM_STR);
            $stmt->bindParam(':is_domestic', $row['is_domestic'], PDO::PARAM_INT);
            $stmt->bindParam(':is_internet', $row['is_internet'], PDO::PARAM_INT);
            $stmt->bindParam(':note', $row['note'], PDO::PARAM_STR);
            $stmt->bindParam(':type_notify', $row['loai_thong_bao'], PDO::PARAM_STR);
            $stmt->bindParam(':formly_notify', $row['hinh_thuc_thong_bao'], PDO::PARAM_STR);
            $stmt->bindParam(':title', $row['ten_khlcndt'], PDO::PARAM_STR);
            $stmt->bindParam(':project_name', $row['project_name'], PDO::PARAM_STR);
            $stmt->bindParam(':project_no', $row['project_no'], PDO::PARAM_STR);
            $stmt->bindParam(':solicitor_id', $row['solicitor_id'], PDO::PARAM_INT);
            $stmt->bindParam(':investor', $row['ben_moi_thau'], PDO::PARAM_STR);
            $stmt->bindParam(':classify', $row['loai_du_an'], PDO::PARAM_STR);
            $stmt->bindParam(':total_cost', $row['total_cost'], PDO::PARAM_STR);
            $stmt->bindParam(':total_cost_num', $row['total_cost_num'], PDO::PARAM_INT);
            $stmt->bindValue(':addtime', $row['addtime'], PDO::PARAM_INT);
            $stmt->bindParam(':relate_plan', $row['thong_bao_lien_quan'], PDO::PARAM_STR);
            $stmt->bindParam(':hinhthucluachon', $row['hinh_thuc_lua_chon_nha_dau_tu'], PDO::PARAM_STR);
            $stmt->bindParam(':phuongthuc', $row['phuong_thuc_lua_chon_nha_dau_tu'], PDO::PARAM_STR);
            $stmt->bindParam(':thoi_gian_thuc_hien', $row['thoi_gian_thuc_hien_hop_dong'], PDO::PARAM_STR);
            $stmt->bindParam(':thoi_gian_bat_dau', $row['thoi_gian_bat_dau_to_chuc_lcndt'], PDO::PARAM_STR);
            $stmt->bindParam(':quyet_dinh', $row['quyet_dinh_phe_duyet'], PDO::PARAM_STR);
            $stmt->bindParam(':content', $row['content'], PDO::PARAM_STR);
            $name_staff = $array_name_staff[array_rand($array_name_staff)];
            $stmt->bindParam(':name_staff', $name_staff, PDO::PARAM_STR);
            $exc = $stmt->execute();
            if ($update_data == 0) {
                $id_plan = $db->lastInsertId();
            } else {
                $row_new = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_bidding_plans_project WHERE id = ' . $id_plan)->fetch();
                $status_insert = insert_log_crawls($id_plan, 'KHLCDT', $row_old, $row_new);
                if ($status_insert > 0) {
                    echo("LOG: KHLCDT - ID: " . $id_plan . "- OK<br/>\n");
                }
            }

            $dbcr->query('UPDATE nv23_lcndt_khlcndt_url SET dauthau_info=1 WHERE id=' . intval($_khlcndt['id']));
            echo "Cập nhật xong = " . $_khlcndt['id'];
            echo "\n";
            $db->commit();
        } catch (PDOException $e) {
            $db->rollBack();
            print_r($e);
            $dbcr->query("UPDATE nv23_lcndt_khlcndt_url SET dauthau_info='-4', crawls_info=" . $dbcr->quote($e) . " WHERE id=" . $_khlcndt['id']);
            return false;
        }
    } catch (PDOException $e) {
        print_r($e);
        return false;
    }
}


function geturlpage($_khlcndt, $reload = 1)
{
    global $dbcr, $num_run;
    ++$num_run;
    if ($_khlcndt['type_project'] == 'es-bidp-plan-project-social-p') {
        $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-investor-selection-v2/services/ebidsdd/plan-project-invest-social/get-detail';
    } else if ($_khlcndt['type_project'] == 'es-bidp-plan-project-invest-sdd-p') {
        $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-investor-selection-v2/services/invest-sdd/project/detail';
    } else if ($_khlcndt['type_project'] == 'es-bidp-plan-project-out-law-p') {
        $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-investor-selection-v2/services/plan-project-invest-law/get-detail';
    }

    $body = '{"id":"' . $_khlcndt['id_msc'] . '"}';

    $referer = 'https://muasamcong.mpi.gov.vn/en/web/guest/investor-selection?p_p_id=egpportalinvestorselectionv2_WAR_egpportalinvestorselectionv2&p_p_lifecycle=0&p_p_state=normal&p_p_mode=view&_egpportalinvestorselectionv2_WAR_egpportalinvestorselectionv2_render=detail&id=' . $_khlcndt['id_msc'] . '&type=' . $_khlcndt['type_project'] . '&no=' . $_khlcndt['planno'];
    $arr_agent = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:104.0) Gecko/20100101 Firefox/104.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/105.0.1343.42',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 9.0; SAMSUNG SM-F900U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.77 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ];

    srand((float) microtime() * 10000000);
    $rand = array_rand($arr_agent);
    $agent = $arr_agent[$rand];
    $_proxy = $dbcr->query("SELECT * FROM `nv4_proxy` WHERE `status`=2 ORDER BY `lasttime` ASC LIMIT 1")->fetch();
    $ch = curl_init();

    if (isset($_proxy['proxy'])) {
        $dbcr->query("UPDATE `nv4_proxy` SET `lasttime` = " . time() . ", number_run=number_run+1 WHERE `id` = " . $_proxy['id']);
        echo $_proxy['proxy'] . "\n";
        curl_setopt($ch, CURLOPT_PROXY, $_proxy['proxy']);
        if (isset($_proxy['proxyuserpwd']) and !empty($_proxy['proxyuserpwd'])) {
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, $_proxy['proxyuserpwd']);
        }
        curl_setopt($ch, CURLOPT_PROXYTYPE, $_proxy['proxytype']);
        curl_setopt($ch, CURLOPT_PROXYPORT, $_proxy['proxyport']);
    }

    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
    curl_setopt($ch, CURLOPT_REFERER, $referer);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json'
    ));

    $json = curl_exec($ch);
    $info = curl_getinfo($ch);
    curl_close($ch);
    $data = json_decode($json, true);
    if (isset($data['planNo'])) {
        return $data;
    } elseif ($reload and $num_run < 5) {
        return geturlpage($_khlcndt, 1);
    } elseif ($reload) {
        return geturlpage($_khlcndt, 0);
    }
    return [];
}

function ho_so_moi_thau($html, $url)
{
    $ho_so_moi_thau = html_entity_decode($html);
    $_dom = new DOMDocument();
    libxml_use_internal_errors(true);
    if ($_dom->loadHTML($html)) {
        // gets all DIVs
        $divs = $_dom->getElementsByTagName('a');

        // traverse the object with all DIVs
        foreach ($divs as $div) {
            $_title = trim($div->nodeValue);
            $_href = trim($div->getAttribute('href'));
            $_onclick = trim($div->getAttribute('onclick'));

            $doc_outerHTML = new DOMDocument();
            $doc_outerHTML->appendChild($doc_outerHTML->importNode($div, true));
            $_outerHTML = html_entity_decode(trim($doc_outerHTML->saveHTML()));

            if ($_href == '#' and $_onclick != '') {
                $_href = $_onclick;
            }

            // javascript:toFile('96', 'Chuong I Chi dan nha thau_DTRR.pdf');
            if (preg_match('/javascript\:tofile\(([^\,]+)\,(.*)\)\;/i', $_href, $_m)) {
                $fileType = trim_v($_m[1]);
                $fileName = trim_v($_m[2]);
                $_href = 'http://muasamcong.mpi.gov.vn:8081/servlet/GT/EP_COV_GTQ953/?fileType=' . $fileType . '&fileName=' . $fileName;
                $get_ho_so = NV_CURRENTTIME;
                $ho_so_moi_thau = $ho_so_moi_thau;
            } else {
                if (preg_match('/javascript\:bangdulieu\((.*)\)/i', $_href, $_m)) {
                    $_e = explode(',', $_m[1]);
                    $_href = "http://muasamcong.mpi.gov.vn:8081/webentry/attack_file/bangdulieu?bid_no=" . trim_v($_e[0]) . "&bid_turn_no=" . trim_v($_e[1]) . "&bid_succmethod=" . trim_v($_e[2]);
                    $get_ho_so = NV_CURRENTTIME;
                } elseif (preg_match('/javascript\:tieuchuandanhgia\((.*)\)/i', $_href, $_m)) {
                    $_e = explode(',', $_m[1]);
                    $_href = "http://muasamcong.mpi.gov.vn:8081/webentry/attack_file/tc_danh_gia?bid_no=" . trim_v($_e[0]) . "&bid_turn_no=" . trim_v($_e[1]) . "&bid_succmethod=" . trim_v($_e[2]);
                    $get_ho_so = NV_CURRENTTIME;
                } elseif (preg_match('/javascript\:phamvicungcap\((.*)\)/i', $_href, $_m)) {
                    $_e = explode(',', $_m[1]);
                    $_href = "http://muasamcong.mpi.gov.vn:8081/webentry/attack_file/phamvicungcap?bid_no=" . trim_v($_e[0]) . "&bid_turn_no=" . trim_v($_e[1]) . "&bid_succmethod=" . trim_v($_e[2]);
                    $get_ho_so = NV_CURRENTTIME;
                } elseif (preg_match('/javascript\:bieumau\((.*)\)/i', $_href, $_m)) {
                    $_e = explode(',', $_m[1]);
                    $_href = "http://muasamcong.mpi.gov.vn:8081/webentry/thoa_thuan_ld?bid_no=" . trim_v($_e[0]) . "&bid_turn_no=" . trim_v($_e[1]) . "&bid_type=1&supplier_id=0&bidMethod=chct";
                    $get_ho_so = NV_CURRENTTIME;
                } elseif (preg_match('/javascript\:dieukiencuthe\((.*)\)/i', $_href, $_m)) {
                    $_e = explode(',', $_m[1]);
                    $_href = "http://muasamcong.mpi.gov.vn:8081/webentry/attack_file/condition_Contract?bid_no=" . trim_v($_e[0]) . "&bid_turn_no=" . trim_v($_e[1]);
                    $get_ho_so = NV_CURRENTTIME;
                } elseif (preg_match('/alertdownloadnotify/i', $_href, $_m)) {
                    $_title .= "</a> (Chưa tới thời gian bán hồ sơ mời thầu nên chưa cho phép tải.<a href=" . $url . " target=\"_blank\"> Xem chi tiết tại đây.</a>)";
                    $get_ho_so = 2;
                    $_href = $url;
                } else {
                    $get_ho_so = 4;
                    $_title .= '</a> (File chỉ có thể tải về bằng Internet Explorer, vui lòng xem hướng dẫn <a href="/news/tu-lieu-cho-nha-thau/huong-dan-nha-thau-tai-file-ho-so-moi-thau-tren-mang-dau-thau-quoc-gia-161.html">tại đây!</a>)'; // Lỗi chưa lập trình được.
                    $_href = $url;
                }
                $ho_so_moi_thau = str_replace($_outerHTML, '<a href="' . $_href . '" target="_blank">' . $_title . '</a>', $ho_so_moi_thau);
            }
        }
    }

    echo $ho_so_moi_thau . '<br>';
    return $ho_so_moi_thau;
}

function trim_v($_v)
{
    $_v = trim(urldecode($_v));
    $_v = trim($_v, "'");
    return trim($_v, '"');
}
