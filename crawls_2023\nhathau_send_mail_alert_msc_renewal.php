<?php

// Gửi mail nhắc gia hạn tên trên msc
// https://vinades.org/dauthau/dauthau.info/-/issues/1356

// Quét bảng nv4_vi_businesslistings_addinfo lấy các id_mail_renewal = 0 AND next_time_send_mail_renewal != 0 AND next_time_send_mail_renewal < NV_CURRENTTIME
// Insert vào bảng nv4_vi_business_renewal_mail
// update bảng nv4_vi_businesslistings_addinfo SET turn_send_mail_renewal = turn_send_mail_renewal +1, id_mail_renewal = :id_mail_renewal
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

define('BUSINESS_ADDINFO_TABLE', BUSINESS_PREFIX_GLOBAL . '_addinfo');

$last_id = 0;
$limit = 100;
do {
    $query_url = $db->query('SELECT * FROM ' . BUSINESS_ADDINFO_TABLE . ' WHERE id>' . $last_id . ' AND id_mail_renewal = 0 AND next_time_send_mail_renewal != 0 AND next_time_send_mail_renewal < ' . NV_CURRENTTIME . ' ORDER BY id ASC limit ' . $limit);
    $last_id = 0;
    while ($row = $query_url->fetch()) {
        $last_id = $row['id'];
        $id_mail_renewal = send_mail_renewal($row['id']);
        if ($id_mail_renewal) {
            echo '- Đã insert mail nhắc gia hạn tên trên msc vào bảng nv4_vi_business_renewal_mail, id: ' . $id_mail_renewal . "\n";
            $db->query('UPDATE ' . BUSINESS_ADDINFO_TABLE . ' SET turn_send_mail_renewal = turn_send_mail_renewal +1, id_mail_renewal = ' . $id_mail_renewal . ' WHERE id=' . $row['id']);
        }
    }
    $query_url->closeCursor();
    echo "last_id: " . $last_id . "\n";
} while ($last_id > 0);

echo "Thoi gian thuc hien = " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";

function send_mail_renewal($business_id)
{
    global $db, $config;

    try {
        $stmt = $db->prepare("INSERT INTO `" . $config['prefix'] . "_business_renewal_mail`(`business_id`, `add_time`) VALUES (:business_id, " . NV_CURRENTTIME . ")");
        $stmt->bindValue(':business_id', $business_id, PDO::PARAM_INT);
        $exc = $stmt->execute();
        if ($exc) {
            return $db->lastInsertId();
        }
        return false;
    } catch (PDOException $e) {
        print_r('Lỗi gửi mail');
        echo '<pre>';
        print_r($e);
        echo '</pre>';
        trigger_error($e->getMessage());
        return false;
    }
}
