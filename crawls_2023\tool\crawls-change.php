<?php

// L<PERSON>y danh sách thông báo mời thầu mới, chỉ quét được: không phải chỉ định thầu
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

$data = geturlpage();

/*
CREATE TABLE `nv23_crawls_change` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `msc_id` varchar(40) NOT NULL DEFAULT '',
    `createddate` varchar(25) NOT NULL DEFAULT '',
    `data_json` text NOT NULL DEFAULT '',
    `code` varchar(255) NOT NULL DEFAULT '',
    PRIMARY KEY (`id`),
    UNIQUE KEY `msc_id` (`msc_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
*/

$data = $data['page']['content'];
$array_id_msc = [];
foreach ($data as $row) {
    $array_id_msc[] = $dbcr->quote($row['id']);
    // if (!empty($row['docFileId'])) {
    //     $url_file = getfile($row['docFileId']);
    //     if (!empty($url_file)) {
    //         downloadfile($url_file, $row['docFilePath']);
    //     }
    // }
}
if (!empty($array_id_msc)) {
    $q = $dbcr->query("SELECT id, msc_id FROM `nv23_crawls_change` WHERE msc_id IN (" . implode(',', $array_id_msc) . ")");
    $array_id_msc = [];
    while ($_r = $q->fetch()) {
        $array_id_msc[$_r['msc_id']] = $_r['id'];
    }
    $q->closeCursor();

    foreach ($data as $row) {
        if (!isset($array_id_msc[$row['id']])) {

            $content = str_replace('&nbsp;', '', $row['content']) . ' ' . $row['docFilePath'];
            $content = html_entity_decode($content, ENT_QUOTES, 'UTF-8');
            preg_match_all('/(PR|PL|IB)([0-9]{10})/', $content, $m);
            $codes = $m[0];
            foreach ($codes as $code) {
                if (preg_match('/^PR/', $code)) {
                    $dbcr->exec('UPDATE nv23_duan_url SET url_run=0, uniqid = "" WHERE pno = ' . $dbcr->quote($code));
                    echo "Đã cập nhật \033[32mdự án: \033[33m{$code}\033[0m\n";
                } elseif (preg_match('/^PL/', $code)) {
                    $dbcr->exec('UPDATE nv23_crawls_khlcnt SET url_run=0, uniqid = "" WHERE planno = ' . $dbcr->quote($code));
                    echo "Đã cập nhật \033[32mKHLCNT: \033[33m{$code}\033[0m\n";
                } elseif (preg_match("/^IB/", $code)) {
                    $dbcr->exec('UPDATE nv23_crawls_tbmt SET url_run=0, uniqid = "" WHERE notifyno = ' . $dbcr->quote($code)) . ' ORDER BY id DESC LIMIT 1';
                    echo "Đã cập nhật \033[32mTBMT: \033[33m{$code}\033[0m\n";
                }
            }
            $codes = implode(", ", $codes);
            $dbcr->query("INSERT IGNORE INTO `nv23_crawls_change` (`msc_id`, `createddate`, `data_json`, `code`) VALUES (" . $dbcr->quote($row['id']) . ", " . $dbcr->quote($row['createdDate']) . ", " . $dbcr->quote(json_encode($row, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT)) . ", " . $dbcr->quote($codes) . ")");
        }
    }
}

function geturlpage($pageNumber = 0, $reload = 1)
{
    global $dbcr, $num_run, $from_date, $to_date;
    ++$num_run;

    $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-notification-system/services/get-list';
    $body = '{
      "pageSize": 20,
      "pageNumber": 0,
      "commonNoti": {
        "content": "Thông báo về việc hỗ trợ điều chỉnh thông tin",
        "isHighlighted": "",
        "notiType": "6"
      }
    }';

    $referer = 'https://muasamcong.mpi.gov.vn/web/guest/contractor-selection?render=index';
    $arr_agent = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:104.0) Gecko/20100101 Firefox/104.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/105.0.1343.42',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 9.0; SAMSUNG SM-F900U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.77 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ];

    srand((float) microtime() * 10000000);
    $rand = array_rand($arr_agent);
    $agent = $arr_agent[$rand];

    $_proxy = $dbcr->query("SELECT * FROM `nv4_proxy` WHERE `status`=2 ORDER BY `lasttime` ASC LIMIT 1")->fetch();
    $ch = curl_init();
    if (isset($_proxy['proxy'])) {
        $dbcr->query("UPDATE `nv4_proxy` SET `lasttime` = " . time() . ", number_run=number_run+1 WHERE `id` = " . $_proxy['id']);
        echo $_proxy['proxy'] . "\n";
        curl_setopt($ch, CURLOPT_PROXY, $_proxy['proxy']);
        if (isset($_proxy['proxyuserpwd']) and !empty($_proxy['proxyuserpwd'])) {
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, $_proxy['proxyuserpwd']);
        }
        curl_setopt($ch, CURLOPT_PROXYTYPE, $_proxy['proxytype']);
        curl_setopt($ch, CURLOPT_PROXYPORT, $_proxy['proxyport']);
    }
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
    curl_setopt($ch, CURLOPT_REFERER, $referer);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json'
    ));

    $json = curl_exec($ch);
    curl_close($ch);

    $data = json_decode($json, true);
    if (isset($data['page']['content'])) {
        return $data;
    } elseif ($reload and $num_run < 5) {
        return geturlpage($pageNumber, 1);
    } elseif ($reload) {
        return geturlpage($pageNumber, 0);
    }
    return [];
}

// Tạm thời không xử lý đọc file, khi nào muốn thì bỏ comment
/* function getfile($fileid, $reload = 1)
{
    global $dbcr, $num_run, $from_date, $to_date;
    ++$num_run;

    $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-notification-system/services/public/file/get-url';
    $body = '{
      "fileId": "' . $fileid . '",
      "responseUrl": true
    }';

    $referer = 'https://muasamcong.mpi.gov.vn/web/guest/notification-system';
    $arr_agent = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:104.0) Gecko/20100101 Firefox/104.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/105.0.1343.42',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 9.0; SAMSUNG SM-F900U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.77 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ];

    srand((float) microtime() * 10000000);
    $rand = array_rand($arr_agent);
    $agent = $arr_agent[$rand];

    $_proxy = $dbcr->query("SELECT * FROM `nv4_proxy` WHERE `status`=2 ORDER BY `lasttime` ASC LIMIT 1")->fetch();
    $ch = curl_init();
    if (isset($_proxy['proxy'])) {
        $dbcr->query("UPDATE `nv4_proxy` SET `lasttime` = " . time() . ", number_run=number_run+1 WHERE `id` = " . $_proxy['id']);
        echo $_proxy['proxy'] . "\n";
        curl_setopt($ch, CURLOPT_PROXY, $_proxy['proxy']);
        if (isset($_proxy['proxyuserpwd']) and !empty($_proxy['proxyuserpwd'])) {
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, $_proxy['proxyuserpwd']);
        }
        curl_setopt($ch, CURLOPT_PROXYTYPE, $_proxy['proxytype']);
        curl_setopt($ch, CURLOPT_PROXYPORT, $_proxy['proxyport']);
    }
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
    curl_setopt($ch, CURLOPT_REFERER, $referer);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json'
    ));

    $json = curl_exec($ch);
    curl_close($ch);

    $data = json_decode($json, true);
    if (isset($data['publicKey'])) {
        return $data['publicKey'];
    } elseif ($reload and $num_run < 5) {
        return getfile($fileid, 1);
    } elseif ($reload) {
        return getfile($fileid, 0);
    }
    return [];
}

function downloadfile($url, $filename, $reload = 1)
{
    global $dbcr, $num_run, $from_date, $to_date;
    ++$num_run;

    // $referer = 'https://muasamcong.mpi.gov.vn/web/guest/notification-system';
    $arr_agent = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:104.0) Gecko/20100101 Firefox/104.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/105.0.1343.42',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 9.0; SAMSUNG SM-F900U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.77 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ];

    srand((float) microtime() * 10000000);
    $rand = array_rand($arr_agent);
    $agent = $arr_agent[$rand];
    
    $_proxy = $dbcr->query("SELECT * FROM `nv4_proxy` WHERE `status`=2 ORDER BY `lasttime` ASC LIMIT 1")->fetch();
    $ch = curl_init();
    if (isset($_proxy['proxy'])) {
        $dbcr->query("UPDATE `nv4_proxy` SET `lasttime` = " . time() . ", number_run=number_run+1 WHERE `id` = " . $_proxy['id']);
        echo $_proxy['proxy'] . "\n";
        curl_setopt($ch, CURLOPT_PROXY, $_proxy['proxy']);
        if (isset($_proxy['proxyuserpwd']) and !empty($_proxy['proxyuserpwd'])) {
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, $_proxy['proxyuserpwd']);
        }
        curl_setopt($ch, CURLOPT_PROXYTYPE, $_proxy['proxytype']);
        curl_setopt($ch, CURLOPT_PROXYPORT, $_proxy['proxyport']);
    }
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
    // curl_setopt($ch, CURLOPT_REFERER, $referer);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    $fp = fopen('crawls-change-file/' . $filename, 'w');

    
    curl_setopt($ch, CURLOPT_FILE, $fp); 
    // curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    // curl_setopt($ch, CURLOPT_HTTPHEADER, array(
    //     'Content-Type: application/json'
    // ));

    curl_exec($ch);
    curl_close($ch);
    // file_put_contents(NV_ROOTDIR . '/crawls-change-file/' . $filename, $file);
    fclose($fp);
    if (file_exists('crawls-change-file/'. $filename)) {
        return true;
    } elseif ($reload and $num_run < 5) {
        return downloadfile($url, $filename, 1);
    } elseif ($reload) {
        return downloadfile($url, $filename, 0);
    }
    return [];
}
 */
