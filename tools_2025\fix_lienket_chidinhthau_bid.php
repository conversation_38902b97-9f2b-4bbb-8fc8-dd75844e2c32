<?php

define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

$file_name = NV_ROOTDIR . '/tools_2025/fix_lienket_chidinhthau_bid.txt';
if (file_exists($file_name)) {
    $strID = file_get_contents($file_name);
    $arrID = explode('_', $strID);
    $id = intval($arrID[0]);
    $max_id = intval($arrID[1]);
} else {
    list($id, $max_id) = $db->query("SELECT MIN(id), MAX(id) FROM nv4_vi_bidding_plans_contract WHERE `kqlcnt_id` > 0 AND `so_tbmt_mst` = ''")->fetch(PDO::FETCH_NUM);
}

echo ("\nmax_id: " . $max_id);
$last_id = ($id  + 1000);

$_result = $db->query("SELECT id, kqlcnt_id FROM nv4_vi_bidding_plans_contract WHERE `kqlcnt_id` > 0 AND `so_tbmt_mst` = '' AND id >= " . $id . " AND id < " . $last_id . " ORDER BY id LIMIT 100");
    
while ($row = $_result->fetch()) {
    echo ("\n>>ID: " . $row['id']);
    $code = $db->query("SELECT `code` FROM `nv4_vi_bidding_result` WHERE `id` = " . $row['kqlcnt_id'])->fetchColumn();
    if ($code) {
        $db->exec("UPDATE nv4_vi_bidding_plans_contract SET so_tbmt_mst=" . $db->quote($code) . " WHERE id = " . $row['id']);
        $db->exec("UPDATE nv4_en_bidding_plans_contract SET so_tbmt_mst=" . $db->quote($code) . " WHERE id = " . $row['id']);
    }
    $newid = $row['id'];
}
$_result->closeCursor();

echo ("\nnewid:" . $newid);
echo ("\nid: " . $id);
if ($newid > $id) {
    echo ("\nnewid = " . $newid);
    file_put_contents($file_name, $newid . '_' . $max_id);
} else {
    if ($last_id < $max_id) {
        $newid = $last_id;
        echo ("\nlast_id = " . $last_id);
        file_put_contents($file_name, $newid . '_' . $max_id);
    } else {
        echo (">> \033[91mHết dữ liệu \033[0m");
        exit(1);
    }
}

echo "\nThoi gian thuc hien = " . number_format((microtime(true) - NV_START_TIME), 3, '.', '');
echo "\n> Đã chạy xong: " . $i;
echo "\n-------------------------------------------\n";
