<?php
ob_start();
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME))));
require NV_ROOTDIR . '/mainfile.php';
/*
$run_bash = 1 để chạy dữ liệu hằng ngày

Chạy các dữ liệu lỗi:
Nếu có lỗi thông báo lên slack chỉ cần cấu hình cho $run_bash = 2 để chạy lại các giá trị bị lỗi
 */

$arr_cerrank = array(
    0 => 'Chưa xếp loại',
    1 => 'Trung Bình',
    2 => 'Khá',
    3 => 'Giỏi',
    4 => 'Xuất sắc'
);

try {
    $run_bash = (int) $request_mode->get('run_bash', '0'); // Mặc định là 0 nếu không có giá trị
    // Xác định điều kiện lấy dữ liệu dựa trên $run_bash
    if ($run_bash == 1) {
        // khi nào chạy dữ liệu lỗi mới vô đây
        $url_run_condition = -1;
    } else {
        $url_run_condition = 0;
    }

    // Nếu không hợp lệ, thoát
    if ($url_run_condition === null) {
        die("Giá trị của run_bash không hợp lệ. Vui lòng đặt giá trị là 1 hoặc 2.");
    }
    $result_data = $db->query('SELECT * FROM `nv4_certificate_courses` WHERE `status`=1 AND `url_run` = ' . $url_run_condition . ' ORDER BY id ASC limit 1')->fetch();
    if (!empty($result_data)) {
        $startPage = ($result_data['total_pages'] > 0) ? intval($result_data['total_pages']) : 1;
        $pageNumber = $startPage;
        $totalPages = 1;
        $pageEmpty = 0;
        $num_students = $result_data['num_students'];
        $num_data_collected = 0;
        do {
            $new_data = 0;
            $num_data = 0;
            $num_run = 0;
            $data = getdata($result_data['id'], $result_data['id_msc'], $pageNumber, 1);

            if (!empty($data['content'])) {
                $_totalPages = intval($data['totalPages']);
                if ($_totalPages > $totalPages) {
                    $totalPages = $_totalPages;
                }
                foreach ($data['content'] as $item) {
                    ++$num_data;
                    ++$num_data_collected;
                    if (!empty($item['birthDate'])) {
                        $parts = explode('T', $item['birthDate'])[0]; 
                        $dateParts = explode('-', $parts);
                        $formattedDate = $dateParts[2].'.'.$dateParts[1].'.'.$dateParts[0];
                        $item['birthDate'] = strtotime($formattedDate);
                    }
                                     
                    $idNo = preg_replace('/\s+/', '', ltrim($item['idNo'], "'")); // Xóa khoảng trắng và dấu '
                    if (preg_match('/\d/', $idNo)) {
                        $item['idNo'] = $idNo; 
                    } else {
                        $birthPlaceCleaned = preg_replace('/\s+/', '', ltrim($item['birthPlace'], "'")); // Xóa khoảng trắng và dấu '
                        $idNoCleaned = $item['idNo'];
                        $item['idNo'] = $birthPlaceCleaned;
                        $item['birthPlace'] = $idNoCleaned;
                    }
                     
                    $place = $item['birthPlace'];
                    $place = trim(nv_compound_unicode($item['birthPlace'])); // Xóa khoảng trắng đầu và cuối
                    $place = preg_replace('/\s+/', ' ', $place); // Thay thế nhiều khoảng trắng liên tiếp bằng 1 khoảng trắng
                    // Chuyển về chữ thường
                    $place = mb_strtolower($place, 'UTF-8');
                    $place = str_replace(["\r", "\n", "\t", "\xC2\xA0", "\u00A0"], '', $place); // Xóa ký tự xuống dòng
                    $replace_map = [
                        // Các biến thể tên tỉnh/thành phố cần chuẩn hóa
                        'tp.hcm' => 'tp.hồ chí minh',
                        'tp. hcm' => 'tp.hồ chí minh',
                        'tp hcm' => 'tp.hồ chí minh',
                        'tp. hồ chí minh' => 'tp.hồ chí minh',
                        'tp hồ chí minh' => 'tp.hồ chí minh',
                        'hồ chí minh' => 'tp.hồ chí minh',
                        'sài gòn' => 'tp.hồ chí minh',
                        'gia định' => 'tp.hồ chí minh',
                        'củ chi' => 'tp.hồ chí minh',
                        'hóc môn' => 'tp.hồ chí minh',
                        'bình chánh' => 'tp.hồ chí minh',
                        'tphcm' => 'tp.hồ chí minh',
                        'thành phố hồ chí minh' => 'tp.hồ chí minh',
                        'tp.hồ chí minh' => 'tp.hồ chí minh',
                        'hcm' => 'tp.hồ chí minh',
                        'nq: hồ chí minh' => 'tp.hồ chí minh',
                        'thủ đức' => 'tp.hồ chí minh',
                        'tp' => 'tp.hồ chí minh',
                        'tphồ chí minh' => 'tp.hồ chí minh',
                        'tp.thủ đức' => 'tp.hồ chí minh',
                        'cần giờ' => 'tp.hồ chí minh',
                        'tp.hồchíminh' => 'tp.hồ chí minh',
                        'tp. hồchíminh' => 'tp.hồ chí minh',

                        'tp.huế' => 'thừa thiên - huế',
                        't.t. huế' => 'thừa thiên - huế',
                        'tt.huế' => 'thừa thiên - huế',
                        'tt. huế' => 'thừa thiên - huế',
                        't.t.huế' => 'thừa thiên - huế',
                        'thừa thiên huế' => 'thừa thiên - huế',
                        'thừa thiên-huế' => 'thừa thiên - huế',
                        'tỉnh thừa thiên huế' => 'thừa thiên - huế',
                        'tt huế' => 'thừa thiên - huế',
                        'huế' => 'thừa thiên - huế',
                        't t huế' => 'thừa thiên - huế',
                        'tt-huế' => 'thừa thiên - huế',
                        'thừa thiên' => 'thừa thiên - huế',
                        'thừa thiên – huế' => 'thừa thiên - huế',
                        'tp. huế' => 'thừa thiên - huế',
                        'thừa thiện huế' => 'thừa thiên - huế',
                        'thừa thiên- huế' => 'thừa thiên - huế',
                        't. t. huế' => 'thừa thiên - huế',
                        'thành phố huế' => 'thừa thiên - huế',

                        'bà rịa – vũng tàu' => 'bà rịa - vũng tàu',
                        'bà rịa-vũng tàu' => 'bà rịa - vũng tàu',
                        'bà rịa vũng tàu' => 'bà rịa - vũng tàu',
                        'bà rịa vũng tàu ' => 'bà rịa - vũng tàu',
                        'vũng tàu' => 'bà rịa - vũng tàu',
                        'brvt' => 'bà rịa - vũng tàu',
                        'br - vũngtàu' => 'bà rịa - vũng tàu',
                        'br - vũng tàu' => 'bà rịa - vũng tàu',
                        'bà rịa vt' => 'bà rịa - vũng tàu',
                        'bà rịa - vt' => 'bà rịa - vũng tàu',
                        'bà rịa- vũng tàu' => 'bà rịa - vũng tàu',
                        'bà rịa-  vũng tàu' => 'bà rịa - vũng tàu',
                        'bà rịa -vũng tàu' => 'bà rịa - vũng tàu',
                        'bà rịa' => 'bà rịa - vũng tàu',
                        'tỉnh bà rịa - vũng tàu' => 'bà rịa - vũng tàu',
                        'tp vũng tàu' => 'bà rịa - vũng tàu',
                        'bắc kạn' => 'bắc cạn',
                        'bắc kan' => 'bắc cạn',
                        
                        'bắc ninh' => 'bắc ninh',
                        'nq: bắc ninh' => 'bắc ninh',
                        'quế võ - bắc ninh' => 'bắc ninh',
                        'tỉnh bắc ninh' => 'bắc ninh',
                        
                        'ninhbình' => 'ninh bình',
                        'tỉnh ninh bình' => 'ninh bình',
                        'ninh bình' => 'ninh bình',
                        'nq: ninh bình' => 'ninh bình',
                        'ninh định' => 'ninh bình',
                        'yên khánh - ninh bình' => 'ninh bình',
                        
                        'biên hòa' => 'đồng nai',
                        'đồng nai' => 'đồng nai',
                        'đồng na' => 'đồng nai',
                        'tỉnh đồng nai' => 'đồng nai',
                        
                        'vĩnh phú' => 'phú thọ',
                        'vĩnh yên' => 'vĩnh phúc',
                        'tỉnh vĩnh phúc' => 'vĩnh phúc',
                        'lập trạch, vĩnh phúc' => 'vĩnh phúc',
                        
                        'hà tây' => 'hà nội',
                        'hà nôij' => 'hà nội',
                        'hànội' => 'hà nội',
                        'tp. hà nội' => 'hà nội',
                        'tp.hà nội' => 'hà nội',
                        'tp hà nội' => 'hà nội',
                        'phúc thọ - hà nội' => 'hà nội',
                        'ba vì - hà nội' => 'hà nội',
                        'ba vì - hà nội ' => 'hà nội',
                        'thường tín - hà nội' => 'hà nội',
                        'thường tín - hà nội ' => 'hà nội',
                        'sơn tây' => 'hà nội',
                        'nq: hà nội' => 'hà nội',
                        'quốc oai - hà nội' => 'hà nội',
                        'hà tây / hà nội' => 'hà nội',
                        'thành phố hà nội' => 'hà nội',
                        'thành phố hà nội ' => 'hà nội',
                        'bình yên - thạch thát - hà nội' => 'hà nội',
                        'ba trại, ba vì , hà nội' => 'hà nội',
                        
                        'quản nam' => 'quảng nam',
                        'quảng nam' => 'quảng nam',
                        'tỉnh quảng nam' => 'quảng nam',
                        
                        'quản bình' => 'quảng bình',
                        'quảng bình' => 'quảng bình',
                        'tỉnh quảng bình' => 'quảng bình',
                        'quãng hòa, quãng trạch, quảng bình' => 'quảng bình',
                        
                        'quản trị' => 'quảng trị',
                        'tỉnh quảng trị' => 'quảng trị',
                        'tỉnh quảng trị ' => 'quảng trị',
                        
                        'tuyên qunag' => 'tuyên quang',
                        'tỉnh tuyên quang' => 'tuyên quang',
                        
                        'quãng ngãi' => 'quảng ngãi',
                        'tỉnh quảng ngãi' => 'quảng ngãi',
                        
                        'quảng ninh' => 'quảng ninh',
                        'nq: quảng ninh' => 'quảng ninh',
                        'đầm hà - quảng ninh' => 'quảng ninh',
                        'mông dương - cẩm phả - quảng ninh' => 'quảng ninh',
                        'uông bí - quảng ninh' => 'quảng ninh',
                        'quảng yên - quảng ninh' => 'quảng ninh',
                        'hoành bồ - quảng ninh' => 'quảng ninh',
                        'quảng xuyên' => 'quảng ninh',
                        'quảnh ninh' => 'quảng ninh',
                        'tỉnh quảng ninh' => 'quảng ninh',
                        'quảng yên' => 'quảng ninh',
                        'việt hưng, hạ long, quảng ninh' => 'quảng ninh',
                        'móng cái, quảng ninh' => 'quảng ninh',
                        'móng cái' => 'quảng ninh',
                        'tinh quảng ninh' => 'quảng ninh',
                        
                        'thanh hoá' => 'thanh hóa',
                        'tp.thanh hóa' => 'thanh hóa',
                        'thanh hóa' => 'thanh hóa',
                        'nq: thanh hóa' => 'thanh hóa',
                        'thọ xuân - thanh hóa' => 'thanh hóa',
                        'tĩnh gia - thanh hóa' => 'thanh hóa',
                        'tỉnh thanh hóa' => 'thanh hóa',
                        'tỉnh thanh hoá' => 'thanh hóa',
                        'tỉnh thanh hóa ' => 'thanh hóa',
                        'hợp thành, triệu sơn, thanh hóa' => 'thanh hóa',
                        'thanh. hoá' => 'thanh hóa',
                        'đắklắk' => 'đắk lắk',
                        'đăklăk' => 'đắk lắk',
                        'buôn ma thuột' => 'đắk lắk',
                        'thành phố buôn ma thuột' => 'đắk lắk',
                        'tp. buôn ma thuột' => 'đắk lắk',
                        'đăk lăk' => 'đắk lắk',
                        'đắc lắc' => 'đắk lắk',
                        'daklak' => 'đắk lắk',
                        'dak lak' => 'đắk lắk',
                        'tỉnh đắk lắk' => 'đắk lắk',
                        'liên sơn' => 'đắk lắk',
                        'đắc nông' => 'đắk nông',
                        
                        'bình sơn quảng ngãi' => 'quảng ngãi',
                        
                        'qui nhơn' => 'bình định',
                        'quy nhơn' => 'bình định',
                        'bình định' => 'bình định',
                        'bình dđinh' => 'bình định',
                        'bình bịnh' => 'bình định',
                        'bình ðịnh' => 'bình định',
                        'tỉnh bình định' => 'bình định',
                        'hoài châu, hoài nhơn, bình định' => 'bình định',
                        'nhơn mỹ, an nhơn, bình định' => 'bình định',
                        
                        'thuận hải' => 'ninh thuận',
                        'hàm tân-thuận hải' => 'ninh thuận',
                        'bắc bình' => 'bình thuận',
                        
                        'hà bắc' => 'bắc giang',
                        'bắc giang' => 'bắc giang',
                        'bắc gang' => 'bắc giang',
                        'lạng giang - bắc giang' => 'bắc giang',
                        'tỉnh bắc giang' => 'bắc giang',
                        'bắc gaing' => 'bắc giang',
                        'lục ngạn, bắc giang' => 'bắc giang',
                        
                        'sông bé' => 'bình dương',
                        'bình dương' => 'bình dương',
                        'tỉnh bình dương' => 'bình dương',
                        
                        'bắc thái' => 'thái nguyên',
                        'thái nguyên' => 'thái nguyên',
                        'nq: thái nguyên' => 'thái nguyên',
                        'tỉnh thái nguyên' => 'thái nguyên',
                        
                        'hoàng liên sơn' => 'lào cai',
                        'lòa cai' => 'lào cai',
                        'tỉnh lào cai' => 'lào cai',
                        'tỉnh lào cai ' => 'lào cai',
                        
                        'phúc thọ' => 'hà nội',
                        'thạch thất' => 'hà nội',
                        
                        'nam hà' => 'nam định',
                        'tỉnh nam định' => 'nam định',
                        'nam định' => 'nam định',
                        'nan định' => 'nam định',
                        'hải hậu - nam định' => 'nam định',
                        'trực ninh - nam định' => 'nam định',
                        'nam ðịnh' => 'nam định',
                        
                        'việt trì' => 'phú thọ',
                        'tỉnh phú thọ' => 'phú thọ',
                        
                        'tp. đà nẵng' => 'đà nẵng',
                        'tp đà nẵng' => 'đà nẵng',
                        'thành phố đà nẵng' => 'đà nẵng',
                        'tp.đà nẵng' => 'đà nẵng',
                        'ðà nẵng' => 'đà nẵng',

                        'hải hưng' => 'hải dương',
                        'hải dương' => 'hải dương',
                        'hà dương' => 'hải dương',
                        'nq: hải dưng' => 'hải dương',
                        'ninh giang - hải dương' => 'hải dương',
                        'thanh hà - hải dương' => 'hải dương',
                        'bình giang - hải dương' => 'hải dương',
                        'tứ kỳ - hải dương' => 'hải dương',
                        '̀ hải dương' => 'hải dương',
                        'tỉnh hải dương' => 'hải dương',
                        'tỉnh hải dương ' => 'hải dương',
                        
                        'nghệ anh' => 'nghệ an',
                        'tp vinh - nghệ an' => 'nghệ an',
                        'tpvinh - nghệ an' => 'nghệ an',
                        'nghĩa đàn - nghệ an' => 'nghệ an',
                        'nghĩa đàn- nghệ an' => 'nghệ an',
                        'anh sơn - nghệ an' => 'nghệ an',
                        'thanh chương - nghệ an' => 'nghệ an',
                        'đô lương- nghệ an' => 'nghệ an',
                        'đô lương - nghệ an' => 'nghệ an',
                        'thái hòa - nghệ an' => 'nghệ an',
                        'kỳ sơn - nghệ an' => 'nghệ an',
                        'hưng nguyên - nghệ an' => 'nghệ an',
                        'diễn châu - nghệ an' => 'nghệ an',
                        'yên thành - nghệ an' => 'nghệ an',
                        'quỳnh lưu - nghệ an' => 'nghệ an',
                        'con cuông - nghệ an' => 'nghệ an',
                        'nghi lộc - nghệ an' => 'nghệ an',
                        'nghị lộc - nghệ an' => 'nghệ an',
                        'cửa lò - nghệ an' => 'nghệ an',
                        'nam đàn - nghệ an' => 'nghệ an',
                        'nghệ an' => 'nghệ an',
                        'nghệ tĩnh' => 'nghệ an',
                        'nq: nghệ an' => 'nghệ an',
                        'vinh, nghệ an' => 'nghệ an',
                        'tỉnh nghệ an' => 'nghệ an',
                        'huyện hưng nguyên, tỉnh nghệ an' => 'nghệ an',
                        
                        'nghi xuân - hà tĩnh' => 'hà tĩnh',
                        'hải sơn -hà tĩnh' => 'hà tĩnh',
                        'cẩm xuyên - hà tĩnh' => 'hà tĩnh',
                        'hà tĩnh' => 'hà tĩnh',
                        'tỉnh hà tĩnh' => 'hà tĩnh',
                        
                        'bình trị thiên' => 'thừa thiên - huế',
                        
                        'hà nam ninh' => 'nam định',
                        'hà nam' => 'hà nam',
                        'bình lục - hà nam' => 'hà nam',
                        'tỉnh hà nam' => 'hà nam',
                        'tỉnh hà nam ' => 'hà nam',
                        'nhân hòa - lý nhân - hà nam' => 'hà nam',
                        
                        'long an`' => 'long an',
                        'long an' => 'long an',
                        'tỉnh long an' => 'long an',
                        
                        'đà lạt' => 'lâm đồng',
                        'tỉnh lâm đồng' => 'lâm đồng',
                        'tỉnh lâm đồng ' => 'lâm đồng',
                        
                        'nha trang' => 'khánh hòa',
                        'tỉnh khánh hoà' => 'khánh hòa',
                        
                        'hà sơn bình' => 'hòa bình',
                        'tỉnh hòa bình' => 'hòa bình',
                        
                        'nghĩa lộ' => 'yên bái',
                        'tp.yên bái' => 'yên bái',
                        'thành phố yên bái, tỉnh yên bái' => 'yên bái',
                        
                        'tp. hải phòng' => 'hải phòng',
                        'tp.hải phòng' => 'hải phòng',
                        'nq: hải phòng' => 'hải phòng',
                        'thủy nguyên - hải phòng' => 'hải phòng',
                        'kiến an - hải phòng' => 'hải phòng',
                        'thành phố hải phòng' => 'hải phòng',
                        'vĩnh bảo, hải phòng' => 'hải phòng',
                        'kiến an, hải phòng' => 'hải phòng',
                        
                        'tiền giang' => 'tiền giang',
                        'mỹ tho' => 'tiền giang',
                        
                        'bến tre' => 'bến tre',
                        'tỉnh bến tre' => 'bến tre',
                        
                        'bình phước' => 'bình phước',
                        'tỉnh bình phước' => 'bình phước',
                        
                        'tây ninh' => 'tây ninh',
                        'tỉnh tây ninh' => 'tây ninh',
                        
                        'đồng tháp' => 'đồng tháp',
                        'ðồng tháp' => 'đồng tháp',
                        'tỉnh đồng tháp' => 'đồng tháp',
                        
                        'thái bình' => 'thái bình',
                        'nq: thái bình' => 'thái bình',
                        'quỳnh phụ - thái bình' => 'thái bình',
                        'thái thụy - thái bình' => 'thái bình',
                        'kiến xương - thái bình' => 'thái bình',
                        'vũ thư - thái bình' => 'thái bình',
                        'đông hưng - thái bình' => 'thái bình',
                        'hưng hà - thái bình' => 'thái bình',
                        'tỉnh thái bình' => 'thái bình',
                        'tỉnh thái bình ' => 'thái bình',
                        'độc lập, hưng hà, thái bình' => 'thái bình',
                        'kiến xương, thái bình' => 'thái bình',
                        
                        'hưng yên' => 'hưng yên',
                        'nq: hưng yên' => 'hưng yên',
                        'phù cừ - hưng yên' => 'hưng yên',
                        'tỉnh hưng yên' => 'hưng yên',
                        'tinh hưng yên' => 'hưng yên',
                        'yên mỹ - hưng yên' => 'hưng yên',
                        
                        'vĩnh long' => 'vĩnh long',
                        'nq: vĩnh long' => 'vĩnh long',
                        'tỉnh vĩnh long' => 'vĩnh long',
                        'cửu long' => 'vĩnh long',
                        'sóc trăng' => 'sóc trăng',
                        'nq: sóc trăng' => 'sóc trăng',
                        'tỉnh sóc trăng' => 'sóc trăng',
                        
                        'cần thơ' => 'cần thơ',
                        'nq: cần thơ' => 'cần thơ',
                        'tp. cần thơ' => 'cần thơ',
                        'thành phố cần thơ' => 'cần thơ',
                        
                        'hậu giang' => 'hậu giang',
                        'nq: hậu giang' => 'hậu giang',
                        
                        'bạc liên' => 'bạc liêu',
                        
                        'an giang' => 'an giang',
                        'long xuyên' => 'an giang',
                        'tỉnh an giang' => 'an giang',
                        
                        'kontum' => 'kon tum',
                        'kom tum' => 'kon tum',
                        'gia lai' => 'gia lai',
                        'tỉnh gia lai' => 'gia lai',
                        
                        'phú yên' => 'phú yên',
                        'tỉnh phú yên' => 'phú yên',
                        
                        'cà mau' => 'cà mau',
                        'tỉnh cà mau' => 'cà mau',
                        'minh hải' => 'cà mau',
                        'điện biên' => 'điện biên',
                        'tỉnh điện biên' => 'điện biên',
                        
                        'lai châu' => 'lai châu',
                        'tỉnh lai châu' => 'lai châu',
                        
                        'sơn la' => 'sơn la',
                        'tỉnh sơn la' => 'sơn la',
                        
                        'hà giang' => 'hà giang',
                        'tỉnh hà giang' => 'hà giang',
                        
                        'lạng sơn' => 'lạng sơn',
                        'tỉnh lạng sơn' => 'lạng sơn',
                        
                        'cao bằng' => 'cao bằng',
                        'tinh cao bằng' => 'cao bằng',
                        'xã thống nhất - huyện hạ lang' => 'cao bằng',
                        'xã đức quang - huyện hạ lang' => 'cao bằng',
                        'phòng dân tộc huyện hạ lang' => 'cao bằng',
                        'xã minh long - huyện hạ lang' => 'cao bằng',
                        'phòng kt - ht huyện hạ lang' => 'cao bằng',
                        'xã quang long - huyện hạ lang' => 'cao bằng',
                        'xã thị hoa - huyện hạ lang' => 'cao bằng',
                        'xã kim cúc - huyện bảo lạc' => 'cao bằng',
                        'xã huy giáp - huyện bảo lạc' => 'cao bằng',
                        'phòng dân tộc huyện bảo lạc' => 'cao bằng',
                        'xã hồng an - huyện bảo lạc' => 'cao bằng',
                        'ubnd thị trấn bảo lạc - huyện bảo lạc' => 'cao bằng',
                        'xã cốc pàng - huyện bảo lạc' => 'cao bằng',
                        'xã đình phùng - huyện bảo lạc' => 'cao bằng',
                        'ban qlda huyện bảo lạc' => 'cao bằng',
                        'xã xuân trường - huyện bảo lạc' => 'cao bằng',
                        'xã hưng đạo - huyện bảo lạc' => 'cao bằng',
                        'xã cô ba - huyện bảo lạc' => 'cao bằng',
                        'xã hồng trị - huyện bảo lạc' => 'cao bằng',
                        'xã thượng hà - huyện bảo lạc' => 'cao bằng',
                        'phòng giáo dục huyện bảo lạc' => 'cao bằng',
                        'xã bảo toàn - huyện bảo lạc' => 'cao bằng',
                        'xã phan thanh - huyện bảo lạc' => 'cao bằng',
                        'xã đồng loan - huyện hạ lang' => 'cao bằng',
                        'xã vinh quý - huyện hạ lang' => 'cao bằng',
                        'tt thanh nhật - huyện hạ lang' => 'cao bằng',
                        'xã kim loan - huyện hạ lang' => 'cao bằng',
                        'xã lý quốc - huyện hạ lang' => 'cao bằng',
                        'ban dân tộc - tỉnh cao bằng' => 'cao bằng',
                        'sở công thương - tỉnh cao bằng' => 'cao bằng',
                        'sở giao thông vận tải - tỉnh cao bằng' => 'cao bằng',
                        'trung tâm khuyến nông và giống nln - tỉnh cao bằng' => 'cao bằng',
                        'sở nông nghiệp & ptnt - tỉnh cao bằng' => 'cao bằng',
                        'sở y tế - tỉnh cao bằng' => 'cao bằng',
                        'xã sơn lộ - huyện bảo lạc' => 'cao bằng',
                        'xã khánh xuân - huyện bảo lạc' => 'cao bằng',
                        'văn hóa tt huyện - huyện bảo lạc' => 'cao bằng',
                        'xã đàm thủy - huyện trùng khánh' => 'cao bằng',
                        'xã ngọc khê - huyện trùng khánh' => 'cao bằng',
                        'xã ngọc côn - huyện trùng khánh' => 'cao bằng',
                        'thị trấn trùng khánh - huyện trùng khánh' => 'cao bằng',
                        'xã đoài dương - huyện trùng khánh' => 'cao bằng',
                        'xã lăng hiếu - huyện trùng khánh' => 'cao bằng',
                        'xã quang trung - huyện trùng khánh' => 'cao bằng',
                        'xã chí viễn - huyện trùng khánh' => 'cao bằng',
                        'thị trấn trà lĩnh - huyện trùng khánh' => 'cao bằng',
                        'xã tri phương - huyện trùng khánh' => 'cao bằng',
                        'phòng tài chính huyện - huyện trùng khánh' => 'cao bằng',
                        'xã đức xuân - huyện thạch an' => 'cao bằng',
                        'xã vân trình - huyện thạch an' => 'cao bằng',
                        'xã lê lai - huyện thạch an' => 'cao bằng',
                        'xã minh khai - huyện thạch an' => 'cao bằng',
                        'xã trọng con - huyện thạch an' => 'cao bằng',
                        'xã canh tân - huyện thạch an' => 'cao bằng',
                        'xã đức thông - huyện thạch an' => 'cao bằng',
                        'xã quang trọng - huyện thạch an' => 'cao bằng',
                        'thị trấn đông khê - huyện thạch an' => 'cao bằng',
                        'xã lê lợi - huyện thạch an' => 'cao bằng',
                        'xã kim đồng - huyện thạch an' => 'cao bằng',
                        'xã đức long - huyện thạch an' => 'cao bằng',
                        'xã thái cường - huyện thạch an' => 'cao bằng',
                        'xã sơn lập - huyện bảo lạc' => 'cao bằng',
                        'xã an lạc - huyện hạ lang' => 'cao bằng',
                        'thị trấn thanh nhật - huyện hạ lang' => 'cao bằng',
                        'văn phòng huyện ủy hạ lang' => 'cao bằng',
                        'xã cô ngân - huyện hạ lang' => 'cao bằng',
                        'xã thắng lợi - huyện hạ lang' => 'cao bằng',
                        'xã cao chương - huyện trùng khánh' => 'cao bằng',
                        'xã quang vinh - huyện trùng khánh' => 'cao bằng',
                        'xã phong nặm - huyện trùng khánh' => 'cao bằng',
                        'xã khâm thành - huyện trùng khánh' => 'cao bằng',
                        'phòng dân tộc huyện trùng khánh - huyện trùng khánh' => 'cao bằng',
                        'xã trung phúc - huyện trùng khánh' => 'cao bằng',
                        'phòng nông nghiệp huyện trùng khánh - huyện trùng khánh' => 'cao bằng',
                        'xã đức hồng - huyện trùng khánh' => 'cao bằng',
                        'xã xuân nội - huyện trùng khánh' => 'cao bằng',
                        'xã quang hán - huyện trùng khánh' => 'cao bằng',
                        'xã cao thăng - huyện trùng khánh' => 'cao bằng',
                        'phòng giáo dục huyện trùng khánh - huyện trùng khánh' => 'cao bằng',
                        'xã thụy hùng - huyện thạch an' => 'cao bằng',
                        'phòng dân tộc - huyện thạch an' => 'cao bằng',
                        'tt gdtx - gdnn - huyện thạch an' => 'cao bằng',
                        'phòng tài chính - huyện thạch an' => 'cao bằng',
                        'trung tâm trợ giúp pháp lý - tỉnh cao bằng' => 'cao bằng',
                        'sở lao động, tb&xh - tỉnh cao bằng' => 'cao bằng',
                        'trung tâm ứng dụng tbkh&cn - tỉnh cao bằng' => 'cao bằng',
                        'sở nn&ptnt - tỉnh cao bằng' => 'cao bằng',
                        'hội liên hiệp phụ nữ - tỉnh cao bằng' => 'cao bằng',
                        'thanh tra sở giao thông vận tải - tỉnh cao bằng' => 'cao bằng',
                        'tỉnh cao bằng'  => 'cao bằng',
                        'tp cần thơ' => 'cần thơ',
                        'vinh' => 'nghệ an',
                        'thụy phương bắc từ liêm hà nội' => 'hà nội',
                        'cẩm thủy, thanh hóa' => 'thanh hóa',
                        'đông anh, hà nội' => 'hà nội',
                        'tiền yên, hoài đức, hà nội' => 'hà nội',
                        'thanh xuân - thanh chương - nghệ an' => 'nghệ an',
                        'minh diệu, hòa bình, bạc liêu' => 'bạc liêu',
                        'tắc vân, tp. cà mau, tỉnh cà mau' => 'cà mau',
                        'tổ 9, thị trấn thịnh long, huyện hải hậu, tỉnh nam định' => 'nam định',
                        'tam thuấn, phúc thọ, hà nội' => 'hà nội',
                        'xà phiên, long mỹ, hậu giang' => 'hậu giang',
                        'đằng chương, yên tiến, ý yên, nam định' => 'nam định',
                        'hải anh, hải hậu, nam định' => 'nam định',
                        'hải tân, hải hậu, nam định' => 'nam định',
                        'xã vĩnh trạch, thành phố bạc liêu, tỉnh bạc liêu' => 'bạc liêu',
                        'xã vĩnh mỹ a, huyện hoà bình, tỉnh bạc liêu' => 'bạc liêu',
                        'bạc llieeu' => 'bạc liêu',
                        'yên cường ý yên nam định' => 'nam định',
                        'thanh thuỷ, thanh hà, hải duong' => 'hải dương',
                        'tổ dân phố lê xá - thị trấn mỹ lộc - mỹ lộc - nam định' => 'nam định',
                        'yên nam, duy tiên, hà nam' => 'hà nam',
                        'xã nguyên lý/huyện lý nhân/tỉnh hà nam' => 'hà nam',
                        'phường năng tĩnh/ thành phố nam định/tỉnh nam định' => 'nam định',
                        'vĩnh yên vĩnh phúc' => 'vĩnh phúc',
                        'liên nghĩa, văn giang, hưng yên' => 'hưng yên',
                        'hạ đình - thanh xuân - hà nội' => 'hà nội',
                        'đồng minh - vĩnh bảo - hải phòng' => 'hải phòng',
                        'lý nhân, hà nam' => 'hà nam',
                        'hòa tiến, hưng hà, thái bình' => 'thái bình',
                        'phú xuyên - hà nội' => 'hà nội',
                        'mỹ thành, mỹ lộc, nam định' => 'nam định',
                        'xóm nam đồng, xã tân cương, thành phố thái nguyên, tỉnh thái nguyên' => 'thái nguyên',
                        'hoài đức, hà nội' => 'hà nội',
                        'quỳnh giao, quỳnh phụ, thái bình' => 'thái bình',
                        'mai đình, sóc sơn, hà nội' => 'hà nội',
                        'diên khánh, khánh hòa' => 'khánh hòa',
                        'diên khánh- khánh hòa' => 'khánh hòa',
                        'thành phố nam định' => 'nam định',
                        'đồngtháp' => 'đồng tháp',
                        'tỉnh nghệ tĩnh' => 'nghệ an',
                        'nghệ tĩnh' => 'nghệ an',
                        'quảngnam' => 'quảng nam',
                        'tỉnh yên bái' => 'yên bái',
                        'hcm' => 'tp.hồ chí minh',
                        'tỉnh khánh hòa' => 'khánh hòa',
                        'hải dươg' => 'hải dương',
                        'tp.cần thơ' => 'cần thơ',
                        'tỉnh vĩnh phú' => 'phú thọ',
                        'tp.cần thơ' => 'cần thơ',
                        'tỉnh hậu giang' => 'hậu giang',
                    ];                                       
                          
                    $place = $replace_map[$place] ?? $place;
                    $array_province = $db->query("SELECT id FROM " . NV_PREFIXLANG . "_location_province WHERE title LIKE " . $db->quote($place))->fetch();
                    $item['idprovince'] = isset($array_province['id']) ? $array_province['id'] : 0;
                    if ($item['idprovince'] == 0 && !empty($place)) {
                        $log_file = NV_ROOTDIR . '/certificate/province_not_found.log';
                        $log_content = date('[Y-m-d H:i:s]') . " " . $place . PHP_EOL;
                        file_put_contents($log_file, $log_content, FILE_APPEND);
                    }
                    $unique_hash = hash('sha256', $item['certificateNo'] . $item['idNo']);
                    $check_stmt = $db->prepare("SELECT courses_id FROM nv4_certificate_students WHERE unique_hash = :unique_hash");
                    $check_stmt->bindParam(':unique_hash', $unique_hash, PDO::PARAM_STR);
                    $check_stmt->execute();
                    $exists = $check_stmt->fetchColumn();
                    $json_item = json_encode($item, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                    if ($exists === false) {
                        $stmt1 = $db->prepare('INSERT INTO nv4_certificate_students (id_msc, courses_id, certificate_code, student_name, birthday, place, id_province, identify_code, result, status, issuedate, createdby, id_training, unique_hash, json_encode) VALUES (:id_msc, :courses_id, :certificate_code, :student_name, :birthday, :place, :id_province, :identify_code, :result, :status, :issuedate, :createdby, :id_training, :unique_hash, :json_encode)');
                        $stmt1->bindParam(':id_msc', $item['id'], PDO::PARAM_INT);
                        $stmt1->bindParam(':courses_id', $result_data['id'], PDO::PARAM_STR);
                        $stmt1->bindParam(':certificate_code', $item['certificateNo'], PDO::PARAM_STR);
                        $stmt1->bindParam(':student_name', $item['fullName'], PDO::PARAM_STR);
                        $stmt1->bindParam(':birthday', $item['birthDate'], PDO::PARAM_STR);
                        $stmt1->bindParam(':place', $item['birthPlace'], PDO::PARAM_STR);
                        $stmt1->bindParam(':id_province', $item['idprovince'], PDO::PARAM_INT);
                        $stmt1->bindParam(':identify_code', $item['idNo'], PDO::PARAM_STR);
                        $stmt1->bindParam(':result', $arr_cerrank[$item['cerRank']], PDO::PARAM_STR);
                        $stmt1->bindParam(':status', $item['status'], PDO::PARAM_STR);
                        $stmt1->bindParam(':issuedate', $result_data['certificate_date'], PDO::PARAM_STR);
                        $stmt1->bindParam(':createdby', $item['createdBy'], PDO::PARAM_STR);
                        $stmt1->bindParam(':id_training', $result_data['id_training'], PDO::PARAM_INT);
                        $stmt1->bindParam(':unique_hash', $unique_hash, PDO::PARAM_STR);
                        $stmt1->bindParam(':json_encode', $json_item, PDO::PARAM_STR);
                        $exc = $stmt1->execute();
                        echo "\n insert unique_hash = : " . $unique_hash . " <br/>";
                        ++$new_data;
                    } else {
                        if (strpos($exists, ',') !== false) {
                            $courses = explode(',', $exists);
                        } else {
                            $courses = [$exists];
                        }

                        $courses = array_map('intval', $courses);

                        if (!in_array((int)$result_data['id'], $courses)) {
                            $courses[] = (int)$result_data['id'];
                        }

                        $updated_courses = implode(',', $courses);
                        $stmt = $db->prepare('UPDATE nv4_certificate_students SET courses_id=:courses_id, certificate_code=:certificate_code, student_name=:student_name, birthday=:birthday, place=:place, id_province=:id_province, identify_code=:identify_code, result=:result, status=:status, issuedate=:issuedate, createdby=:createdby,  id_training=:id_training,  json_encode=:json_encode WHERE unique_hash = ' . $db->quote($unique_hash));
                        $stmt->bindParam(':courses_id',  $updated_courses, PDO::PARAM_STR);
                        $stmt->bindParam(':certificate_code', $item['certificateNo'], PDO::PARAM_STR);
                        $stmt->bindParam(':student_name', $item['fullName'], PDO::PARAM_STR);
                        $stmt->bindParam(':birthday', $item['birthDate'], PDO::PARAM_STR);
                        $stmt->bindParam(':place', $item['birthPlace'], PDO::PARAM_STR);
                        $stmt->bindParam(':id_province', $item['idprovince'], PDO::PARAM_INT);
                        $stmt->bindParam(':identify_code', $item['idNo'], PDO::PARAM_STR);
                        $stmt->bindParam(':result', $arr_cerrank[$item['cerRank']], PDO::PARAM_STR);
                        $stmt->bindParam(':status', $item['status'], PDO::PARAM_STR);
                        $stmt->bindParam(':issuedate', $result_data['certificate_date'], PDO::PARAM_STR);
                        $stmt->bindParam(':createdby', $item['createdBy'], PDO::PARAM_STR);
                        $stmt->bindParam(':id_training', $result_data['id_training'], PDO::PARAM_INT);
                        $stmt->bindParam(':json_encode', $json_item, PDO::PARAM_STR);
                        $exc = $stmt->execute();
                        echo "\n Update thành công \n unique_hash = " . $unique_hash . " <br/>";
                    }
                }
                $db->query("UPDATE `nv4_certificate_courses` SET url_run='1', total_pages = 0, number_student = " . $data['totalElements'] . " WHERE id=" . $result_data['id']);
                echo "\n Số tin mới: " . $new_data . " pageNumber: " . $pageNumber . "/" . $totalPages . "<br/>";
            } else if ($data['totalElements'] === 0) {
                $db->query("UPDATE `nv4_certificate_courses` SET url_run='-2', total_pages = 0, number_student = " . $data['totalElements'] . " WHERE id=" . $result_data['id']);
            } else {
                exit(2);
            }
            ++$pageNumber;
        } while ($pageNumber <= $totalPages);
    } else {
        echo "Đã cập nhật xong";
        exit(1);
    }
} catch (PDOException $e) {
    if (!empty($result_data)) {
        $db->query("UPDATE `nv4_certificate_courses` SET url_run='-1' , total_pages = " . intval($pageNumber) . " WHERE id=" . $result_data['id']);
    }
    print_r($e);
    trigger_error(print_r($e, true), 256);
}
echo "Thoi gian thuc hien = " . number_format((microtime(true) - NV_START_TIME), 3, '.', '');

function getdata($id_courses, $id_msc, $pageNumber, $reload = 1)
{
    global $db, $dbcr, $num_run;
    ++$num_run;
    $number_new = 0;
    $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-tender-certificate/services/list-student-of-class';
    $body = '{
        "pageSize": 20,
        "pageNumber": ' . $pageNumber . ',
        "student": {
            "idClass": "' . $id_msc . '"
        }
    }';
    $referer = 'https://muasamcong.mpi.gov.vn/web/guest/student-list?p_p_id=egpportaltendercertificate_WAR_egpportaltendercertificate&p_p_lifecycle=0&p_p_state=normal&p_p_mode=view&_egpportaltendercertificate_WAR_egpportaltendercertificate_render=detail-system-trainer&id=' . $id_msc;

    $arr_agent = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:104.0) Gecko/20100101 Firefox/104.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/105.0.1343.42',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 9.0; SAMSUNG SM-F900U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.77 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ];
    srand((float) microtime() * 10000000);
    $rand = array_rand($arr_agent);
    $agent = $arr_agent[$rand];
    $ch = curl_init();

    if (defined('USE_PROXY')) {
        $_proxy = $dbcr->query("SELECT * FROM `nv4_proxy` WHERE `status`=2 ORDER BY `lasttime` ASC LIMIT 1")->fetch();
        if (isset($_proxy['proxy'])) {
            $dbcr->query("UPDATE `nv4_proxy` SET `lasttime` = " . time() . ", number_run=number_run+1 WHERE `id` = " . $_proxy['id']);
            echo $_proxy['proxy'] . "\n";
            curl_setopt($ch, CURLOPT_PROXY, $_proxy['proxy']);
            if (isset($_proxy['proxyuserpwd']) and !empty($_proxy['proxyuserpwd'])) {
                curl_setopt($ch, CURLOPT_PROXYUSERPWD, $_proxy['proxyuserpwd']);
            }
            curl_setopt($ch, CURLOPT_PROXYTYPE, $_proxy['proxytype']);
            curl_setopt($ch, CURLOPT_PROXYPORT, $_proxy['proxyport']);
        }
    }
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 20);
    curl_setopt($ch, CURLOPT_TIMEOUT, 20);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
    curl_setopt($ch, CURLOPT_REFERER, $referer);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json'
    ));

    $json = curl_exec($ch);
    curl_close($ch);
    
    $data_student = json_decode($json, true);
    if ($data_student['totalElements'] > 0 && !empty($data_student['content'])) {
        return $data_student;
    } elseif (!empty($data_student['content']) && $reload && $num_run < 5) {
        return getdata($id_courses, $id_msc, $pageNumber, $reload = 1);
    } elseif ($data_student['totalElements'] === 0) {
        return ['content' => [], 'totalElements' => 0, 'totalPages' => 0];
    } else {
        echo "msc khóa học lỗi kết nối.";
        return [];
    }
}
echo "Thoi gian thuc hien = " . number_format((microtime(true) - NV_START_TIME), 3, '.', '');
ob_end_flush();