---
description: 
globs: 
alwaysApply: false
---
---
description: 
globs: 
alwaysApply: false
---
# C<PERSON>u trúc viết module của NukeViet

## 1. Source code sẽ nằm trong thư mục src
Các module của NukeViet được đặt trong thư mục `modules/`. Tên mỗi module bao gồm chữ cái, chữ số và dấu gạch ngang. Cấu trúc cơ bản của một module bao gồm các file và thư mục:

- `admin`
- `admin/main.php`
- `admin.functions.php`
- `admin.menu.php`
- `action_mysql.php`
- `action_oci.php`
- `blocks`
- `funcs`
- `funcs/main.php`
- `funcs/rss.php`
- `funcs/search.php`
- `comment.php`
- `notification.php`
- `functions.php`
- `global.functions.php`
- `language`
- `language/ngonngu.php` (ví dụ ngôn ngữ tiếng Việt: `vi.php`)
- `menu.php`
- `rssdata.php`
- `search.php`
- `siteinfo.php`
- `theme.php`
- `version.php`

## Chức năng của các file như sau:
- **File `version.php`**: File này có chức năng khai báo tiêu đề module, các funcs có block, tác giả module, thông tin phiên bản, cấu trúc thư mục trong thư mục `uploads`.
- **File `admin.functions.php`**: File này thường chứa các function, hằng dùng trong admin.
- **File `admin.menu.php`**: File này thường chứa biến `submenu` của module và `allow_func` của module.
- **File `functions.php`**: File này thường chứa các function, hằng dùng cho ngoài site.
- **File `global.functions.php`**: File này thường chứa các function, hằng dùng cho ngoài site.
- **File `admin/main.php`**: File này sẽ thể hiện nội dung của module phần admin.
- **File `funcs/main.php`**: File này sẽ thể hiện nội dung (trang chính) của module bên ngoài site.
- **File `action_mysql.php`**: Chứa các lệnh cài đặt CSDL ban đầu cho module. Áp dụng cho CSDL là MySQL.
- **File `action_oci.php`**: Chứa các lệnh cài đặt CSDL ban đầu cho module. Áp dụng cho các CSDL khác.
- **File `theme.php`**: Chứa các hàm xử lý giao diện của khu vực ngoài site.
- **File `siteinfo.php`**: Chứa thông tin module trong admin.
- **File `comment.php`**: File này không bắt buộc có, nếu có nó dùng để cập nhật lại số comment của đối tượng được bình luận khi module có dùng chức năng bình luận của hệ thống.
- **File `notification.php`**: File này không bắt buộc có, nếu có nó dùng để xác định tiêu đề và đường dẫn liên kết của chức năng thông báo trong admin.

## Thứ tự khởi động một module như sau:
Khi module được chạy thì tùy theo admin hay ngoài site mà file `admin.functions.php` hay file `functions.php` được chạy trước, sau đó là các file trong thư mục `admin` hay `funcs` được chạy tiếp theo, mặc định sẽ là file `main.php`.

Ta thường thấy URL trang web NukeViet (chưa bật rewrite) có dạng:
http://yourdomain/index.php?lang=vi&nv=qlhs&op=main


Trong đó:
- `lang` chính là ngôn ngữ của site.
- `nv` là module đang chạy.
- `op` chính là funcs đang chạy (ở đây là `main`). Giá trị `op` này chính là tên của funcs trong thư mục `funcs` hay `admin`. Nếu trên URL mà khuyết phần `op=`, có nghĩa funcs `main.php` đang được chạy.

## Các hằng cấu hình hệ thống
Các hằng cấu hình hệ thống được định nghĩa trong file `constants.php`. Ngoài ra, NukeViet còn định nghĩa một số hằng sau:

- **NV_START_TIME**: Thời gian bắt đầu phiên làm việc.
- **NV_ROOTDIR**: Thư mục gốc website, ví dụ: `D:/Web/www/nukeviet`.
- **NV_IS_USER_FORUM**: Có giá trị `true` nếu site có tích hợp diễn đàn.
- **NV_OPENID_ALLOWED**: Cho phép đăng nhập bằng OpenID.
- **NV_CURRENTTIME**: Thời gian hiện tại, giá trị bằng `time()`.
- **NV_DEL_ONLINE_TIME**: Thời gian xóa trạng thái online.
- **NV_CLIENT_IP**: IP của client.
- **NV_SERVER_NAME**: Tên máy chủ, ví dụ: `mydomain1.com`.
- **NV_SERVER_PROTOCOL**: Giao thức, ví dụ: `http`.
- **NV_SERVER_PORT**: Cổng, ví dụ: `80`.
- **NV_MY_DOMAIN**: Tên miền website, ví dụ: `http://mydomain1.com:80`.
- **NV_HEADERSTATUS**: Ví dụ: `HTTP/1.0`.
- **NV_USER_AGENT**: Thông tin useragent.
- **NV_BASE_SITEURL**: Tên thư mục gốc chứa file kèm theo ký tự `/` ở đầu và cuối. Nếu không có thư mục thì là ký tự `/`, ví dụ: `/nukeviet/` hoặc `/` hoặc `/nukeviet/nukeviet2/`.
- **NV_BASE_ADMINURL**: Đường dẫn đến admin, ví dụ: `/nukeviet/admin`.
- **NV_DOCUMENT_ROOT**: Đường dẫn tuyệt đối đến thư mục chứa web của server, ví dụ: `D:/AppServ/www`.
- **NV_EOL**: Ký tự ngắt dòng.
- **NV_UPLOAD_MAX_FILESIZE**: Kích thước tối đa file tải lên, tính bằng byte.
- **NV_UPLOADS_REAL_DIR**: Đường dẫn tuyệt đối đến thư mục chứa file.
- **NV_CACHE_PREFIX**: Tiếp đầu tố của cache.
- **NV_IS_AJAX**: Kiểm tra referer hệ thống hay bên ngoài, giá trị `true` hoặc `false`.
- **NV_IS_MY_USER_AGENT**: Xác định có phải User_Agent của NukeViet hay không.
- **NV_AUTHORS_GLOBALTABLE**: Tên table authors.
- **NV_GROUPS_GLOBALTABLE**: Tên table nhóm thành viên.
- **NV_USERS_GLOBALTABLE**: Tên table thành viên.
- **NV_SESSIONS_GLOBALTABLE**: Tên table lưu session.
- **NV_COOKIES_GLOBALTABLE**: Tên table lưu cookie.
- **NV_LANGUAGE_GLOBALTABLE**: Tên table lưu ngôn ngữ.
- **NV_BANNERS_CLIENTS_GLOBALTABLE**: Tên table chứa khách hàng phần quảng cáo.
- **NV_BANNERS_PLANS_GLOBALTABLE**: Tên table chứa kế hoạch quảng cáo.
- **NV_BANNERS_ROWS_GLOBALTABLE**: Tên table chứa quảng cáo.
- **NV_BANNERS_CLICK_GLOBALTABLE**: Tên table chứa thông tin thống kê quảng cáo.
- **NV_CONFIG_GLOBALTABLE**: Tên table cấu hình hệ thống.
- **NV_UPLOAD_GLOBALTABLE**: Tên table lưu các file upload.
- **NV_CRONJOBS_GLOBALTABLE**: Tên table lưu tiến trình tự động.
- **NV_PREFIXLANG**: Tiếp đầu tố + ngôn ngữ của table, ví dụ: `nv3_vi`.
- **NV_MODULES_TABLE**: Tên table lưu thông tin các module.
- **NV_BLOCKS_TABLE**: Tên table lưu thông tin các block.
- **NV_MODFUNCS_TABLE**: Tên table thiết lập các layout cho theme.
- **NV_COUNTER_TABLE**: Tên table chứa thông tin thống kê.
- **NV_SEARCHKEYS_TABLE**: Tên table chứa các từ khóa tìm kiếm.
- **NV_REFSTAT_TABLE**: Tên table thống kê các referer.
- **UPLOAD_CHECKING_MODE**: Kiểu kiểm tra file tải lên.
- **ADMIN_LOGIN_MODE**: Kiểu đăng nhập của admin.
- **PCLZIP_TEMPORARY_DIR**: Đường dẫn tuyệt đối đến thư mục chứa file nén tạm thời.
- **NV_IS_USER**: Có phải là thành viên không.
- **NV_IS_SPADMIN**: Có phải là admin tối cao hoặc người điều hành chung không.
- **NV_IS_GODADMIN**: Có phải là admin tối cao không.
- **NV_IS_MODADMIN**: Có phải là người quản trị hay không (bao gồm điều hành module, điều hành chung, quản trị tối cao).
- **NV_ALLOW_FILES_TYPE**: Các kiểu file được tải lên.
- **NV_ALLOW_UPLOAD_FILES**: Cho phép tải file lên.
- **NV_LANG_DATA**: Ngôn ngữ data.
- **NV_LANG_INTERFACE**: Ngôn ngữ giao diện.
- **NV_UPLOADS_DIR**: Tên thư mục upload.
- **SYSTEM_UPLOADS_DIR**: Tương tự `NV_UPLOADS_DIR`.
- **SYSTEM_CACHEDIR**: Tương tự `NV_CACHEDIR`.
- **NV_IP_DIR**: Thư mục chứa file IP.
- **NV_CACHEDIR**: Thư mục cache.
- **NV_ASSETS_DIR**: Thư mục assets.
- **NV_EDITORSDIR**: Thư mục editor.
- **NV_BANNER_DIR**: Thư mục file cấu hình quảng cáo.
- **NV_FILEHEAD**: Thông tin mô tả các file PHP.
- **NUKEVIET_STORE_APIURL**: Giá trị API cố định của NukeViet Store.
- **NV_ANTI_AGENT**: Nếu là `true`, báo hiệu hệ thống sẽ chặn các User Agent = `none`.
- **NV_ANTI_IFRAME**: Nếu là `true`, báo hiệu hệ thống sẽ cấm các chèn iframe.
- **NV_GFX_HEIGHT**: Chiều cao mã captcha.
- **NV_GFX_NUM**: Độ dài mã captcha.
- **NV_GFX_WIDTH**: Chiều rộng mã captcha.
- **NV_LIVE_COOKIE_TIME**: Thời gian một cookie tồn tại.
- **NV_LIVE_SESSION_TIME**: Thời gian tồn tại của session.
- **NV_MAX_HEIGHT**: Chiều cao tối đa ảnh được phép upload.
- **NV_MAX_WIDTH**: Chiều rộng tối đa ảnh được phép upload.
- **NV_SYSTEM**: Nếu hằng này được định nghĩa, có nghĩa đang ở khu vực ngoài site.
- **NV_CONFIG_FILENAME**: Vị trí file config của hệ thống.
- **NV_ADMINDIR**: Tên thư mục quản trị.
- **NV_DATADIR**: Tên thư mục data.
- **NV_LOGS_DIR**: Tên thư mục logs.
- **NV_TEMP_DIR**: Tên thư mục tmp.
- **NV_TEMPNAM_PREFIX**: Tiếp đầu tố của file tạm.
- **NV_ERRORLOGS_FILENAME**: Tên thư mục error_log.
- **NV_LOGS_EXT**: Tên phần mở rộng (ext) của file log.
- **NV_NAME_VARIABLE**: Định nghĩa biến request module name.
- **NV_OP_VARIABLE**: Định nghĩa biến request function của module.
- **NV_LANG_VARIABLE**: Định nghĩa biến request ngôn ngữ data.
- **ZLIB_OUTPUT_COMPRESSION_LEVEL**: Độ nén trang khi bật chế độ GZip.
- **NV_ONLINE_UPD_TIME**: Thời gian để tính thành viên online.
- **NV_REF_LIVE_TIME**: Thời gian lưu trữ referer.
- **NV_MIN_SEARCH_LENGTH**: Độ dài tối thiểu của khóa tìm kiếm.
- **NV_MAX_SEARCH_LENGTH**: Độ dài tối đa của khóa tìm kiếm.
- **NV_TITLEBAR_DEFIS**: Ký tự phân cách của tiêu đề site.
- **NV_ALLOW_REQUEST_MODS**: Các phương thức request được chấp nhận.
- **NV_REQUEST_DEFAULT_MODE**: Phương thức request mặc định.

## Thông tin định nghĩa một số trình duyệt và hệ điều hành:

### Browser Names
- `BROWSER_OPERA`: Opera
- `BROWSER_OPERAMINI`: Opera Mini
- `BROWSER_WEBTV`: WebTV
- `BROWSER_EXPLORER`: Internet Explorer
- `BROWSER_EDGE`: Microsoft Edge
- `BROWSER_POCKET`: Pocket Internet Explorer
- `BROWSER_KONQUEROR`: Konqueror
- `BROWSER_ICAB`: iCab
- `BROWSER_OMNIWEB`: OmniWeb
- `BROWSER_FIREBIRD`: Firebird
- `BROWSER_FIREFOX`: Firefox
- `BROWSER_ICEWEASEL`: Iceweasel
- `BROWSER_SHIRETOKO`: Shiretoko
- `BROWSER_MOZILLA`: Mozilla
- `BROWSER_AMAYA`: Amaya
- `BROWSER_LYNX`: Lynx
- `BROWSER_SAFARI`: Safari
- `BROWSER_IPHONE`: iPhone
- `BROWSER_IPOD`: iPod
- `BROWSER_IPAD`: iPad
- `BROWSER_CHROME`: Chrome
- `BROWSER_COCCOC`: Coc Coc
- `BROWSER_ANDROID`: Android
- `BROWSER_GOOGLEBOT`: GoogleBot
- `BROWSER_YAHOOSLURP`: Yahoo! Slurp
- `BROWSER_W3CVALIDATOR`: W3C Validator
- `BROWSER_BLACKBERRY`: BlackBerry
- `BROWSER_ICECAT`: IceCat
- `BROWSER_NOKIAS60`: Nokia S60 OSS Browser
- `BROWSER_NOKIA`: Nokia Browser
- `BROWSER_MSN`: MSN Browser
- `BROWSER_MSNBOT`: MSN Bot
- `BROWSER_BINGBOT`: Bing Bot

### Platform Names
- `PLATFORM_WIN`: Windows
- `PLATFORM_WIN10`: Windows 10
- `PLATFORM_WIN8`: Windows 8
- `PLATFORM_WIN7`: Windows 7
- `PLATFORM_WIN2003`: Windows 2003
- `PLATFORM_WINVISTA`: Windows Vista
- `PLATFORM_WINCE`: Windows CE
- `PLATFORM_WINXP`: Windows XP
- `PLATFORM_WIN2000`: Windows 2000
- `PLATFORM_APPLE`: Apple
- `PLATFORM_LINUX`: Linux
- `PLATFORM_OS2`: OS/2
- `PLATFORM_BEOS`: BeOS
- `PLATFORM_IPHONE`: iPhone
- `PLATFORM_IPOD`: iPod
- `PLATFORM_IPAD`: iPad
- `PLATFORM_BLACKBERRY`: BlackBerry
- `PLATFORM_NOKIA`: Nokia
- `PLATFORM_FREEBSD`: FreeBSD
- `PLATFORM_OPENBSD`: OpenBSD
- `PLATFORM_NETBSD`: NetBSD
- `PLATFORM_SUNOS`: SunOS
- `PLATFORM_OPENSOLARIS`: OpenSolaris
- `PLATFORM_ANDROID`: Android
- `PLATFORM_IRIX`: Irix
- `PLATFORM_PALM`: Palm

- **NV_MEMCACHED_HOST**: Tên host Memcached
- **NV_MEMCACHED_PORT**: Memcache port
- **NV_REDIS_HOST**: Redis cache host
- **NV_REDIS_PORT**: Redis cache post
- **NV_REDIS_PASSWORD**: Redis cache pass
- **NV_REDIS_DBINDEX**: Redis cache prefix
- **NV_REDIS_TIMEOUT**: Redis cache timeout
- **ADMIN_LOGIN_MODE**: Cấp bậc admin được phép đăng nhập: 
  - `1` ⇒ Quản trị tối cao được đăng nhập
  - `2` ⇒ Điều hành chung được đăng nhập
  - `3` ⇒ Tất cả các admin được đăng nhập
- **NV_IS_REWRITE_OBSOLUTE**: Nếu được định nghĩa và có giá trị `true`, hệ thống sẽ rewrite các link với đường dẫn tuyệt đối (chứa domain).
- **NV_REWRITE_EXTURL**: Nếu được định nghĩa bằng `true`, tức liên kết hiện tại đang rewrite với giá trị phần mở rộng ở cuối.
- **NV_IS_DRAG_BLOCK**: Nếu định nghĩa bằng `true`, tức admin đang bật kéo thả block.
- **NV_ALLOWED_HTML_TAGS**: Các mã HTML được chấp nhận trong request.
- **NV_ADMIN**: Hằng này định nghĩa tức đang thao tác trong admin.

## Sử dụng template trong module
Trong file `funcs` sử dụng file `main.tpl`. Để sử dụng template này thì trong thư mục `themes/' . $global_config['module_theme'] . '/modules/' . $module_file` tạo file `main.tpl`.

Để tách HTML và PHP ra thì cần khai báo file HTML (`*.tpl`) trong thư mục `themes/' . $global_config['module_theme'] . '/modules/' . $module_file`.

Ví dụ: Trong `funcs/main.php`:

```php
<?php

/**
 * @Project Module Nukeviet 4.x
 * <AUTHOR> (<EMAIL>)
 * @copyright 2014 J&A.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @createdate 08/10/2014 09:47
 */

if (!defined('NV_IS_MOD_QLHS'))
    die('Stop!!!');

$contents = nv_page_main_list($array_data1, $array_data2, . . . );

include (NV_ROOTDIR . "/includes/header.php");
echo nv_admin_theme($contents);
include (NV_ROOTDIR . "/includes/footer.php");

```
- **NV_ROOTDIR**: Thư mục gốc chứa mã nguồn NukeViet.
- **NV_IS_DRAG_BLOCK**: Hàm giao diện do người dùng định nghĩa trong file theme.php (nằm cùng cấp với version.php)

```php
<?php

/**
 * @Project Module Nukeviet 4.x
 * <AUTHOR> (<EMAIL>)
 * @copyright 2014 J&A.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @createdate 08/10/2014 09:47
 */

if (!defined('NV_IS_MOD_QLHS'))
    die('Stop!!!');

function nv_page_main_list($array_data1, $array_data2, . . . . )
{
    global $module_file, $lang_module, $lang_global, $module_info, $meta_property, $my_head, $client_info, $page_config, $module_name;

    $template = (file_exists(NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_file . '/main.tpl')) ? $module_info['template'] : 'default';

    $xtpl = new XTemplate('main.tpl', NV_ROOTDIR . '/themes/' . $template . '/modules/' . $module_file);
    $xtpl->assign('LANG', $lang_module);
    $xtpl->assign('GLANG', $lang_global);

    $xtpl->parse('main');
    return $xtpl->text('main');
}
?>

```tpl
<!-- BEGIN: main -->

<!-- END: main -->
```

```php
$result_array[] = array(
    'link' => $link,
    'title' => BoldKeywordInStr($title, $key, $logic),
    'content' => BoldKeywordInStr($content, $key, $logic)
);
```

## Các hàm được xây dựng trong NukeViet 4

### nv_aleditor
- **Vị trí**: `assets/editors/ckeditor/nv.php`
- **Chức năng**: Gọi ra trình soạn thảo Ckeditor
- **Cú pháp**:  
  `nv_aleditor($textareaname, $width = '100%', $height = '450px', $val = '', $customtoolbar = '', $path = '', $currentpath = '')`
- **Trong đó**:
  - `$textareaname`: ID của trình soạn thảo
  - `$width`: Chiều rộng trình soạn thảo, ví dụ `100%`, `100px`
  - `$height`: Chiều cao trình soạn thảo, ví dụ `300px`
  - `$val`: Nội dung soạn thảo
  - `$customtoolbar`: Giá trị tùy chỉnh các công cụ soạn thảo, để trống nếu mặc định hoặc tùy chỉnh theo `https://ckeditor.com/latest/samples/old/toolbar/toolbar.html`
  - `$path`: Đường dẫn upload được phép khi duyệt file, có thể để trống
  - `$currentpath`: Đường dẫn upload hiện tại khi duyệt file, có thể để trống

### nv_admin_checkip
- **Vị trí**: `includes/core/admin_access.php`
- **Chức năng**: Kiểm tra xem IP có bị cấm truy cập vào khu vực admin không. Trả về `true` nếu không bị cấm và `false` nếu bị cấm
- **Cú pháp**:  
  `nv_admin_checkip()`

### nv_admin_checkfirewall
- **Vị trí**: `includes/core/admin_access.php`
- **Chức năng**: Kiểm tra và hiển thị tường lửa truy cập khu vực admin
- **Cú pháp**:  
  `nv_admin_checkfirewall()`

### nv_admin_checkdata
- **Vị trí**: `includes/core/admin_access.php`
- **Chức năng**: Kiểm tra dữ liệu admin
- **Cú pháp**:  
  `nv_admin_checkdata($adm_session_value)`
- **Trong đó**:
  - `$adm_session_value`: Mảng chứa các field được lấy từ bảng `users`
- **Ghi chú**: Đây là hàm chỉ để sử dụng trong hệ thống

### nv_base64_encode
- **Chức năng**: Dùng để mã hóa một chuỗi
- **Cú pháp**:  
  `nv_base64_encode($input)`
- **Ví dụ**:  
  `nv_base64_encode("http://nukeviet.vn")`  
  **Kết quả**: `aHR0cDovL251a2V2aWV0LnZu`

### nv_base64_decode
- **Chức năng**: Ngược với hàm `nv_base64_encode`
- **Cú pháp**:  
  `nv_base64_decode($input)`
- **Ví dụ**:  
  `nv_base64_decode("aHR0cDovL251a2V2aWV0LnZu")`  
  **Kết quả**: `http://nukeviet.vn`

### nv_check_valid_email
- **Cú pháp**:  
  `nv_check_valid_email($mail, $return = false)`
- **Ghi chú**: `$return` được thêm vào kể từ phiên bản 4.3.08
- **Chức năng**: Trả về giá trị rỗng nếu email hợp lệ và trả về thông báo lỗi nếu email không hợp lệ. Thường dùng để kiểm tra tính hợp lệ của email khi nhập vào. Nếu tham số `$return` được đặt lên khác `false` thì dữ liệu trả về là mảng một chiều gồm hai phần tử: phần tử `0` là giá trị kiểm tra trong trường hợp `$return = false` và phần tử `1` là email đã được chuyển từ Unicode sang ASCII

### nv_set_allow
- **Cú pháp**:  
  `nv_set_allow($who, $groups)`
- **Ghi chú**: Hàm này đã bị loại khỏi NukeViet 4

### nv_date
- **Cú pháp**:  
  `nv_date($format, $time = 0)`
- **Trong đó**:
  - `$format`: Định dạng ngày tương tự cú pháp của hàm `date` trong PHP, xem tại `http://php.net/manual/en/function.date.php`
  - `$time`: Giá trị của ngày
- **Ghi chú**: Cần biết thêm hằng `NV_CURRENTTIME`
- **Ví dụ**:  
  `nv_date("H:i d/m/Y", NV_CURRENTTIME)`  
  **Kết quả**: `20:10 06/05/2011`

### nv_htmlspecialchars
- **Cú pháp**:  
  `nv_htmlspecialchars($string)`
- **Chức năng**: Chuyển đổi các ký tự đặc biệt thành chuỗi:
  - `&` => `&amp;`
  - `\'` => `&#039;`
  - `"` => `&quot;`
  - `<` => `&lt;`
  - `>` => `&gt;`
  - `\\` => `\`
  - `/` => `/`
  - `(` => `(`
  - `)` => `)`
  - `*` => `*`
  - `[` => `[`
  - `]` => `]`
  - `!` => `!`
  - `=` => `=`
  - `#` => `#`
  - `%` => `%`
  - `^` => `^`
  - `:` => `:`
  - `{` => `{`
  - `}` => `}`
  - ``` => `&quot;`
  - `~` => `~`
- **Trong đó**:
  - `$string`: Có thể là một chuỗi ký tự hoặc một mảng một chiều chứa các chuỗi ký tự
- **Tham khảo**: Bảng mã hóa các ký tự đặc biệt: `http://htmlhelp.com/reference/html40/entities/special.html`

### nv_unhtmlspecialchars
- **Chức năng**: Ngược lại với hàm `nv_htmlspecialchars`
- **Cú pháp**:  
  `nv_unhtmlspecialchars($string)`

### nv_nl2br
- **Cú pháp**:  
  `nv_nl2br($text, $replacement = '<br />')`
- **Chức năng**: Dùng để chuyển các ký tự xuống dòng thành `<br />` (trong HTML), thường dùng để xuống dòng khi lấy dữ liệu từ textarea
- **Ví dụ**:  
  `nv_nl2br("Nuke\nViet")`  
  **Kết quả**: `Nuke<br />Viet`

### nv_br2nl
- **Cú pháp**:  
  `nv_br2nl($text)`
- **Chức năng**: Ngược lại với hàm `nv_nl2br`

### nv_editor_nl2br
- **Cú pháp**:  
  `nv_editor_nl2br($text)`
- **Chức năng**: Tương tự `nv_nl2br` nhưng dùng trong trường hợp dữ liệu lấy từ EDITOR

### nv_editor_br2nl
- **Cú pháp**:  
  `nv_editor_br2nl($text)`
- **Chức năng**: Ngược lại với `nv_editor_nl2br`
- **Ghi chú**: Các hàm `nv_editor_br2nl`, `nv_editor_nl2br`, `nv_br2nl`, `nv_nl2br` thường được dùng trong khi lấy dữ liệu từ EDITOR hay textarea rồi lưu vào cơ sở dữ liệu rồi đưa ngược từ CSDL ra EDITOR hay textarea (ví dụ viết một bài viết và sửa một bài viết nào đó)

### filter_text_input
- **Chức năng**: Lấy dữ liệu được submit
- **Ví dụ**:  
  `filter_text_input('alias', 'post', 'GIA TRI MAC DINH')`
- **Ghi chú**: Hàm này đã bị loại khỏi NukeViet 4

### filter_text_textarea
- **Ghi chú**: Hàm này đã bị loại khỏi NukeViet 4

### nv_editor_filter_textarea
- **Ghi chú**: Hàm này đã bị loại khỏi NukeViet 4

### nv_sendmail
- **Chức năng**: Gửi email
- **Cú pháp**:  
  `nv_sendmail($from, $to, $subject, $message, $files = '', $AddEmbeddedImage = false)`
- **Trong đó**:
  - `$from`: Xem thêm cách dùng bên dưới
  - `$to`: Địa chỉ email nhận
  - `$subject`: Tiêu đề của email
  - `$message`: Nội dung email, có thể có định dạng HTML
  - `$files`: Địa chỉ tập tin đính kèm (có thể bỏ trống)
  - `$AddEmbeddedImage`: Nếu khác `false`, hệ thống sẽ tự động thêm logo của site vào cuối email như là chữ ký. File logo này được cấu hình tại phần Cấu hình site
- **Ví dụ**:  
  `nv_sendmail(array("VINADES", "<EMAIL>"), "<EMAIL>", "Tiêu đề", "<strong>Nội dung</strong>");`
- **Cách sử dụng biến `$from`**:
  - `$from` là `string`: String đó là địa chỉ email reply
  - `$from` là mảng có 2 phần tử:  
    `$from = ['Tên người reply', '<EMAIL>'];`
  - `$from` là mảng có 4 phần tử (khả dụng từ bản 4.4.00 về sau):  
    `$from = ['Tên người reply', '<EMAIL>', 'Tên người gửi', '<EMAIL>'];`
- **Lưu ý**:
  - Thông số email người gửi có thể không có tác dụng khi gửi tới Gmail và một số máy chủ khác. Trường hợp đó máy chủ tự tìm ra email gửi chính xác để hiển thị
  - Từ bản 4.4.00 về sau, có thể bỏ trống biến `$from`, hệ thống sẽ tự lấy từ email, tên của site

### nv_generate_page
- **Chức năng**: Xuất số trang
- **Cú pháp**:  
  `nv_generate_page($base_url, $all_page, $per_page, $page);`
- **Trong đó**:
  - `$base_url`: Link cơ bản khi ấn vào một trang, ví dụ `nukeviet.vn/`, khi ấn vào trang link sẽ thêm `page=So-trang`, dùng `$nv_Request->get_int('page', 'get', 1);` để lấy
  - `$all_page`: Tổng số phần tử, ví dụ hai mươi bài hát
  - `$per_page`: Số phần tử trên một trang, ví dụ 5 bài trên một trang
  - `$page`: Trang thứ mấy, ví dụ trang 2. Dữ liệu xuất sẽ là một đoạn mã HTML bao gồm các link để ấn vào, chỉ cần xuất ra trình duyệt trực tiếp. Giá trị này nhỏ nhất là `1`
- **Nâng cao**:  
  `nv_generate_page($base_url, $num_items, $per_page, $on_page, $add_prevnext_text = true, $onclick = false, $js_func_name = 'nv_urldecode_ajax', $containerid = 'generate_page')`
- **Trong đó**:
  - `$base_url`: Đường dẫn cơ bản khi ấn vào một liên kết
  - `$num_items`: Tổng số mục cần phân trang
  - `$per_page`: Số mục trên một trang
  - `$on_page`: Trang hiện tại (nhỏ nhất là `1`)
  - `$add_prevnext_text`: Mang giá trị `true`, `false`, bật hoặc tắt hai liên kết "trang trước" và "trang sau"
  - `$onclick`: Mang giá trị `true`, `false`. Nếu giá trị là `true` thì liên kết sẽ được thêm `onclick="$js_func_name"`
  - `$js_func_name`: Tên hàm được gọi khi ấn vào liên kết
  - `$containerid`: ID của đối tượng được truyền vào hàm `$js_func_name`

### nv_is_url
- **Cú pháp**:  
  `nv_is_url($url)`
- **Chức năng**: Kiểm tra một địa chỉ có phải là địa chỉ web đúng hay không, giá trị trả về là `true` nếu đúng và `false` nếu sai
- **Ví dụ**:  
  `nv_is_url("http://nukeviet.vn")`  
  **Kết quả**: `true`

### nv_check_url
- **Cú pháp**:  
  `nv_check_url($url, $is_200 = 0)`
- **Chức năng**: Kiểm tra sự tồn tại của địa chỉ `$url` sau thời gian hết hạn thực thi, giá trị trả về là `true` hoặc `false`
- **Ví dụ**:  
  `nv_check_url("http://nukeviet.vn")`  
  **Kết quả**: `true`

### nv_insert_logs
- **Chức năng**: Dùng để lưu lại lịch sử một công việc nào đó
- **Ví dụ**: Câu lệnh lưu lại là đã có một quản trị viên xóa một chuyên mục sẽ viết như sau:  
  `nv_insert_logs(NV_LANG_DATA[Ngôn ngữ], $module_name[tên module hiện đang dùng], $lang_module['file_addfile'][Tên công việc], $array['title'][Nội dung công việc], $admin_info['userid'][ID của thành viên], $link[Link để truy cập nếu có]);`

### nv_site_mods
- **Cú pháp**:  
  `nv_site_mods()`
- **Chức năng**: Trả về danh sách cấu hình các module hiện đang sử dụng, giá trị trả về có dạng:
```php
Array
(
    [about] => Array
        (
            [module_file] => page
            [module_data] => about
            [module_upload] => about
            [custom_title] => Giới thiệu
            [admin_title] => Giới thiệu
            [admin_file] => 1
            [main_file] => 1
            [theme] => 
            [mobile] => 
            [description] => 
            [keywords] => 
            [groups_view] => 6
            [is_modadmin] => 
            [admins] => 
            [rss] => 1
            [gid] => 0
            [funcs] => Array
                (
                    [sitemap] => Array
                        (
                            [func_id] => 2
                            [func_name] => sitemap
                            [show_func] => 0
                            [func_custom_name] => Sitemap
                            [in_submenu] => 0
                        )
                    [rss] => Array
                        (
                            [func_id] => 3
                            [func_name] => rss
                            [show_func] => 0
                            [func_custom_name] => Rss
                            [in_submenu] => 0
                        )
                    [main] => Array
                        (
                            [func_id] => 1
                            [func_name] => main
                            [show_func] => 1
                            [func_custom_name] => Main
                            [in_submenu] => 0
                        )
                )
            [alias] => Array
                (
                    [sitemap] => sitemap
                    [rss] => rss
                    [main] => main
                )
        )
    [news] => Array
        (
        )
)
```
### nv_groups_list
- **Cú pháp**:  
  `nv_groups_list($mod_data = 'users')`
- **Chức năng**: Trả về danh sách các nhóm thành viên trong hệ thống. Mặc định sẽ trả về của module `users`, thay đổi `$mod_data` bằng module ảo của module `users` để trả về nhóm của module đó.

### nv_info_die
- **Cú pháp**:  
  `nv_info_die($page_title = ""[Tiêu đề], $info_title[Thông tin1], $info_content[Thông tin2], $error_code[Mã lỗi HTTP], $adminlink = 0[Cho phép hiển thị link đến khu vực quản trị])`
- **Chức năng**: Thường dùng để thông báo lỗi và kết thúc, ví dụ báo lỗi 404 - trang web bạn cố gắng truy cập hiện không tồn tại.

### nv_ImageInfo
- **Chức năng**: Hàm tạo ảnh thumb
- **Cú pháp**:  
  `nv_ImageInfo($original_name, $width = 0, $is_create_thumb = false, $thumb_path = '')`
- **Trong đó**:
  - `$original_name`: Đường dẫn tuyệt đối đến file ảnh
  - `$width`: Chiều rộng ảnh thumb
  - `$is_create_thumb`: Nếu có giá trị `true` thì hệ thống sẽ tạo ảnh thumb nếu ảnh gốc có kích thước lớn hơn ảnh thumb
  - `$thumb_path`: Đường dẫn tuyệt đối đến thư mục chứa ảnh thumb

### change_alias
- **Chức năng**: Tạo liên kết tĩnh
- **Cú pháp**:  
  `change_alias($title)`
- **Ví dụ**:  
  `change_alias("Tiêu đề")`  
  **Kết quả**: `Tieu-de`

### nv_clean60
- **Chức năng**: Cắt ngắn một chuỗi
- **Cú pháp**:  
  `nv_clean60($string, $num = 60, $specialchars = false)`
- **Trong đó**:
  - `$string`: Chuỗi cần cắt
  - `$num`: Số ký tự
  - `$specialchars`: Nếu là `true` thì chuỗi trả về sẽ được xử lý qua hàm `nv_htmlspecialchars`
- **Ví dụ**:  
  `nv_clean60("Nukeviet là phần mềm nguồn mở được nhiều người ưa thích", 10)`  
  **Kết quả**: `Nukeviet là phần ...`

### nv_db_cache
- **Ghi chú**: Hàm này đã bị loại khỏi NukeViet 4, tham khảo `$nv_Cache->db`

### nv_del_moduleCache
- **Ghi chú**: Hàm này đã bị loại khỏi NukeViet 4, tham khảo `$nv_Cache->delMod`

### nv_loadUploadDirList
- **Ghi chú**: Hàm này đã bị loại khỏi NukeViet 4

### nv_url_rewrite
- **Cú pháp**:  
  `nv_url_rewrite($buffer, $is_url = false)`
- **Chức năng**: Trả về đường dẫn rewrite từ đường dẫn chưa rewrite nếu giá trị `$is_url` là `true`

### nv_get_keywords
- **Cú pháp**:  
  `nv_get_keywords($content, $keyword_limit = 20)`
- **Chức năng**: Trả về từ khóa từ `$content`, giới hạn tối đa bởi `$keyword_limit`

### nv_genpass
- **Cú pháp**:  
  `nv_genpass($length = 8, $type = 0)`
- **Chức năng**: Trả về một đoạn mã ngẫu nhiên có `$length` ký tự. Giá trị của `$type` quy định phạm vi ký tự:
  - `0`: Chữ cái thường và số
  - `2`: Chữ cái thường, chữ in hoa và số
  - `3`: Chữ cái thường, ký tự đặc biệt và số
  - `4`: Chữ cái thường, chữ in hoa, số và ký tự đặc biệt

### nv_capcha_txt
- **Cú pháp**:  
  `nv_capcha_txt($seccode)`
- **Chức năng**: Trả về `true` nếu mã captcha `$seccode` là hợp lệ, ngược lại trả về `false`

### nv_get_cache
- **Ghi chú**: Hàm này đã bị loại khỏi NukeViet 4, tham khảo `$nv_Cache->getItem`

### nv_set_cache
- **Ghi chú**: Hàm này đã bị loại khỏi NukeViet 4, tham khảo `$nv_Cache->setItem`

### nv_scandir
- **Cú pháp**:  
  `nv_scandir($directory, $pattern, $sorting_order = 0)`
- **Chức năng**: Tương tự hàm `scandir`

### nv_getextension
- **Cú pháp**:  
  `nv_getextension($filename)`
- **Chức năng**: Trả về kiểu file của `$filename`

### nv_mkdir
- **Cú pháp**:  
  `nv_mkdir($path, $dir_name)`
- **Chức năng**: Tạo thư mục `$dir_name` trong thư mục `$path`

### nv_deletefile
- **Cú pháp**:  
  `nv_deletefile($file, $delsub = false)`
- **Chức năng**: Xóa file hoặc thư mục `$file`. Nếu đối tượng xóa là thư mục và giá trị `$delsub` là `true` thì hàm chỉ xóa các thư mục rỗng

### nv_copyfile
- **Cú pháp**:  
  `nv_copyfile($file, $newfile)`
- **Chức năng**: Copy `$file` sang `$newfile`

### nv_renamefile
- **Cú pháp**:  
  `nv_renamefile($file, $newname)`
- **Chức năng**: Đổi tên file `$file` thành `$newname`

### nv_chmod_dir
- **Cú pháp**:  
  `nv_chmod_dir($conn_id, $dir, $subdir = false)`
- **Chức năng**: CHMOD thư mục `$dir` và các thư mục con nếu `$subdir` có giá trị `true`. `$conn_id` là connection FTP (`ftp_connect()`)

### nv_error_info
- **Cú pháp**:  
  `nv_error_info()`
- **Chức năng**: Trả về các thông báo lỗi hệ thống

### nv_rss_generate
- **Cú pháp**:  
  `nv_rss_generate($channel, $items)`
- **Chức năng**: Xuất RSS

### nv_html_meta_tags
- **Vị trí**: `includes/core/user_functions.php`
- **Chức năng**: Xuất các meta tags của site
- **Cú pháp**:  
  `nv_html_meta_tags($html = true)`
- **Trong đó**:
  - `$html`: Nếu đặt là `true`, nội dung xuất ra sẽ được chuyển thành chuỗi, ngược lại dữ liệu trả về là mảng chứa các meta tags

### nv_is_file
- **Cú pháp**:  
  `nv_is_file($file_fullpath, $array_allow_dirs)`
- **Chức năng**: Kiểm tra một file `$file_fullpath` có tồn tại hay không trong một hoặc nhiều thư mục cho phép `$array_allow_dirs`. Kết quả trả về `true` nếu file tồn tại và `false` nếu file không tồn tại
- **Trong đó**:
  - `$file_fullpath`: Đường dẫn tới file cần kiểm tra, được lấy trực tiếp từ giá trị trả về của hàm JavaScript `nv_open_browse()`, đường dẫn này có dạng: `NV_BASE_SITEURL . $folders . '/' . $filename`
  - **Ví dụ một số đường dẫn file**:
    - `/uploads/news/2015_12/image.jpg`
    - `/assets/images/loading.png`
    - `/modules/news/data/jquery.custom.js`
  - `$array_allow_dirs`: Thư mục cho phép của `$file_fullpath`, có thể là một thư mục, hoặc là mảng một chiều gồm nhiều thư mục cho phép. Nếu tham số này để trống trong lệnh gọi hàm, giá trị mặc định gồm hai thư mục cơ bản:
    - `uploads` // Thư mục chứa file upload
    - `assets/images` // Thư mục ảnh
  - **Chú ý**: Nếu giá trị là thư mục `A` thì tất cả các thư mục con của thư mục `A` cũng được xem là thư mục cho phép
  - **Ví dụ về đường dẫn file và thư mục truyền vào**:
    - `/uploads/news/2015_12/image.jpg` => `uploads/news/2015_12`
    - `/assets/images/loading.png` => `assets/images`
    - `/modules/news/data/jquery.custom.js` => `modules/news/data`

### nv_alias_page
- **Chức năng**: Phân trang có hỗ trợ rewrite
- **Cú pháp cơ bản**:  
  `nv_alias_page($title, $base_url, $num_items, $per_page, $on_page)`
- **Trong đó**:
  - `$title`: Nội dung tiêu đề của liên kết
  - `$base_url`: Đường dẫn cơ bản cho mỗi liên kết
  - `$num_items`: Tổng số kết quả
  - `$per_page`: Số kết quả 1 trang
  - `$on_page`: Trang hiện tại, tối thiểu là `1`
- **Nâng cao**:  
  `nv_alias_page($title, $base_url, $num_items, $per_page, $on_page, $add_prevnext_text = true, $full_theme = true)`
- **Trong đó**:
  - `$add_prevnext_text`: Xác định có hay không việc thêm vào nút trang trước, trang sau
  - `$full_theme`: Nếu mang giá trị `false`, hàm sẽ trả về chỉ các thẻ `<li>`, nếu là `true`, hàm trả về cả `<ul>`

### nv_object2array
- **Chức năng**: Chuyển từ kiểu object sang kiểu array
- **Cú pháp**:  
  `nv_object2array($a)`

### nv_convertfromBytes
- **Chức năng**: Chuyển từ đơn vị byte sang đơn vị lớn hơn thích hợp
- **Cú pháp**:  
  `nv_convertfromBytes($size)`
- **Ví dụ**:
  - `nv_convertfromBytes(1024)` // `1,024.00 bytes`
  - `nv_convertfromBytes(65464654)` // `62.43 MB`
  - `nv_convertfromBytes(65464654188)` // `60.97 GB`
  - `nv_convertfromBytes(6546465418865464654188)` // `5.55 ZB`

### nv_convertfromSec
- **Chức năng**: Chuyển từ số giây sang thời gian đọc được thích hợp
- **Cú pháp**:  
  `nv_convertfromSec($sec = 0)`
- **Ví dụ**:
  - `nv_convertfromSec(61)` // `1 phút 1 giây`
  - `nv_convertfromSec(3600)` // `1 giờ`
  - `nv_convertfromSec(86450)` // `1 ngày 50 giây`
  - `nv_convertfromSec(869751)` // `10 ngày 1 giờ 35 phút 51 giây`

### nv_converttoBytes
- **Chức năng**: Ngược lại với hàm `nv_convertfromBytes`
- **Cú pháp**:  
  `nv_convertfromSec($string)`

### nv_check_valid_login
- **Chức năng**: Kiểm tra giá trị username có hợp lệ hay không
- **Cú pháp**:  
  `nv_check_valid_login($login, $max, $min)`
- **Trong đó**:
  - `$login`: Username cần kiểm tra
  - `$max`: Số ký tự tối đa cho phép
  - `$min`: Số ký tự tối thiểu cho phép
- **Chức năng**: Trả về chuỗi rỗng nếu hợp lệ và chuỗi thể hiện lỗi nếu không hợp lệ

### nv_check_valid_pass
- **Chức năng**: Kiểm tra mật khẩu có hợp lệ hay không
- **Cú pháp**:  
  `nv_check_valid_pass($pass, $max, $min)`
- **Trong đó**:
  - `$pass`: Mật khẩu cần kiểm tra
  - `$max`: Số ký tự tối đa cho phép
  - `$min`: Số ký tự tối thiểu cho phép
- **Chức năng**: Trả về chuỗi rỗng nếu hợp lệ và chuỗi thể hiện lỗi nếu không hợp lệ

### nv_check_valid_email
- **Chức năng**: Kiểm tra email có hợp lệ hay không
- **Cú pháp**:  
  `nv_check_valid_email($mail)`
- **Chức năng**: Trả về chuỗi rỗng nếu hợp lệ và chuỗi thể hiện lỗi nếu không hợp lệ

### nv_user_in_groups
- **Chức năng**: Kiểm tra thành viên có thuộc ít nhất một trong các nhóm được chỉ định hay không
- **Cú pháp**:  
  `nv_user_in_groups($groups_view)`
- **Chức năng**: Giá trị trả về là `true` hoặc `false`
- **Ví dụ**:  
  `nv_user_in_groups("4,6,1,15")`

### nv_groups_add_user
- **Chức năng**: Thêm thành viên vào nhóm
- **Cú pháp**:  
  `nv_groups_add_user($group_id, $userid, $approved = 1, $mod_data = 'users')`
- **Trong đó**:
  - `$group_id`: ID của nhóm
  - `$userid`: ID của thành viên
  - `$approved`: Mang giá trị `0` hoặc `1`, có nghĩa là thành viên chưa được xác nhận hay đã được xác nhận vào nhóm
  - `$mod_data`: Giá trị `$module_data` của module chứa CSDL thành viên, mặc định là module `users`
- **Chức năng**: Trả về `true` nếu thành công và `false` nếu thất bại

### nv_groups_del_user
- **Chức năng**: Loại thành viên khỏi nhóm
- **Cú pháp**:  
  `nv_groups_del_user($group_id, $userid, $mod_data = 'users')`
- **Trong đó**:
  - `$group_id`: ID của nhóm
  - `$userid`: ID của thành viên
  - `$mod_data`: Giá trị `$module_data` của module chứa CSDL thành viên, mặc định là module `users`
- **Chức năng**: Trả về `true` nếu thành công và `false` nếu thất bại

### nv_show_name_user
- **Chức năng**: Lấy tên đầy đủ của thành viên
- **Cú pháp**:  
  `nv_show_name_user($first_name, $last_name, $user_name = '')`

### nv_get_redirect
- **Chức năng**: Lấy link chuyển hướng từ `$_GET['nv_redirect']` hoặc `$_POST['nv_redirect']`
- **Cú pháp**:  
  `nv_get_redirect($mode = 'post,get', $decode = false)`
- **Chức năng**: Trả về link chuyển hướng thực nếu `nv_redirect` hợp lệ hoặc rỗng nếu không hợp lệ

### nv_redirect_decrypt
- **Chức năng**: Giải mã link chuyển hướng
- **Cú pháp**:  
  `nv_redirect_decrypt($string, $insite = true)`
- **Chức năng**: Trả về link chuyển hướng thực nếu redirect được mã hóa đúng kiểu, hoặc rỗng nếu không hợp lệ

### nv_redirect_encrypt
- **Chức năng**: Mã hóa link chuyển hướng
- **Cú pháp**:  
  `nv_redirect_encrypt($url)`

### nv_insert_notification
- **Chức năng**: Lưu một thông báo vào CSDL. Thông báo này sẽ hiển thị cho admin biết lúc truy cập vào khu vực quản trị
- **Cú pháp cho bản trước 4.3.09**:  
  `nv_insert_notification($module, $type, $content = array(), $obid = 0, $send_to = 0, $send_from = 0, $area = 1)`
- **Trong đó**:
  - `$module`: Tên module đưa ra thông báo, thường là biến `$module_name`
  - `$type`: Chuỗi ký tự bất kỳ để tạo khóa cho thông báo. Ví dụ: "new_contact" cho thông báo khi có liên hệ mới, "new_comment" cho bình luận mới,...
  - `$content`: Mảng một chiều chứa các thông tin do người lập trình quy định, các thông tin này sẽ được lấy ra khi hiển thị thông báo
  - `$obid`: ID của hành động được thực hiện. Ví dụ: sau khi gửi 1 thư liên hệ, thư này sẽ được ghi vào CSDL với một ID
  - `$send_to`: Giá trị userID của người nhận, bằng `0` nếu muốn thông báo cho tất cả
  - `$send_from`: Giá trị userID của người gửi thông báo, bằng `0` nếu là thông báo từ hệ thống
  - `$area`: Khu vực thông báo: `0` là ngoài site, `1` là quản trị và `2` là ở cả hai khu vực
- **Cú pháp từ bản 4.3.09 về sau**:  
  `nv_insert_notification($module, $type, $content = [], $obid = 0, $send_to = 0, $send_from = 0, $area = 1, $admin_view_allowed = 0, $logic_mode = 0)`
- **Trong đó**:
  - `$module`: Tên module đưa ra thông báo, thường là biến `$module_name`
  - `$type`: Chuỗi ký tự bất kỳ để tạo khóa cho thông báo
  - `$content`: Mảng một chiều chứa các thông tin do người lập trình quy định
  - `$obid`: ID của hành động được thực hiện
  - `$send_to`: Giá trị userID của người nhận, để trống nếu muốn thông báo cho tất cả. Nếu có nhiều người nhận thì giá trị này là mảng một chiều chứa ID người nhận
  - `$send_from`: Giá trị userID của người gửi thông báo, bằng `0` nếu là thông báo từ hệ thống
  - `$area`: Khu vực thông báo: `0` là ngoài site, `1` là quản trị và `2` là ở cả hai khu vực
  - `$admin_view_allowed`: Có giá trị `0`, `1`, `2`. `0` tức là gửi cho toàn bộ, `1` là gửi cho quản trị tối cao, `2` là gửi cho điều hành chung
  - `$logic_mode`: Có giá trị `0` hoặc `1`. `0` thì quản trị cấp trên sẽ thấy thông báo quản trị cấp dưới, `1` thì chỉ có cấp đó mới được thấy thông báo
- **Lưu ý**:
  - Quản trị cấp trên sẽ xem được thông báo của quản trị cấp dưới nếu `logic_mode = 0`
  - Nếu giá trị `$send_to` được chỉ định thì chỉ có những người nhận được chỉ định trong cấp đó và cấp trên (nếu `logic_mode = 0`) xem được

### nv_status_notification
- **Chức năng**: Cập nhật trạng thái thông báo
- **Cú pháp**:  
  `nv_status_notification($language, $module, $type, $obid, $status = 1, $area = 1)`
- **Trong đó**:
  - `$module`, `$type`, `$obid`, `$area`: Là khóa của thông báo
  - `$language`: Ngôn ngữ chứa thông báo, mang giá trị của `NV_LANG_DATA`
  - `$status`: Trạng thái thông báo: `0` là chưa xem, `1` là đã xem

### nv_delete_notification
- **Chức năng**: Xóa thông báo
- **Cú pháp**:  
  `nv_delete_notification($language, $module, $type, $obid)`
- **Trong đó**: `$module`, `$type`, `$obid` là khóa của thông báo, `$language` là ngôn ngữ chứa thông báo

### nv_groups_post
- **Vị trí**: `includes/core/admin_functions.php`
- **Chức năng**: Kiểm tra logic phân quyền các nhóm, loại bỏ các nhóm không cần thiết và trả về các nhóm cần thiết
- **Cú pháp**:  
  `nv_groups_post($groups_view)`
- **Trong đó**:
  - `$groups_view`: Mảng chứa ID các nhóm phân quyền
- **Ghi chú**: Chỉ dùng hàm này trong khu vực quản trị

### nv_var_export
- **Vị trí**: `includes/core/admin_functions.php`
- **Chức năng**: Đưa tất cả chuỗi trả về của hàm `var_export` về một dòng
- **Cú pháp**:  
  `nv_var_export($var_array)`
- **Trong đó**:
  - `$var_array`: Biến cần xuất
- **Ghi chú**: Chỉ dùng hàm này đối với admin

### nv_save_file_config_global
- **Vị trí**: `includes/core/admin_functions.php`
- **Chức năng**: Xuất cấu hình trong CSDL ra file `config_global.php`
- **Cú pháp**:  
  `nv_save_file_config_global()`
- **Ghi chú**: Chỉ dùng hàm này đối với admin

### nv_geVersion
- **Vị trí**: `includes/core/admin_functions.php`
- **Chức năng**: Kiểm tra phiên bản NukeViet, trả về dữ liệu XML thông tin kiểm tra phiên bản
- **Cú pháp**:  
  `nv_geVersion($updatetime = 3600)`
- **Trong đó**:
  - `$updatetime`: Thời gian hết hạn để thực hiện load mới dữ liệu
- **Ghi chú**: Hàm này chỉ sử dụng trong hệ thống NukeViet

### nv_version_compare
- **Vị trí**: `includes/core/admin_functions.php`
- **Chức năng**: Tương đương hàm `version_compare`
- **Cú pháp**:  
  `nv_version_compare($version1, $version2)`
- **Ghi chú**: Chỉ sử dụng hàm này cho admin

### nv_check_rewrite_file
- **Vị trí**: `includes/core/admin_functions.php`
- **Chức năng**: Kiểm tra hệ thống có hỗ trợ rewrite không. Trả về `true` nếu có và `false` nếu không
- **Cú pháp**:  
  `nv_check_rewrite_file()`
- **Ghi chú**: Chỉ sử dụng cho admin

### nv_rewrite_change
- **Vị trí**: `includes/core/admin_functions.php`
- **Chức năng**: Ghi lại thông tin rewrite vào file `.htaccess` hoặc `.webconfig`
- **Cú pháp**:  
  `nv_rewrite_change($array_config_global)`
- **Trong đó**:
  - `$array_config_global` có dạng:
    ```php
    $array_config_rewrite = array(
        'rewrite_enable' => $global_config['rewrite_enable'],
        'rewrite_optional' => $global_config['rewrite_optional'],
        'rewrite_endurl' => $global_config['rewrite_endurl'],
        'rewrite_exturl' => $global_config['rewrite_exturl'],
        'rewrite_op_mod' => $global_config['rewrite_op_mod'],
        'ssl_https' => 0
    );

    ```
- **Ghi chú**: Chỉ sử dụng cho admin

### nv_http_get_lang
- **Vị trí**: `includes/core/admin_functions.php`
- **Chức năng**: Trả về ngôn ngữ lỗi từ error code class HTTP của NukeViet
- **Cú pháp**:  
  `nv_http_get_lang($input)`
- **Trong đó**:  
  - `$input`: Error code
- **Ghi chú**: Chỉ sử dụng cho admin

### nv_parse_ini_file
- **Vị trí**: `includes/core/filesystem_functions.php`
- **Chức năng**: Đọc file ini và trả về mảng dữ liệu
- **Cú pháp**:  
  `nv_parse_ini_file($filename, $process_sections = false)`
- **Trong đó**:  
  - `$filename`: File cần đọc
  - `$process_sections`: Nếu đặt là `true` thì các section sẽ được chọn làm key của mảng trả về

### nv_get_mime_type
- **Vị trí**: `includes/core/filesystem_functions.php`
- **Chức năng**: Xác định mime type của `$filename`
- **Cú pháp**:  
  `nv_get_mime_type($filename, $magic_path = '', $default_mime = 'application/octet-stream')`

### nv_get_allowed_ext
- **Vị trí**: `includes/core/filesystem_functions.php`
- **Chức năng**: Trả về mảng một chiều chứa các key là kiểu file, value là mime của file được phép
- **Cú pháp**:  
  `nv_get_allowed_ext($allowed_filetypes, $forbid_extensions, $forbid_mimes)`
- **Trong đó**:  
  - `$allowed_filetypes`: Loại file, được xác định trong file `includes/ini/mime.ini`
  - `$forbid_extensions`: Các kiểu file bị cấm
  - `$forbid_mimes`: Các mime bị cấm

### nv_string_to_filename
- **Vị trí**: `includes/core/filesystem_functions.php`
- **Chức năng**: Chuyển một chuỗi bất kỳ sang tên file hợp lệ
- **Cú pháp**:  
  `nv_string_to_filename($word)`
- **Trong đó**:  
  - `$word`: Chuỗi cần chuyển

### nv_pathinfo_filename
- **Vị trí**: `includes/core/filesystem_functions.php`
- **Chức năng**: Trả về thư mục chứa file
- **Cú pháp**:  
  `nv_pathinfo_filename($file)`
- **Trong đó**:  
  - `$file`: Full path tới file

### nv_ftp_del_dir
- **Vị trí**: `includes/core/filesystem_functions.php`
- **Chức năng**: Sử dụng FTP để xóa thư mục
- **Cú pháp**:  
  `nv_ftp_del_dir($ftp, $dst_dir, $delsub)`
- **Trong đó**:  
  - `$ftp`: Biến Class FTP
  - `$dst_dir`: Thư mục cần xóa
  - `$delsub`: Nếu đặt là `false` thì sẽ không xóa nếu thư mục có chứa thư mục con
- **Ghi chú**: Hàm này chỉ sử dụng trong hệ thống

### nv_is_image
- **Vị trí**: `includes/core/filesystem_functions.php`
- **Chức năng**: Kiểm tra file có phải là ảnh không và trả về thông tin nếu file là ảnh. Nếu file không phải là ảnh thì trả về `array()`
- **Cú pháp**:  
  `nv_is_image($img)`
- **Trong đó**:  
  - `$img`: Full path tới file cần kiểm tra
- **Dữ liệu trả về có dạng**:  
  - `$imageinfo['src'] = `
  - `$imageinfo['width'] = `
  - `$imageinfo['height'] = `
  - `$imageinfo['mime'] = `
  - `$imageinfo['type'] = `
  - `$imageinfo['ext'] = `
  - `$imageinfo['bits'] = `
  - `$imageinfo['channels'] = `

### nv_imageResize
- **Vị trí**: `includes/core/filesystem_functions.php`
- **Chức năng**: Trả về kích thước ảnh đúng tỉ lệ từ tham số `$maxX`, `$maxY`
- **Cú pháp**:  
  `nv_imageResize($origX, $origY, $maxX, $maxY)`
- **Trong đó**:  
  - `$origX`: Chiều rộng thực
  - `$origY`: Cao thực
  - `$maxX`: Chiều rộng tối đa cần resize
  - `$maxY`: Chiều cao tối đa cần resize

### nv_htmlOutput
- **Vị trí**: `includes/core/theme_functions.php`
- **Chức năng**: Sử dụng thay cho `die($content)`
- **Cú pháp**:  
  `nv_htmlOutput($html)`

### nv_jsonOutput
- **Vị trí**: `includes/core/theme_functions.php`
- **Chức năng**: Sử dụng thay cho `die(json_encode())`
- **Cú pháp**:  
  `nv_jsonOutput($array_data)`

### nv_xmlOutput
- **Vị trí**: `includes/core/theme_functions.php`
- **Chức năng**: Xuất ra trình duyệt file XML
- **Cú pháp**:  
  `nv_xmlOutput($content, $lastModified)`
- **Trong đó**:  
  - `$content`: Nội dung file XML
  - `$lastModified`: Thời gian thay đổi của file XML

### nv_xmlSitemap_generate
- **Vị trí**: `includes/core/theme_functions.php`
- **Chức năng**: Xuất file XML sitemap
- **Cú pháp**:  
  `nv_xmlSitemap_generate($url)`
- **Trong đó**:  
  - `$url`: Mảng chứa dữ liệu sitemap

### nv_xmlSitemapIndex_generate
- **Vị trí**: `includes/core/theme_functions.php`
- **Chức năng**: Xuất file XML sitemap của trang chính
- **Cú pháp**:  
  `nv_xmlSitemapIndex_generate()`
- **Ghi chú**: Hàm này chỉ sử dụng trong hệ thống

### nv_css_setproperties
- **Vị trí**: `includes/core/theme_functions.php`
- **Chức năng**: Trả về định dạng CSS nằm trên một hàng từ các thuộc tính `$property_array`
- **Cú pháp**:  
  `nv_css_setproperties($tag, $property_array)`
- **Trong đó**:  
  - `$tag`: Đối tượng, ví dụ `.classname`, `#idname`
  - `$property_array`: Mảng chứa các thuộc tính, key là tên thuộc tính, value là dữ liệu

### nv_theme_alert
- **Vị trí**: `includes/core/theme_functions.php`
- **Chức năng**: Xuất ra nội dung thông báo lỗi cho site dựa theo mẫu của giao diện
- **Cú pháp**:  
  `nv_theme_alert($message_title, $message_content, $type = 'info', $url_back = '', $lang_back = '', $time_back = 5)`
- **Trong đó**:  
  - `$message_title`: Tiêu đề thông báo
  - `$message_content`: Nội dung thông báo
  - `$type`: Kiểu với `info` = thông báo, `danger` = lỗi, `success` = thành công, `warning` = cảnh cáo
  - `$url_back`: Đường dẫn click vào liên kết quay lại
  - `$lang_back`: Ngôn ngữ cho liên kết quay lại
  - `$time_back`: Thời gian tự động quay lại
- **Ghi chú**: Hàm này được thêm vào kể từ NukeViet 4.2

### nv_create_submenu
- **Vị trí**: `includes/core/user_functions.php`
- **Chức năng**: Đưa các function hiển thị ngoài site của các module vào biến `$nv_vertical_menu`
- **Cú pháp**:  
  `nv_create_submenu()`
- **Ghi chú**: Hàm này chỉ sử dụng trong hệ thống

### nv_blocks_content
- **Vị trí**: `includes/core/user_functions.php`
- **Chức năng**: Đưa nội dung các block vào mã HTML của site
- **Cú pháp**:  
  `nv_blocks_content($sitecontent)`
- **Trong đó**:  
  - `$sitecontent`: Full mã HTML xuất ra cho trình duyệt
- **Ghi chú**: Hàm này chỉ sử dụng trong hệ thống

### nv_html_links
- **Vị trí**: `includes/core/user_functions.php`
- **Chức năng**: Lấy tất cả dữ liệu của site để xuất ra các thẻ `<link>` cho trình duyệt
- **Cú pháp**:  
  `nv_html_links($html = true)`
- **Trong đó**:  
  - `$html`: Nếu là `true` thì nội dung sẽ chuyển thành chuỗi, ngược lại trả về mảng

### nv_html_page_title
- **Vị trí**: `includes/core/user_functions.php`
- **Chức năng**: Lấy tiêu đề của site theo cấu hình
- **Cú pháp**:  
  `nv_html_page_title($html = true)`
- **Trong đó**:  
  - `$html`: Nếu là `true` thì nội dung sẽ chuyển thành chuỗi, ngược lại trả về mảng

### nv_html_css
- **Vị trí**: `includes/core/user_functions.php`
- **Chức năng**: Trả về CSS của hệ thống và module
- **Cú pháp**:  
  `nv_html_css($html = true)`
- **Trong đó**:  
  - `$html`: Nếu là `true` thì nội dung sẽ chuyển thành chuỗi, ngược lại trả về mảng

### nv_html_site_rss
- **Vị trí**: `includes/core/user_functions.php`
- **Chức năng**: Trả về các link RSS của module
- **Cú pháp**:  
  `nv_html_site_rss($html = true)`
- **Trong đó**:  
  - `$html`: Nếu là `true` thì nội dung sẽ chuyển thành chuỗi, ngược lại trả về mảng

### nv_html_site_js
- **Vị trí**: `includes/core/user_functions.php`
- **Chức năng**: Trả về các file JS của site và module
- **Cú pháp**:  
  `nv_html_site_js($html = true)`
- **Trong đó**:  
  - `$html`: Nếu là `true` thì nội dung sẽ chuyển thành chuỗi, ngược lại trả về mảng

### nv_admin_menu
- **Vị trí**: `includes/core/user_functions.php`
- **Chức năng**: Lấy nội dung HTML menu của admin ngoài site
- **Cú pháp**:  
  `nv_admin_menu()`

### nv_groups_list_pub
- **Vị trí**: `includes/core/user_functions.php`
- **Chức năng**: Lấy các nhóm thành viên tham gia tự do trong hệ thống
- **Cú pháp**:  
  `nv_groups_list_pub($mod_data = 'users')`
- **Trong đó**:  
  - `$mod_data`: Module lấy dữ liệu, để trống là module `users` của hệ thống

### nv_getCountry_from_file
- **Vị trí**: `includes/countries.php`
- **Chức năng**: Xác định quốc gia từ địa chỉ IP, dữ liệu lấy từ file
- **Cú pháp**:  
  `nv_getCountry_from_file($ip)`
- **Trong đó**:  
  - `$ip`: IP cần xác định quốc gia

### nv_getCountry_from_cookie
- **Vị trí**: `includes/countries.php`
- **Chức năng**: Xác định quốc gia từ địa chỉ IP, dữ liệu lấy từ cookie
- **Cú pháp**:  
  `nv_getCountry_from_cookie($ip)`
- **Trong đó**:  
  - `$ip`: IP cần xác định quốc gia

### nv_getenv
- **Vị trí**: `includes/functions.php`
- **Chức năng**: Tương tự hàm `getenv`
- **Cú pháp**:  
  `nv_getenv($a)`
- **Trong đó**:  
  - `$a`: Khóa

### nv_preg_quote
- **Vị trí**: `includes/functions.php`
- **Chức năng**: Là hàm `preg_quote` với tham số thứ 2 là `/`
- **Cú pháp**:  
  `nv_preg_quote($a)`

### nv_is_myreferer
- **Vị trí**: `includes/functions.php`
- **Chức năng**: Kiểm tra `$referer` có phải là của chính website hay không
- **Cú pháp**:  
  `nv_is_myreferer($referer = '')`

### nv_is_banIp
- **Vị trí**: `includes/functions.php`
- **Chức năng**: Kiểm tra xem địa chỉ IP có bị cấm truy cập website hay không
- **Cú pháp**:  
  `nv_is_banIp($ip)`
- **Trong đó**:  
  - `$ip`: IP cần kiểm tra

### nv_checkagent
- **Vị trí**: `includes/functions.php`
- **Chức năng**: Kiểm tra tính hợp lệ của User Agent, trả về `none` nếu User Agent không hợp lệ
- **Cú pháp**:  
  `nv_checkagent($a)`
- **Trong đó**:  
  - `$a`: User Agent cần kiểm tra

### nv_function_exists
- **Vị trí**: `includes/functions.php`
- **Chức năng**: Tương tự hàm `function_exists`
- **Cú pháp**:  
  `nv_function_exists($funcName)`

### nv_class_exists
- **Vị trí**: `includes/functions.php`
- **Chức năng**: Tương tự hàm `class_exists`
- **Cú pháp**:  
  `nv_class_exists($clName, $autoload = true)`

### nv_md5safe
- **Vị trí**: `includes/functions.php`
- **Chức năng**: Lấy mã MD5 của một chuỗi sau khi đã đưa chuỗi đó về lowercase
- **Cú pháp**:  
  `nv_md5safe($username)`
- **Trong đó**:  
  - `$username`: Chuỗi cần lấy MD5

### nv_EncodeEmail
- **Vị trí**: `includes/functions.php`
- **Chức năng**: Mã hóa email để xuất ra trình duyệt tránh các máy chủ tự bóc email
- **Cú pháp**:  
  `nv_EncodeEmail($strEmail, $strDisplay = '', $blnCreateLink = true)`
- **Trong đó**:  
  - `$strEmail`: Email cần mã hóa
  - `$strDisplay`: Nội dung hiển thị
  - `$blnCreateLink`: Đặt là `true` thì dữ liệu trả về dạng liên kết

### nv_user_groups
- **Vị trí**: `includes/functions.php`
- **Chức năng**: Kiểm tra và trả về các nhóm của thành viên
- **Cú pháp**:  
  `nv_user_groups($in_groups, $res_2step = false, $manual_groups = array())`
- **Trong đó**:  
  - `$in_groups`: Các nhóm trong CSDL
  - `$res_2step`: Nếu đặt là `true` thì dữ liệu trả về là một mảng, phần tử `0` là các nhóm, phần tử `1` (`true|false`) biểu thị có yêu cầu xác thực hai bước hay không
  - `$manual_groups`: Các nhóm thêm thủ công vào

### nv_monthname
- **Vị trí**: `includes/functions.php`
- **Chức năng**: Lấy tên tháng (theo ngôn ngữ) từ tháng dạng số
- **Cú pháp**:  
  `nv_monthname($i)`
- **Trong đó**:  
  - `$i`: Tháng

### nv_check_domain
- **Vị trí**: `includes/functions.php`
- **Chức năng**: Kiểm tra domain hợp lệ, nếu hợp lệ trả về domain, nếu không trả về chuỗi rỗng
- **Cú pháp**:  
  `nv_check_domain($domain)`
- **Trong đó**:  
  - `$domain`: Domain cần kiểm tra

### nv_redirect_location
- **Vị trí**: `includes/functions.php`
- **Chức năng**: Chuyển hướng trình duyệt tới URL, URL sẽ được rewrite nếu có thể
- **Cú pháp**:  
  `nv_redirect_location($url, $error_code = 301)`
- **Trong đó**:  
  - `$url`: URL cần chuyển hướng
  - `$error_code`: HTTP code chuyển hướng

### nv_strlen
- **Vị trí**: `includes/utf8/iconv_string_handler.php`
- **Chức năng**: Là hàm `strlen` hỗ trợ UTF-8
- **Cú pháp**:  
  `nv_strlen($string)`
- **Trong đó**:  
  - `$string`: Chuỗi cần xác định độ dài

### nv_substr
- **Vị trí**: `includes/utf8/iconv_string_handler.php`
- **Chức năng**: Là hàm `substr` hỗ trợ UTF-8
- **Cú pháp**:  
  `nv_substr($string, $start, $length)`

### nv_substr_count
- **Vị trí**: `includes/utf8/iconv_string_handler.php`
- **Chức năng**: Là hàm `substr_count` hỗ trợ UTF-8
- **Cú pháp**:  
  `nv_substr_count($haystack, $needle)`

### nv_strpos
- **Vị trí**: `includes/utf8/iconv_string_handler.php`
- **Chức năng**: Là hàm `strpos` hỗ trợ UTF-8
- **Cú pháp**:  
  `nv_strpos($haystack, $needle, $offset = 0)`

### nv_strrpos
- **Vị trí**: `includes/utf8/iconv_string_handler.php`
- **Chức năng**: Là hàm `strrpos` hỗ trợ UTF-8
- **Cú pháp**:  
  `nv_strrpos($haystack, $needle, $offset = 0)`

### nv_strtolower
- **Vị trí**: `includes/utf8/iconv_string_handler.php`
- **Chức năng**: Là hàm `strtolower` hỗ trợ UTF-8
- **Cú pháp**:  
  `nv_strtolower($string)`

### nv_strtoupper
- **Vị trí**: `includes/utf8/iconv_string_handler.php`
- **Chức năng**: Là hàm `strtoupper` hỗ trợ UTF-8
- **Cú pháp**:  
  `nv_strtoupper($string)`

### utf8_to_unicode
- **Vị trí**: `includes/utf8/utf8_functions.php`
- **Chức năng**: Chuyển mã UTF-8 sang Unicode
- **Cú pháp**:  
  `utf8_to_unicode($str)`
- **Trong đó**:  
  - `$str`: Mảng cần chuyển

### unicode_to_entities
- **Vị trí**: `includes/utf8/utf8_functions.php`
- **Chức năng**: Chuyển mã Unicode sang mã hiển thị trên trình duyệt
- **Cú pháp**:  
  `unicode_to_entities($unicode)`
- **Trong đó**:  
  - `$unicode`: Mảng cần chuyển

### unicode_to_utf8
- **Vị trí**: `includes/utf8/utf8_functions.php`
- **Chức năng**: Chuyển mã Unicode sang UTF-8
- **Cú pháp**:  
  `unicode_to_utf8($str)`
- **Trong đó**:  
  - `$str`: Mảng cần chuyển

### nv_str_split
- **Vị trí**: `includes/utf8/utf8_functions.php`
- **Chức năng**: Là hàm `str_split` hỗ trợ UTF-8
- **Cú pháp**:  
  `nv_str_split($str, $split_len = 1)`

### nv_strspn
- **Vị trí**: `includes/utf8/utf8_functions.php`
- **Chức năng**: Là hàm `strspn` hỗ trợ UTF-8
- **Cú pháp**:  
  `nv_strspn($str, $mask, $start = null, $length = null)`

### nv_ucfirst
- **Vị trí**: `includes/utf8/utf8_functions.php`
- **Chức năng**: Là hàm `ucfirst` hỗ trợ UTF-8
- **Cú pháp**:  
  `nv_ucfirst($str)`

### nv_ltrim
- **Vị trí**: `includes/utf8/utf8_functions.php`
- **Chức năng**: Là hàm `ltrim` hỗ trợ UTF-8
- **Cú pháp**:  
  `nv_ltrim($str, $charlist = false)`

### nv_rtrim
- **Vị trí**: `includes/utf8/utf8_functions.php`
- **Chức năng**: Là hàm `rtrim` hỗ trợ UTF-8
- **Cú pháp**:  
  `nv_rtrim($str, $charlist = false)`

### nv_trim
- **Vị trí**: `includes/utf8/utf8_functions.php`
- **Chức năng**: Là hàm `trim` hỗ trợ UTF-8
- **Cú pháp**:  
  `nv_trim($str, $charlist = false)`

### nv_EncString
- **Vị trí**: `includes/utf8/utf8_functions.php`
- **Chức năng**: Lọc dấu tiếng Việt
- **Cú pháp**:  
  `nv_EncString($string)`
- **Trong đó**:  
  - `$string`: Chuỗi cần lọc dấu

### nv_save_file_ips
- **Vị trí**: `includes/core/admin_functions.php`
- **Chức năng**: Ghi cấu hình chặn IP và cấu hình IP loại trừ Flood ra file
- **Cú pháp**:  
  `nv_save_file_ips($type)`
- **Trong đó**:  
  - `$type`: `0` thì lưu Ban IP, `1` thì lưu IP loại trừ Flood

### change_alias_tags
- **Vị trí**: `includes/utf8/utf8_functions.php`
- **Ghi chú**: Được bổ sung kể từ NukeViet 4.3.03
- **Chức năng**: Chuyển chuỗi thành liên kết tĩnh hợp lệ của tags
- **Cú pháp**:  
  `change_alias_tags($alias)`
- **Trong đó**:  
  - `$alias`: Chuỗi cần chuyển

### Chuyển từ XTemplate sang Smarty
- Nếu sửa XTemplate sang Smarty, chỉ cần sửa thay từ
  `$xtpl = new XTemplate('main.tpl', NV_ROOTDIR . '/themes/' . $template . '/modules/' . $module_file);`  
  thành
  ```php
  $template = get_tpl_dir([$global_config['module_theme'], $global_config['admin_theme']], 'admin_future', '/modules/' . $module_file . '/content.tpl');
    $tpl = new \NukeViet\Template\NVSmarty();
    $tpl->setTemplateDir(NV_ROOTDIR . '/themes/' . $template . '/modules/' . $module_file);
  ```
  còn thuật toán trên giữ nguyên


