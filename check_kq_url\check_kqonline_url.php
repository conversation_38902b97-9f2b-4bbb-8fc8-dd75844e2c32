<?php
// <PERSON>ac dinh thu muc goc cua site
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

/*
 * INSERT INTO `nv4_duthau_cat` (`id`, `type`, `catid`, `lasttime`, `active`, `numberday`, `page_old`) VALUES
 * (NULL, 'kqlcnt_online_check', 1, 0, 1, 0, 0),
 * (NULL, 'kqlcnt_online_check', 3, 0, 1, 0, 0),
 * (NULL, 'kqlcnt_online_check', 5, 0, 1, 0, 0),
 * (NULL, 'kqlcnt_online_check', 10, 0, 0, 0, 0),
 * (NULL, 'kqlcnt_online_check', 15, 0, 1, 0, 0),
 * (NULL, 'kqlcnt_online_check', 20, 0, 1, 0, 0);
 */
$bidType = 1;
$fromDate_int = strtotime('-360 Day'); // MSC cho lấy tối đa 1 năm
$toDate_int = time();

$fromDate = date('d/m/Y', $fromDate_int);
$toDate = date('d/m/Y', $toDate_int);

$_logs_file = NV_ROOTDIR . '/check_kq_url/check_kqonline_url_' . date('Y-m-d') . '.txt';
$query_url = $dbcr->query("SELECT * FROM nv4_duthau_cat WHERE type='kqlcnt_online_check' AND active =1 ORDER BY lasttime ASC LIMIT 1");
while ($_row = $query_url->fetch()) {
    $dbcr->query('UPDATE nv4_duthau_cat SET lasttime=' . NV_CURRENTTIME . ' WHERE id=' . $_row['id']);
    $bidType = $_row['catid']; // 1, 3, 5, 10, 15, 20
    $pagetotal = 10; // Gán số trang mặc định ban đầu là 10, sau đó sẽ tự xác định

    file_put_contents($_logs_file, date('d-m-Y', $fromDate_int) . '_' . date('d-m-Y', $toDate_int) . '_new_' . $bidType . "\n", FILE_APPEND);
    $_error_file = NV_ROOTDIR . '/check_kq_url/check_kqonline_url_' . date('d-m-Y', $fromDate_int) . '_' . date('d-m-Y', $toDate_int) . '_error_' . $bidType . '.txt';
    for ($page_no = 1; $page_no <= $pagetotal; $page_no ++) {
        $_start_time = microtime(true);
        echo "page_no = " . $page_no . "\n";
        $url = 'http://muasamcong.mpi.gov.vn:8082/NC/EP_COJ_NCQ812.jsp?orgCode=&radOrgan=0&bidType=' . $bidType . '&referNum=&noticeNm=&openbidDate1=' . $fromDate . '&noticeType=Y&notice_num=&instituCode=&searchType=1&instituName_cln=&openbidDate2=' . $toDate . '&pageSize=10&page_no=' . $page_no;
        $return = geturlpage($url, 1);
        if (!isset($return['pagetotal']) or empty($return['pagetotal'])) {
            file_put_contents($_error_file, $page_no . "\n", FILE_APPEND);
        } else {
            if ($return['number_new_id'] > 0) {
                file_put_contents($_logs_file, $page_no . "/" . $pagetotal . "\t" . $return['number_new_id'] . "\n", FILE_APPEND);
            }
            if ($return['pagetotal'] > $pagetotal) {
                $pagetotal = $return['pagetotal'];
            }
        }
        $return['run_time'] = number_format((microtime(true) - $_start_time), 3, '.', '');
        print_r($return);
    }

    // Đọc lại các page bị lỗi
    if (file_exists($_error_file)) {
        $string = file_get_contents($_error_file);
        $error_array = explode("\n", $string);
        unlink($_error_file);
        foreach ($error_array as $page_no) {
            $page_no = intval($page_no);
            if ($page_no > 0) {
                $_start_time = microtime(true);
                echo "page_no = " . $page_no . "\n";
                $url = 'http://muasamcong.mpi.gov.vn:8082/NC/EP_COJ_NCQ812.jsp?orgCode=&radOrgan=0&bidType=' . $bidType . '&referNum=&noticeNm=&openbidDate1=' . $fromDate . '&noticeType=Y&notice_num=&instituCode=&searchType=1&instituName_cln=&openbidDate2=' . $toDate . '&pageSize=10&page_no=' . $page_no;
                $return = geturlpage($url, 1);
                if (!isset($return['pagetotal']) or empty($return['pagetotal'])) {
                    file_put_contents($_error_file, $page_no . "\n", FILE_APPEND);
                } else {
                    if ($return['number_new_id'] > 0) {
                        file_put_contents($_logs_file, $page_no . "/" . $pagetotal . "\t" . $return['number_new_id'] . "\n", FILE_APPEND);
                    }
                }
                $return['run_time'] = number_format((microtime(true) - $_start_time), 3, '.', '');
                print_r($return);
            }
        }
    }
}

function geturlpage($url, $reload)
{
    global $dbcr, $db;

    // echo $url . "\n";
    $html = geturlhtml($url, 2);
    $return = [];
    $dom = new DOMDocument();
    libxml_use_internal_errors(true);
    if (empty($html) or !$dom->loadHTML($html)) {
        if ($reload) {
            return geturlpage($url, 0);
        }

        return 0;
    }
    $xpath = new DOMXPath($dom);

    $number_new_id = $number_new = $number_new_check = 0;
    $nodeList = $xpath->query('//table/tr/td[@class="page"]');
    if ($nodeList->length > 0) {
        $_html = trim(html_entity_decode(DOMinnerHTML($nodeList[0]), ENT_QUOTES, 'UTF-8'));
        if (preg_match_all("/([0-9])+/", $_html, $m)) {
            $return['pagetotal'] = ceil(($m[0][0] / 10));
        }
    }

    $nodeList = $xpath->query('//form[@name="sendData"]/table[@class="tr"]/tr');
    $number_new = 0;
    if ($nodeList->length > 0) {
        $number_new_id = 0;
        $arr_data_all = $arr_data_code = $arr_data_so_tbmt = array();
        foreach ($nodeList as $node) {
            $_html = DOMinnerHTML($node);
            $dom_i = new DOMDocument();
            if ($dom_i->loadHTML($_html)) {
                $xpath_i = new DOMXPath($dom_i);
                $nodeLis_i = $xpath_i->query('//td');
                if ($nodeLis_i->length == 9) {
                    $contents_array = array();
                    for ($i = 0; $i < $nodeLis_i->length; $i ++) {
                        $contents_array[] = trim(DOMinnerHTML($nodeLis_i[$i]));
                    }
                    $openbider = trim(html_entity_decode(DOMinnerHTML($nodeLis_i[6]), ENT_QUOTES, 'UTF-8'));
                    $thoi_diem_mo_thau = trim(DOMinnerHTML($nodeLis_i[4]));
                    if ((preg_match("/openbider\(\'([0-9]+)\'\,\'([0-9]+)\'\,\'bider\'\)\"\>\n*\s*\t*(.*)\n*\s*\t*\<\/a\>/", $openbider, $m) or preg_match("/toResonView\(\'([0-9]+)\'\,\'([0-9]+)\'\,\'([0-9]+)\'\,\%20\'([0-9]+)\'\)\"\>(.*)\<\/a\>/", $openbider, $m)) and preg_match('/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})\s([0-9]{1,2})\:([0-9]{1,2})$/', $thoi_diem_mo_thau, $t)) {
                        $arr_data = array();
                        $arr_data['code'] = $m[1];
                        $arr_data['code'] = trim(str_replace(' ', '', $arr_data['code']));
                        $arr_data['ten_du_an'] = html_entity_decode(DOMinnerHTML($nodeLis_i[2]), ENT_QUOTES, 'UTF-8');
                        $arr_data['ben_moi_thau'] = html_entity_decode(DOMinnerHTML($nodeLis_i[3]), ENT_QUOTES, 'UTF-8');
                        $arr_data['num_business'] = html_entity_decode(DOMinnerHTML($nodeLis_i[5]), ENT_QUOTES, 'UTF-8');
                        $arr_data['gia_trung_thau'] = html_entity_decode(DOMinnerHTML($nodeLis_i[7]), ENT_QUOTES, 'UTF-8');
                        $arr_data['gia_goi_thau'] = html_entity_decode(DOMinnerHTML($nodeLis_i[8]), ENT_QUOTES, 'UTF-8');
                        $arr_data['nhathautrungthau'] = html_entity_decode(DOMinnerHTML($nodeLis_i[6]), ENT_QUOTES, 'UTF-8');
                        if (preg_match("/toResonView\(\'([0-9]+)\'\,\'([0-9]+)\'\,\'([0-9]+)\'\,\%20\'([0-9]+)\'\)\"\>(.*)\<\/a\>/", $arr_data['nhathautrungthau'], $_match)) {
                            $arr_data['link_url'] = 'gonggo_num=' . $_match[1] . '&gonggo_cha=' . $_match[2] . '&gubun=' . $_match[3] . '&rebid_no=' . $_match[4];
                            $arr_data['type_result'] = 1; // k có kết quả
                            $arr_data['business'] = 'Không có nhà thầu trúng thầu';
                        } else if (preg_match("/openbider\(\'([0-9]+)\'\,\'([0-9]+)\'\,\'bider\'\)\"\>\n*\s*\t*(.*)\n*\s*\t*\<\/a\>/", $arr_data['nhathautrungthau'], $match)) {
                            $arr_data['link_url'] = 'bidNo=' . $match[1] . '&bidTurnNo=' . $match[2];
                            $arr_data['type_result'] = 0;
                            $arr_data['business'] = $match[3];
                        } else {
                            $arr_data['link_url'] = 'bidNo=' . $m[1] . '&bidTurnNo=' . $m[2];
                            $arr_data['type_result'] = 0;
                            $arr_data['business'] = strip_tags($arr_data['nhathautrungthau']);
                        }
                        $arr_data['thoi_diem_mo_thau'] = mktime($t[4], $t[5], 0, $t[2], $t[1], $t[3]);

                        $arr_data_all[$arr_data['code']] = $arr_data;
                        $arr_data_code[$arr_data['code']] = $arr_data['code'];
                        $so_tbmt = html_entity_decode(DOMinnerHTML($nodeLis_i[1]), ENT_QUOTES, 'UTF-8');
                        $so_tbmt = trim(str_replace(' ', '', $so_tbmt));
                        $so_tbmt = strip_tags($so_tbmt);
                        $arr_data_so_tbmt[$so_tbmt] = $so_tbmt;
                        ++ $number_new;
                    }
                }
            }
        }

        if (!empty($arr_data_code)) {
            $_result = $db->query("SELECT code FROM nv4_vi_bidding_result WHERE code IN ('" . implode("','", $arr_data_so_tbmt) . "')");
            $arr_result_exit = array();
            while ($tmp = $_result->fetch()) {
                $tmp['code'] = explode('-', $tmp['code']);
                $arr_result_exit[$tmp['code'][0]] = $tmp['code'][0];
            }
            try {
                $_sql_insert = [];
                foreach ($arr_data_all as $data) {
                    if (!in_array($data['code'], $arr_result_exit)) {
                        $_sql_insert[] = '(' . $dbcr->quote($data['code']) . ',' . $dbcr->quote($data['ten_du_an']) . ',' . $dbcr->quote($data['ben_moi_thau']) . ',' . $dbcr->quote($data['thoi_diem_mo_thau']) . ',' . $dbcr->quote($data['link_url']) . ', ' . $arr_data['num_business'] . ', ' . $dbcr->quote($data['business']) . ', ' . $dbcr->quote($data['gia_trung_thau']) . ', ' . $dbcr->quote($data['gia_goi_thau']) . ', ' . $data['type_result'] . ')';
                    } else {
                        $number_new_check ++;
                    }
                }
                if (!empty($_sql_insert)) {
                    $number_new_id = $dbcr->exec('INSERT IGNORE INTO `nv4_vi_bidding_result_url` (`code`, `title`, `owner`, `addtime`, `url`, `num_business`, `business`, `gia_trung_thau`, `gia_goi_thau`, `type_result`) VALUES ' . implode(', ', $_sql_insert));
                }
            } catch (PDOException $e) {
                print_r($e);
                file_put_contents(NV_ROOTDIR . '/check_kq_url/pdoexception.txt', print_r($e, true) . "\n\n", FILE_APPEND);
            }
        }
    } elseif ($reload) {
        return geturlpage($url, 0);
    }

    $return['number_new'] = $number_new;
    $return['number_new_check'] = $number_new_check;
    $return['number_new_id'] = $number_new_id;
    return $return;
}
