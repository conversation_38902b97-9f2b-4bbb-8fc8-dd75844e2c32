<?php

// <PERSON>yển đổi dữ liệu chủ đầu tư của hệ thống msc mới (investor)
// https://muasamcong.mpi.gov.vn/web/guest/investors-approval-v2
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
define('CHUDAUTU_URL_TABLE', 'nv22_chudautu');
define('SOLICITOR_TABLE', BID_PREFIX_GLOBAL . '_solicitor');
define('SOLICITOR_DETAIL_TABLE', BID_PREFIX_GLOBAL . '_solicitor_detail');
define('BUSINESS_TYPE', 'nv4_vi_businesslistings_businesstype');

// lấy thông tin cơ quan chủ quản
$array_management_agency = [];
$management_agency_query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_bidding_agency');
while ($temp = $management_agency_query->fetch()) {
    $array_management_agency[$temp['title']] = $temp['id'];
}
$array_management_agency['UBND tỉnh Bà Rịa-Vũng Tàu'] = 7;
$array_management_agency['Khác'] = 0;

// alias tỉnh thành
$arr_id_province = [
    'ha-noi' => 101,
    'hai-phong' => 103,
    'hai-duong' => 107,
    'hung-yen' => 109,
    'ha-nam' => 111,
    'nam-dinh' => 113,
    'thai-binh' => 115,
    'ninh-binh' => 117,
    'ha-giang' => 201,
    'cao-bang' => 203,
    'lao-cai' => 205,
    'bac-kan' => 207,
    'lang-son' => 209,
    'tuyen-quang' => 211,
    'yen-bai' => 213,
    'thai-nguyen' => 215,
    'phu-tho' => 217,
    'vinh-phuc' => 219,
    'bac-giang' => 221,
    'bac-ninh' => 223,
    'quang-ninh' => 225,
    'dien-bien' => 301,
    'lai-chau' => 302,
    'son-la' => 303,
    'hoa-binh' => 305,
    'thanh-hoa' => 401,
    'nghe-an' => 403,
    'ha-tinh' => 405,
    'quang-binh' => 407,
    'quang-tri' => 409,
    'thua-thien-hue' => 411,
    'da-nang' => 501,
    'quang-nam' => 503,
    'quang-ngai' => 505,
    'binh-dinh' => 507,
    'phu-yen' => 509,
    'khanh-hoa' => 511,
    'kon-tum' => 601,
    'gia-lai' => 603,
    'dak-lak' => 605,
    'dak-nong' => 606,
    'ho-chi-minh' => 701,
    'lam-dong' => 703,
    'ninh-thuan' => 705,
    'binh-phuoc' => 707,
    'tay-ninh' => 709,
    'binh-duong' => 711,
    'dong-nai' => 713,
    'binh-thuan' => 715,
    'ba-ria-vung-tau' => 717,
    'long-an' => 801,
    'dong-thap' => 803,
    'an-giang' => 805,
    'tien-giang' => 807,
    'vinh-long' => 809,
    'ben-tre' => 811,
    'kien-giang' => 813,
    'can-tho' => 815,
    'hau-giang' => 816,
    'tra-vinh' => 817,
    'soc-trang' => 819,
    'bac-lieu' => 821,
    'ca-mau' => 823
];

// Thông tin loại hình pháp lý business_type
$business_query = $db->query('SELECT * FROM ' . BUSINESS_TYPE);
$business_type = [];
while ($temp = $business_query->fetch()) {
    $business_type[$temp['code']] = $temp;
}

// Thông tin nation
$nation_query = $db->query('SELECT * FROM nv4_msc_nation');
$arr_nation = [];
while ($temp = $nation_query->fetch()) {
    $arr_nation[$temp['code']] = $temp;
}

// Thông tin rep_position
$rep_position_query = $db->query('SELECT * FROM nv4_vi_rep_position');
$arr_rep_position = [];
while ($temp = $rep_position_query->fetch()) {
    $arr_rep_position[$temp['alias']] = $temp['id'];
    $arr_rep_position_alias[$temp['id']] = $temp['alias'];
}

// lấy thông tin chudautu
$limit = 20;
$uniqid = uniqid('', true);
$dbcr->query("UPDATE " . CHUDAUTU_URL_TABLE . " SET dauthau_info='-1', uniqid = " . $dbcr->quote($uniqid) . " WHERE uniqid = '' and dauthau_info=0 AND url_run > 99 ORDER BY id ASC limit " . $limit);
$query_url = $dbcr->query('SELECT * FROM ' . CHUDAUTU_URL_TABLE . ' WHERE uniqid = ' . $dbcr->quote($uniqid));
// $query_url = $dbcr->query('SELECT * FROM ' . CHUDAUTU_URL_TABLE . ' WHERE id = 4845');

while ($row = $query_url->fetch()) {
    $err_code = [];
    $error = [];
    $project_owner = $project_owner_detail = [];
    print_r("\n -------- Bat dau convert chudautu url id: " . $row['id'] . "\n");

    $save = false;
    // kiểm tra xem dữ liệu đã có trong bảng solicitor chưa
    if ($row['is_solicitor'] > 0) {
        // kiểm tra xem bảng solicitor đã có org_code chưa
        $check_solicitor = $db->query('SELECT id FROM ' . SOLICITOR_TABLE . ' WHERE org_code = ' . $db->quote($row['orgcode']) . ' ORDER BY id DESC LIMIT 1')
                ->fetchColumn();
        if (!empty($check_solicitor)) {
            // update bảng solicitor trường is_project_owner = 1
            $db->query("UPDATE " . SOLICITOR_TABLE . " SET is_project_owner=1, update_data=0 WHERE id = " . $check_solicitor);
            // cập nhật id của solicitor vào bảng url
            $dbcr->query("UPDATE " . CHUDAUTU_URL_TABLE . " SET dauthau_info=" . $check_solicitor . " WHERE id=" . $row['id']);
            print_r("- chudautu url id da convert xong: " . $row['id'] . "\n");
            print_r("- chudautu id: " . $check_solicitor . "\n");
            continue;
        } else {
            $save = true;
        }
    } else {
        $save = true;
    }

    // lưu thông tin
    if ($save) {
        $detail2 = !empty($row['detail2']) ? json_decode($row['detail2'], true) : [];
        $detail1 = !empty($row['detail1']) ? json_decode($row['detail1'], true) : [];
    
        // orgCode -> org_code
        $org_code = !empty($detail2['orgCode']) ? $detail2['orgCode'] : '';
        if (empty($org_code)) {
            $err_code[] = 2001;
            $error[] = 'orgCode is required';
        }
    
        // orgFullName -> title
        $title = !empty($detail2['orgFullName']) ? $detail2['orgFullName'] : '';
        $title = nv_compound_unicode($title);
        if (empty($title)) {
            $err_code[] = 2002;
            $error[] = 'orgFullName is required';
        }
        // officeAdd -> address
        $address = !empty($detail2['officeAdd']) ? $detail2['officeAdd'] : '';
        $address = nv_compound_unicode($address);
        // officePhone -> phone
        $phone = !empty($detail2['officePhone']) ? $detail2['officePhone'] : '';
        // status -> detail.status
        $status = !empty($row['status']) ? $row['status'] : 0;
        // effRoleDate -> aproval_time
        $aproval_time = ((!empty($row['effroledate']) && (preg_match('/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/', $row['effroledate'], $m))) ? mktime(0, 0, 0, $m[2], $m[1], $m[3]) : 0);
    
        // orgEnName -> detail.english_name
        $english_name = !empty($detail2['orgEnName']) ? $detail2['orgEnName'] : '';
        $english_name = nv_compound_unicode($english_name);
        // taxCode -> detail.tax
        $tax = !empty($detail2['taxCode']) ? $detail2['taxCode'] : '';
        // taxDate -> detail.date_registration
        $date_registration = (!empty($detail2['taxDate']) ? (int) ($detail2['taxDate'] / 1000) : 0);
        if ($date_registration > NV_CURRENTTIME) {
            $date_registration = 0;
        }
        // taxNation -> detail.nation
        $nation_code = !empty($detail2['taxNation']) ? $detail2['taxNation'] : '';
        $nation = !empty($nation_code) && in_array($nation_code, array_keys($arr_nation)) ? nv_compound_unicode($arr_nation[$nation_code]['id']) : 0;
    
        // businessType -> detail.solicitor_type
        $project_owner_type_code = !empty($detail2['businessType']) ? $detail2['businessType'] : '';
        $project_owner_type = !empty($project_owner_type_code) && in_array($project_owner_type_code, array_keys($business_type)) ? nv_compound_unicode($business_type[$project_owner_type_code]['id']) : 0;
    
        // agencyName -> detail.management_agency
        $id_agency_msc = 0;
        if (!empty($detail2['agencyName'])) {
            $id_agency_msc = !empty($detail2['agencyName']) && in_array($detail2['agencyName'], $array_management_agency) ? $detail2['agencyName'] : 0;
        }
        // budgetCode -> detail.budget_code
        $budget_code = !empty($detail2['budgetCode']) ? $detail2['budgetCode'] : '';
        $budget_code = nv_compound_unicode($budget_code);
        // officeWeb -> detail.website
        $website = !empty($detail2['officeWeb']) ? $detail2['officeWeb'] : '';
        if (!empty($website)) {
            if (!preg_match("/^(?:http(s)?:\/\/)?[\w.-]+(?:\.[\w\.-]+)+[\w\-\.\_\~\:\/\?\#\[\]\@\!\$\&\'\%\(\)\*\+,;=.]+$/", $website)) {
                $website = '';
            }
        }
        $website = nv_compound_unicode($website);
        // repName -> detail.rep_name
        $rep_name = !empty($detail2['repName']) ? $detail2['repName'] : '';
        $rep_name = nv_compound_unicode($rep_name);
        // repPosition -> detail.rep_position
        $rep_position = !empty($detail2['repPosition']) ? $detail2['repPosition'] : '';
        $rep_position = find_trim_address($rep_position);
        $rep_position = trim($rep_position);
        $rep_position = nv_compound_unicode($rep_position);
        $rep_position_alias = change_alias($rep_position);
        $rep_position_alias = strtolower($rep_position_alias);
        $rep_position_id = 0;
    
        // lấy id rep_position
        if (in_array($rep_position_alias, $arr_rep_position_alias)) {
            $rep_position_id = $arr_rep_position[$rep_position_alias];
    
            if (empty($rep_position_id)) {
                $rep_position_id = 0;
            }
        } else {
            // nếu không tìm thấy -> thêm mới bảng nv4_vi_rep_position
            $stmt = $db->prepare("INSERT INTO `nv4_vi_rep_position`(`title`, `alias`) VALUES (:title, :alias)");
            $stmt->bindParam(':title', $rep_position, PDO::PARAM_STR);
            $stmt->bindParam(':alias', $rep_position_alias, PDO::PARAM_STR);
            $exc = $stmt->execute();
            if ($exc) {
                $rep_position_id = $db->lastInsertId();
                echo '- Thêm rep_position vào bảng nv4_vi_rep_position, id: ' . $rep_position_id . ', title: ' . $rep_position . ', alias: ' . $rep_position_alias . "\n";
            }
        }
    
        // officePro -> id_province, detail.province
        // Lấy thông tin id tỉnh trong module location
        $string = $name_province1 = $alias_province1 = $alias_province1 = $alias_province2 = '';
        $id_province = $id_province2 = 0;
        if (!empty($detail2['officePro'])) {
            // Kiểm tra phát hiện sai thông tin tỉnh/thành phố
            if (!empty($detail2['officeAdd'])) {
                $string = trim($detail2['officeAdd']);
                $name_province1 = '';
                $alias_province1 = $alias_province2 = '';
                if (preg_match('/\s*(tỉnh|thành phố|tp\.|tp\s|t\.|t\s)\s*([^\,]+)$/i', $string, $m)) {
                    $name_province1 = find_trim_address($m[2]);
                    $alias_province1 = strtolower(change_alias($name_province1));
                }
    
                $alias_province2 = array_search($detail2['officePro'], $arr_id_province);
    
                if (!empty($name_province1) and !empty($alias_province2) and $alias_province1 != $alias_province2) {
                    $id_province2 = isset($arr_id_province[$alias_province1]) ? $arr_id_province[$alias_province1] : 0;
                    if ($id_province2 > 0) {
                        echo "dia_chi: " . $detail2['officeAdd'] . "\n";
                        echo "tinh_thanh_pho: " . $detail2['officePro'] . "\n";
                        echo "name_province: " . $name_province1 . "\n";
                        echo "id_province2: " . $id_province2 . "\n\n";
                    }
                }
            }
    
            // lấy tên tỉnh/thành phố
            $id_province = (int) $detail2['officePro'];
            $province = '';
            if (!empty($id_province)) {
                $sql_diachi = $db->query('SELECT title FROM `nv4_vi_location_province` WHERE `id`=' . $db->quote($id_province));
                $province = $sql_diachi->fetchColumn();
            }
    
            if (empty($province)) {
                print_r('id_province: ' . $detail2['officePro'] . ' -> không lấy được tên tỉnh/tp<br/>');
            }
        }
    
        $province = !empty($province) ? nv_compound_unicode($province) : '';
    
        /* Begin Xử lý các dữ liệu mới https://vinades.org/dauthau/dauthau.info/-/issues/1445 */
        // $exp_time = ((!empty($detail1) && !empty($detail1['expTime']) && is_array($detail1['expTime']) && count($detail1['expTime']) == 6 && $detail1['expTime'] === array_filter($detail1['expTime'], 'is_int')) ? mktime($detail1['expTime'][3], $detail1['expTime'][4], $detail1['expTime'][5], $detail1['expTime'][1], $detail1['expTime'][2], $detail1['expTime'][0]) : 0);
        $exp_time = 0;
    
        $project_owner = [
            // column mới
            'id_district' => !empty($detail2['officeDis']) ? $detail2['officeDis'] : 0,
            'id_ward' => !empty($detail2['officeWar']) ? $detail2['officeWar'] : 0,
            'exp_time' => $exp_time,
            // column cũ
            'org_code' => $org_code,
            'title' => $title,
            'address' => $address,
            'aproval_time' => $aproval_time,
            'phone' => $phone,
            'id_province' => $id_province,
            'id_agency_msc' => $id_agency_msc,
            'id_province2' => $id_province2,
            'english_name' => $english_name
        ];
    
        // receiverEmail -> receiver_email
        $email_validation_regex = "/^[_a-z0-9-]+(\.[_a-z0-9-]+)*@[a-z-]+(\.[a-z-]+)*(\.[a-z]{2,3})$/i";
        $receiver_email = !empty($detail2['receiverEmail']) ? nv_compound_unicode($detail2['receiverEmail']) : '';
        if (!empty($receiver_email)) {
            $receiver_email = trim($receiver_email);
            $receiver_email = preg_replace('/\\t|\\n|\\r|\s/', '', $receiver_email);
    
            if (!filter_var($receiver_email, FILTER_VALIDATE_EMAIL) || !preg_match($email_validation_regex, $receiver_email)) {
                $receiver_email = '';
            }
        }
        // recEmailElecInvo -> rec_email_elec_invo
        $rec_email_elec_invo = !empty($detail2['recEmailElecInvo']) ? nv_compound_unicode($detail2['recEmailElecInvo']) : '';
        if (!empty($rec_email_elec_invo)) {
            $rec_email_elec_invo = trim($rec_email_elec_invo);
            $rec_email_elec_invo = preg_replace('/\\t|\\n|\\r|\s/', '', $rec_email_elec_invo);
    
            if (!filter_var($rec_email_elec_invo, FILTER_VALIDATE_EMAIL) || !preg_match($email_validation_regex, $rec_email_elec_invo)) {
                $rec_email_elec_invo = '';
            }
        }
        // repEmail -> rep_email
        $rep_email = !empty($detail2['repEmail']) ? nv_compound_unicode($detail2['repEmail']) : '';
        if (!empty($rep_email)) {
            $rep_email = trim($rep_email);
            $rep_email = preg_replace('/\\t|\\n|\\r|\s/', '', $rep_email);
    
            if (!filter_var($rep_email, FILTER_VALIDATE_EMAIL) || !preg_match($email_validation_regex, $rep_email)) {
                $rep_email = '';
            }
        }
    
        $project_owner_detail = [
            // column mới
            'receiver_name' => !empty($detail2['receiverName']) ? nv_compound_unicode($detail2['receiverName']) : '',
            'receiver_phone' => !empty($detail2['receiverPhone']) ? $detail2['receiverPhone'] : '',
            'receiver_email' => $receiver_email,
            'rec_email_elec_invo' => $rec_email_elec_invo,
            'rep_phone' => !empty($detail2['repPhone']) ? $detail2['repPhone'] : '',
            'rep_email' => $rep_email,
            // các column cũ
            'full_name' => $title,
            'english_name' => $english_name,
            'province' => $province,
            'website' => $website,
            'tax' => $tax,
            'no_business_licence' => $tax,
            'status' => $status,
            'date_registration' => $date_registration,
            'nation' => $nation,
            'solicitor_type' => $project_owner_type,
            'budget_code' => $budget_code,
            'rep_name' => $rep_name,
            'rep_position' => $rep_position_id,
            'management_agency' => $id_agency_msc
        ];
        /* END xử lý */
    
        if (empty($err_code)) {
            try {
                // Thêm/update thông tin vào bảng nv4_vi_bidding_solicitor và bảng nv4_vi_bidding_solicitor_detail
                // 1. Kiểm tra xem nhà mời thầu này đã tồn tại chưa, nếu chưa thì thêm mới, nếu có rồi thì chỉ update thôi
                $sdkkd = $tax;
                $sdkkd_old = $_solicitor = [];
    
                $_solicitor = $db->query('SELECT * FROM ' . SOLICITOR_TABLE . ' WHERE org_code = ' . $db->quote($org_code) . ' ORDER BY id DESC LIMIT 1')
                    ->fetch();
                if (!empty($_solicitor)) {
                    $sdkkd_old = $db->query('SELECT * FROM ' . SOLICITOR_DETAIL_TABLE . ' WHERE id = ' . $_solicitor['id'])->fetch();
                } else {
                    if (!empty($sdkkd)) {
                        $sdkkd_old = $db->query('SELECT * FROM ' . SOLICITOR_DETAIL_TABLE . ' t1 INNER JOIN ' . SOLICITOR_TABLE . ' t2 ON t1.id = t2.id WHERE t1.tax= ' . $db->quote($sdkkd) . ' AND t2.aproval_time> 0 ORDER BY t2.aproval_time DESC LIMIT 1')
                            ->fetch();
                    }
                }
    
                /*
                 * Nếu có thay đổi về mã cơ quan nhưng số đăng ký kinh doanh/ MST không thay đổi, Tên không thay đổi (các dữ liệu khác có thể thay đổi hoặc không) => Bên mời thầu cũ được cấp mã cơ quan mới trên hệ thống.
                 * Nếu có thay đổi về Tên cơ quan, số ĐKKD không thay đổi (các thông tin khác có thể thay đổi) => Cơ quan đổi tên. Xét tiếp các trường hợp sau:
                 * - Mã cơ quan có thay đổi ==> hệ thống sinh ID mới cho bên mời thầu với dữ liệu mới. Từ số ĐKKD liên kết sang ID cũ (Dữ liệu cũ) và ngược lại.
                 * - Mã cơ quan không thay đổi ==> hệ thống sinh phiên bản mới cho bên mời thầu (cập nhật vào số lần thay đổi và lịch sử thay đổi).
                 * Các trường hợp thay đổi các thông tin còn lại => Chỉ là thay đổi thông tin đơn thuần ==> Cập nhật vào số lần thay đổi và lịch sử thay đổi.
                 */
    
                $insert = 1;
    
                if (!empty($sdkkd_old)) {
                    if (empty($_solicitor)) {
                        $_solicitor = $db->query('SELECT * FROM ' . SOLICITOR_TABLE . ' WHERE id=' . $sdkkd_old['id'])->fetch();
                    }
    
                    if (!empty($_solicitor)) {
                        if ((!empty($_solicitor['org_code']) and $_solicitor['org_code'] != $org_code) and (!empty($sdkkd_old['no_business_licence']) and $sdkkd_old['no_business_licence'] == $tax)) {
                            $insert = 1;
                        } else if ((!empty($sdkkd_old['no_business_licence']) and $sdkkd_old['no_business_licence'] == $tax) and strip_punctuation(nv_strtolower($title)) != strip_punctuation(nv_strtolower($_solicitor['title']))) {
                            if (!empty($_solicitor['org_code']) and $_solicitor['org_code'] == $org_code) {
                                $insert = 0;
                            } else {
                                $insert = 1;
                            }
                        } else {
                            $insert = 0;
                        }
                    }
                } else {
                    if (!empty($_solicitor)) {
                        $insert = 0;
                    }
                }
    
                if ($insert == 0 and !empty($_solicitor)) {
                    // Kiểm tra lại nếu Giá trị mới không có thì gán lại giá trị cũ
                    // các row cần check
                    $project_owner_check = [
                        'address',
                        'aproval_time',
                        'phone',
                        'id_province',
                        'status',
                        'id_agency_msc',
                        'english_name'
                    ];
                    // Gán giá trị cũ
                    foreach ($project_owner as $project_owner_key => $project_owner_value) {
                        if (empty($project_owner_value) && in_array($project_owner_key, $project_owner_check)) {
                            $project_owner[$project_owner_key] = $_solicitor[$project_owner_key];
                        }
                    }
                }
    
                $content_full = $org_code . ' ' . $title . ' ' . $address . ' ' . $english_name . ' ' . $tax;
                $content = str_replace('-', ' ', change_alias($content_full));
                if ($insert == 1) {
                    // Thêm mới
                    echo "...Bat dau insert " . SOLICITOR_TABLE . "... \n";
                    $project_owner['note'] = '';
                    $project_owner['solicitor_code'] = '';
                    $project_owner['alias'] = get_alias_bidd($title);
                    $project_owner['content'] = $content;
                    $project_owner['content_full'] = $content_full;
                    $project_owner['upcount'] = 1;
                    $project_owner['get_time'] = NV_CURRENTTIME;
                    $project_owner['fget_time'] = NV_CURRENTTIME;
                    $project_owner['elasticsearch'] = 0;
                    $project_owner['is_new_msc'] = 1;
                    $project_owner['is_project_owner'] = 1;
    
                    // bắt đầu insert
                    $exc = insert_array($project_owner, SOLICITOR_TABLE);
                    $id_solicitor = $exc;
                } else {
                    // update lại thông tin nhà mời thầu đã tồn tại trên hệ thống
                    echo "...Bat dau update " . SOLICITOR_TABLE . "... \n";
                    $id_solicitor = $_solicitor['id'];
                    $project_owner['alias'] = get_alias_bidd($title, $id_solicitor);
                    $project_owner['upcount'] = $_solicitor['upcount'] + 1;
                    $project_owner['get_time'] = NV_CURRENTTIME;
                    $project_owner['content'] = $content;
                    $project_owner['content_full'] = $content_full;
                    $project_owner['elasticsearch'] = 0;
                    $project_owner['is_new_msc'] = 1;
                    $project_owner['is_project_owner'] = 1;
    
                    $arr_where = [];
                    $arr_where[] = 'id=' . $_solicitor['id'];
                    // bắt đầu update
                    $exc = update_array($project_owner, SOLICITOR_TABLE, $arr_where);
                    $log_change = [];
    
                    // tạo array log_change solicitor
                    $project_owner_key_log = [
                        'title',
                        'org_code',
                        'address',
                        'aproval_time',
                        'phone',
                        'id_province',
                        'id_agency_msc'
                    ];
                    foreach ($project_owner as $project_owner_key => $project_owner_value) {
                        if (in_array($project_owner_key, $project_owner_key_log) && $project_owner_value != $_solicitor[$project_owner_key]) {
                            $log_change[$project_owner_key] = $_solicitor[$project_owner_key] . '=>' . $project_owner[$project_owner_key];
                        }
                    }
                }
                /* Thêm/update thông tin tại bảng nv4_vi_bidding_solicitor_detail */
                if ($exc) {
                    // Kiểm tra $id_solicitor đã có trong csdl chưa
                    $project_owner_old_detail = $db->query('SELECT * FROM ' . SOLICITOR_DETAIL_TABLE . ' WHERE id=' . $db->quote($id_solicitor))
                        ->fetch();
                    if (empty($project_owner_old_detail)) {
                        echo "...Bat dau insert " . SOLICITOR_DETAIL_TABLE . "... \n";
                        $project_owner_detail['id'] = $id_solicitor;
                        $exc1 = insert_array($project_owner_detail, SOLICITOR_DETAIL_TABLE);
                    } elseif ($project_owner_old_detail['id'] > 0) {
                        // Kiểm tra lại nếu Giá trị mới không có thì gán lại giá trị cũ
                        // các row cần check
                        $project_owner_detail_check = [
                            'english_name',
                            'province',
                            'website',
                            'tax',
                            'status',
                            'date_registration',
                            'nation',
                            'solicitor_type',
                            'budget_code',
                            'rep_name',
                            'rep_position',
                            'no_business_licence',
                            'receiver_name',
                            'receiver_phone',
                            'receiver_email',
                            'rec_email_elec_invo',
                            'rep_phone',
                            'rep_email',
                            'management_agency'
                        ];
                        // Gán giá trị cũ
                        foreach ($project_owner_detail as $project_owner_detail_key => $project_owner_detail_value) {
                            if (empty($project_owner_detail_value) && in_array($project_owner_detail_key, $project_owner_detail_check)) {
                                $project_owner_detail[$project_owner_detail_key] = $project_owner_old_detail[$project_owner_detail_key];
                            }
                        }
    
                        echo "...Bat dau update " . SOLICITOR_DETAIL_TABLE . "... \n";
                        $arr_where = [];
                        $arr_where[] = 'id=' . $id_solicitor;
                        $exc1 = update_array($project_owner_detail, SOLICITOR_DETAIL_TABLE, $arr_where);
                        if ($exc1) {
                            $project_owner_new_detail = $db->query('SELECT * FROM ' . SOLICITOR_DETAIL_TABLE . ' WHERE id=' . $db->quote($id_solicitor))
                                ->fetch();
                            $project_owner_old_detail['get_time'] = !empty($_solicitor['get_time']) ? $_solicitor['get_time'] : NV_CURRENTTIME;
                            $status_insert = insert_log_crawls($project_owner_old_detail['id'], 'PROJECT_OWNER', $project_owner_old_detail, $project_owner_new_detail);
                            if ($status_insert > 0) {
                                echo ("<br/>\nLOG: PROJECT_OWNER - ID: " . $project_owner_old_detail['id'] . "- OK<br/>\n");
                            }
                        }
    
                        // tạo array log_change project_owner_detail
                        $project_owner_detail_key_log = [
                            'full_name',
                            'english_name',
                            'province',
                            'website',
                            'tax',
                            'status',
                            'date_registration',
                            'nation',
                            'solicitor_type',
                            'budget_code',
                            'rep_name',
                            'rep_position'
                        ];
                        foreach ($project_owner_detail as $project_owner_detail_key => $project_owner_detail_value) {
                            if (in_array($project_owner_detail_key, $project_owner_detail_key_log) && $project_owner_detail_value != $project_owner_old_detail[$project_owner_detail_key]) {
                                $log_change[$project_owner_detail_key] = $project_owner_old_detail[$project_owner_detail_key] . '=>' . $project_owner_detail[$project_owner_detail_key];
                            }
                        }
                    }
                    if (!empty($log_change)) {
                        $_solicitor['log_change'] = (!empty($_solicitor['log_change'])) ? json_decode($_solicitor['log_change'], true) : [];
                        $_solicitor['log_change'][NV_CURRENTTIME] = $log_change;
                        $_solicitor['log_change'] = json_encode($_solicitor['log_change']);
                        $db->query('UPDATE ' . SOLICITOR_TABLE . ' SET log_change=' . $db->quote($_solicitor['log_change']) . ' WHERE `id`=' . $db->quote($id_solicitor));
                    }
                    // cập nhật id của solicitor vào bảng url
                    $dbcr->query("UPDATE " . CHUDAUTU_URL_TABLE . " SET dauthau_info=" . $id_solicitor . ", id_agency=" . $id_agency_msc . " WHERE id=" . $row['id']);
                    print_r("- chudautu url id da convert xong: " . $row['id'] . "\n");
                    print_r("- project_owner id: " . $id_solicitor . "\n");
                }
            } catch (PDOException $e) {
                $err_code[] = 3000;
                print_r("- Da xay ra loi trong qua trinh convert, chudautu url id: " . $row['id'] . "\n");
                print_r($e);
                $dbcr->query("UPDATE " . CHUDAUTU_URL_TABLE . " SET dauthau_info='-1', err_code = '" . implode(",", $err_code) . "' WHERE id=" . $row['id']);
                return false;
            }
    
        } else {
            print_r("- Da xay ra loi trong qua trinh convert, chudautu url id: " . $row['id'] . "\n");
            print_r($error);
            $dbcr->query("UPDATE " . CHUDAUTU_URL_TABLE . " SET dauthau_info='-2', err_code = '" . implode(",", $err_code) . "' WHERE id=" . $row['id']);
        }
    }
    
}
echo "Thoi gian thuc hien = " . number_format((microtime(true) - NV_START_TIME), 3, '.', '');

function get_alias_bidd($title, $id = 0)
{
    global $db;

    if (empty($title)) {
        return '';
    }

    $alias = change_alias($title);
    $alias = strtolower($alias);

    // cắt ngắn alias nếu quá 255 ký tự
    if (strlen($alias) > 255) {
        $alias = nv_clean_alias($title);
        $alias = strtolower($alias);
    }

    $sql = 'SELECT COUNT(id) FROM ' . SOLICITOR_TABLE . ' WHERE alias= :alias';
    if ($id > 0) {
        $sql .= ' AND id != ' . $id;
    }

    $stmt = $db->prepare($sql);
    $stmt->bindParam(':alias', $alias, PDO::PARAM_STR);
    $stmt->execute();
    $nb = $stmt->fetchColumn();
    if (!empty($nb)) {
        $alias .= '-' . (intval($nb) + 1);
    }

    return $alias;
}

function find_trim_address($string)
{
    $string = trim($string, '.'); // loại bỏ dấu chấm ở cuối
    $string = trim($string, ','); // loại bỏ dấu phảy ở cuối
    $string = trim($string, ';'); // loại bỏ dấu chấm phảy ở cuối
    $string = trim($string, '-');
    $string = str_replace('–', '-', $string); // Tòa nhà Agribank số 36 đường Nguyễn Cơ Thạch – P.Mỹ Đình 1 – Q. Nam Từ Liêm – Hà Nội

    $string = nv_compound_unicode($string);
    return trim($string);
}
