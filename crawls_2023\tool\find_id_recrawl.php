<?php

// Cập nhật ID cho TBMT
// SQL xem lại các loại tin cần bóc: SELECT `type`, count(*) FROM `nv23_crawls` GROUP BY `type`;
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

$vnd_id = 0;
do {
    $array_data = [];
    $array_tbmt = [];
    $array_kqmt = [];
    $array_kqlcnt = [];
    $_sql = 'SELECT `vnd_id`, `notifyno`, `notifyversion`, `vnd_id_tbmt`, `vnd_id_mothau`, `vnd_id_kqlcnt`, `bidclosedate`, `statusfornotify` FROM `nv23_url` WHERE `vnd_id` > ' . $vnd_id . ' AND vnd_id_kqlcnt <= 0 ORDER BY `vnd_id` ASC LIMIT 1000';
    $_result = $dbcr->query($_sql);
    $array_id = [];
    $vnd_id = 0;
    while ($row = $_result->fetch()) {
        $vnd_id = $row['vnd_id'];
        $_code = $row['notifyno'] . '-' . $row['notifyversion'];
        $array_id[$row['vnd_id']] = $_code;
        if ($row['vnd_id_tbmt'] <= 0) {
            $array_tbmt[$_code] = [
                'vnd_id' => $vnd_id,
                'id_tbmt' => -1,
                'id_mothau' => $row['vnd_id_mothau'] <=0 ? -1 : $row['vnd_id_mothau'],
                'id_kqlcnt' => $row['vnd_id_kqlcnt'] <=0 ? -1 : $row['vnd_id_kqlcnt'],
                'bidclosedate' => strtotime($row['bidclosedate']),
                'status' => $row['statusfornotify']
            ];
        }

        if ($row['vnd_id_mothau'] <= 0) {
            $array_kqmt[$_code] = [
                'vnd_id' => $vnd_id,
                'id_tbmt' => $row['vnd_id_tbmt'] <=0 ? -1 : $row['vnd_id_tbmt'],
                'id_mothau' => -1,
                'id_kqlcnt' => $row['vnd_id_kqlcnt'] <=0 ? -1 : $row['vnd_id_kqlcnt'],
                'bidclosedate' => strtotime($row['bidclosedate']),
                'status' => $row['statusfornotify']
            ];
        }

        if ($row['vnd_id_kqlcnt'] <= 0) {
            $array_kqlcnt[$_code] = [
                'vnd_id' => $vnd_id,
                'id_tbmt' => $row['vnd_id_tbmt'] <=0 ? -1 : $row['vnd_id_tbmt'],
                'id_mothau' => $row['vnd_id_mothau'] <=0 ? -1 : $row['vnd_id_mothau'],
                'id_kqlcnt' => -1,
                'bidclosedate' => strtotime($row['bidclosedate']),
                'status' => $row['statusfornotify']
            ];
        }
        $array_data = array_merge($array_tbmt, $array_kqmt, $array_kqlcnt);
    }
    $_result->closeCursor();

    if ($vnd_id > 0) {
        if (!empty($array_tbmt)) {
            $_sql = "SELECT `id`, `so_tbmt` FROM `nv4_vi_bidding_row` WHERE `so_tbmt` IN (" . implode(", ", array_map([$db, 'quote'], array_keys($array_tbmt))) . ")";
            $_result = $db->query($_sql);
            while ($row = $_result->fetch()) {
                $array_data[$row['so_tbmt']]['id_tbmt'] = $row['id'];
            }
            $_result->closeCursor();
        }

        if (!empty($array_kqmt)) {
            $_sql = "SELECT `so_tbmt` FROM `nv4_vi_bidding_open` WHERE `so_tbmt` IN (" . implode(", ", array_map([$db, 'quote'], array_keys($array_kqmt))) . ")";
            $_result = $db->query($_sql);
            while ($row = $_result->fetch()) {
                $array_data[$row['so_tbmt']]['id_mothau'] = 1;
            }
            $_result->closeCursor();
        }

        if (!empty($array_kqlcnt)) {
            $_sql = "SELECT id, `code` FROM `nv4_vi_bidding_result` WHERE `code` IN (" . implode(", ", array_map([$db, 'quote'], array_keys($array_kqlcnt))) . ")";
            $_result = $db->query($_sql);
            while ($row = $_result->fetch()) {
                $array_data[$row['code']]['id_kqlcnt'] = $row['id'];
            }
            $_result->closeCursor();
        }
        unset($array_id);
        try {
            foreach ($array_data as $_code => $row) {
                $_code_arr = explode('-', $_code); //$dbcr->query
                $_sql = 'UPDATE `nv23_url` SET `vnd_id_tbmt` = ' . $row['id_tbmt'] . ', `vnd_id_mothau` = ' . $row['id_mothau'] . ', `vnd_id_kqlcnt` = ' . $row['id_kqlcnt'] . ' WHERE `vnd_id`=' . $row['vnd_id'];
                $_result = $dbcr->query($_sql);
                if ($row['id_mothau'] > 0 && $row['id_kqlcnt'] <= 0 && $row['status'] != 'DHT' && $row['status'] != 'DHTBMT') {
                    $today = mktime(0, 0 ,0, date('n'), date('j'), date('Y'));
                    $itsdate = mktime(0, 0 ,0, date('n', $row['bidclosedate']), date('j', $row['bidclosedate']), date('Y', $row['bidclosedate']));
                    $diff = floor(($today - $itsdate) / 86400);
                    if ($diff >= 20 && ($diff % 20 == 0) && $diff <= 60) {
                        $dbcr->exec('UPDATE nv23_crawls_tbmt SET url_run = 0, uniqid ="" WHERE dauthau_info=' . $row['id_tbmt']);
                    }
                }
            }
        } catch (PDOException $e) {
            print_r($e);
            die($_sql);
        }
    }
    unset($array_data);
    echo $vnd_id . "\n";
} while ($vnd_id > 0);
