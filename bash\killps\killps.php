<?php
$killps = file_get_contents('/home/<USER>/private/killps/killps.txt');
$array_data = explode("\n", $killps);

$kill_arr = [];
foreach ($array_data as $input_line) {

    # USER PID %CPU %MEM VSZ RSS TTY STAT START TIME COMMAND
    # craws 13451 0.2 3.4 613956 65480 ? S 12:29 0:14 php /home/<USER>/private/crawls/check_kq_url/check_kqonline_url.php
    if (preg_match('/craws\s*([0-9]+)\s*([0-9\.]+)\s*([0-9\.]+)\s*([0-9]+)\s*([0-9]+)\s*(.*)\s([0-9]{1,2}:[0-9]{1,2})\s*([0-9]+)\:([0-9]+)\s*php/', $input_line, $m)) {
        $run_time = $m[9] + $m[8] * 60;
        if ($run_time > 20 and $m[5] > 200 * 1024) { // (in KiB)
            $kill_arr[] = "kill " . $m[1];
        }
    }
}

if (!empty($kill_arr)) {
    file_put_contents('/home/<USER>/private/killps/kill.sh', "#!/bin/bash\n\n" . implode("\n", $kill_arr) . "\n");
    file_put_contents('/home/<USER>/private/killps/kill_' . date('Ymd') . '.log', date('H:i') . "\n" . implode("\n", $kill_arr) . "\n", FILE_APPEND);
}
