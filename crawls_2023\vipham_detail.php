<?php

// L<PERSON>y danh sách: <PERSON>h sách tổ chức, cá nhân vi phạm
// https://muasamcong.mpi.gov.vn/web/guest/organizations-violators
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

try {

    $limit = 10;
    $uniqid = uniqid('', true);
    $dbcr->query("UPDATE `nv22_organizations_violators_url` SET url_run='-" . NV_CURRENTTIME . "', count_url=count_url+1, uniqid='" . $uniqid . "' WHERE url_run=0 AND uniqid='' ORDER BY id DESC LIMIT " . $limit);
    $query_url = $dbcr->query("SELECT * FROM nv22_organizations_violators_url WHERE `uniqid`='" . $uniqid . "' LIMIT " . $limit);
    while ($_data = $query_url->fetch()) {
        $num_run = 0;
        $detail2 = geturlpage($_data, 1);
        if (isset($detail2['decisionNo'])) {
            $dbcr->query("UPDATE nv22_organizations_violators_url SET url_run=" . NV_CURRENTTIME . ", detail2  =" . $dbcr->quote(json_encode($detail2, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT)) . ", dauthau_info=1 WHERE id=" . $_data['id']);
            echo "OK\n";
            $result = getdata($detail2);
            print_r("\n \033[33m-------- Bat dau convert organizations_violators url id: \033[32m" . $_data['id'] . "\033(B\033[m\n");

            if ($result['status'] == 'success' && empty($result['error'])) {
                $array_data = $result['data'];
                $array_data['name'] = !empty($_data['orgname']) ? $_data['orgname'] : '';
                $array_data['status'] = !empty($_data['status']) ? $_data['status'] : '';
                $array_data['alias'] = change_alias($_data['orgname']);
                $_userViolates = !empty($array_data['userViolates']) ? json_encode($array_data['userViolates']) : '';
                $row_old = $db->query('SELECT * FROM ' . $config['prefix'] . '_vi_organizations_violators_row WHERE decisionno=' . $db->quote($array_data['decisionno']))
                    ->fetch();

                try {
                    $id = 0;
                    if (!empty($row_old) and $row_old['id'] > 0) {
                        echo "...Bat dau update nv4_vi_organizations... \n";
                        $array_data = nv_compound_unicode_recursion($array_data);
                        $id = intval($row_old['id']);

                        if (empty($array_data['date_post'])) {
                            $array_data['date_post'] = $row_old['date_post'];
                        }
                        if (empty($array_data['alias'])) {
                            $array_data['alias'] = $row_old['alias'];
                        }

                        $stmt = $db->prepare("UPDATE `nv4_vi_organizations_violators_row` SET `name`=:name, `name_violate`=:name_violate, `alias`=:alias, `date_post`=:date_post, `decisionno` = :decisionno, `issued_date` = :issued_date, `status` = :status, `position` = :position, `repdecisionno` = :repdecisionno, `file_name` = :file_name, `file_id` = :file_id, translator = 0 WHERE id=" . $id);
                        $stmt->bindParam(':name', $array_data['name'], PDO::PARAM_STR); //
                        $stmt->bindParam(':name_violate', $array_data['name_violate'], PDO::PARAM_STR); //
                        $stmt->bindParam(':alias', $array_data['alias'], PDO::PARAM_STR);
                        $stmt->bindParam(':date_post', $array_data['date_post'], PDO::PARAM_STR);
                        $stmt->bindParam(':decisionno', $array_data['decisionno'], PDO::PARAM_STR);
                        $stmt->bindParam(':issued_date', $array_data['issued_date'], PDO::PARAM_STR);
                        $stmt->bindParam(':status', $array_data['status'], PDO::PARAM_STR);
                        $stmt->bindParam(':position', $array_data['position'], PDO::PARAM_STR);
                        $stmt->bindParam(':repdecisionno', $array_data['repdecisionno'], PDO::PARAM_STR);
                        $stmt->bindParam(':file_name', $array_data['file_name'], PDO::PARAM_STR);
                        $stmt->bindParam(':file_id', $array_data['file_id'], PDO::PARAM_STR);
                        $exc = $stmt->execute();
                        $id_row = $id;
                        // // Get data after insert
                        // $row_new = $db->query('SELECT * FROM nv4_vi_organizations_violators_row WHERE id = ' . $id)->fetch();

                        // $status_insert = insert_log_crawls($id, 'DAMCB', $row_old, $row_new);
                        // if ($status_insert > 0) {
                        // echo ("LOG: DAMCB - ID: " . $id . "- OK");
                        // }
                    } else {
                        $array_data = nv_compound_unicode_recursion($array_data);
                        echo "...Bat dau insert nv4_vi_organizations_row... \n";
                        $stmt = $db->prepare("INSERT INTO `nv4_vi_organizations_violators_row`(name, name_violate, alias, decisionno, date_post, issued_date, status, position, repdecisionno, file_name, file_id) VALUES (:name, :name_violate, :alias, :decisionno, :date_post, :issued_date, :status, :position, :repdecisionno, :file_name, :file_id)");
                        $stmt->bindParam(':name', $array_data['name'], PDO::PARAM_STR); //
                        $stmt->bindParam(':name_violate', $array_data['name_violate'], PDO::PARAM_STR); //
                        $stmt->bindParam(':alias', $array_data['alias'], PDO::PARAM_STR);
                        $stmt->bindParam(':date_post', $array_data['date_post'], PDO::PARAM_STR);
                        $stmt->bindParam(':decisionno', $array_data['decisionno'], PDO::PARAM_STR);
                        $stmt->bindParam(':issued_date', $array_data['issued_date'], PDO::PARAM_STR);
                        $stmt->bindParam(':status', $array_data['status'], PDO::PARAM_STR);
                        $stmt->bindParam(':position', $array_data['position'], PDO::PARAM_STR);
                        $stmt->bindParam(':repdecisionno', $array_data['repdecisionno'], PDO::PARAM_STR);
                        $stmt->bindParam(':file_name', $array_data['file_name'], PDO::PARAM_STR);
                        $stmt->bindParam(':file_id', $array_data['file_id'], PDO::PARAM_STR);
                        $exc = $stmt->execute();
                        $id_row = $db->lastInsertId();
                    }
                    if ($exc) {
                        $project = $array_data['userViolates'];
                        // $violators_detail = $db->query('SELECT id FROM nv4_vi_organizations_violators_detail WHERE id_row=' . $id_row)
                        // ->fetchColumn();
                        // if ($violators_detail > 0) {
                        // $stmt = $db->prepare("UPDATE `nv4_vi_organizations_violators_detail` SET `id_row`=:id_row, `name`=:name, `address`=:address, `effdate`=:effdate, `expdate`=:expdate, `contractnumber`=:contractnumber, `pentypename`=:pentypename, `pentype`=:pentype, `des`=:des, `penunit`=:penunit, `orgcode`=:orgcode, `role`=:role, `shoppingmethod`=:shoppingmethod, `orderid`=:orderid, `pencontent`=:pencontent, `bussiness_id`=:bussiness_id WHERE id=" . $id);
                        // if (!empty($array_data['userViolates'])) {
                        // $bussiness_id = 0;
                        // foreach ($array_data['userViolates'] as $pro) {
                        // $name_ct = $pro['name'];
                        // $address = $pro['address'];
                        // $effdate = 0;
                        // if (!empty($pro['effDate'])) {
                        // $effdate = strtotime($pro['effDate']);
                        // }
                        // $expdate = 0;
                        // if (!empty($pro['expDate'])) {
                        // $expdate = strtotime($pro['expDate']);
                        // }
                        // $contractnumber = $pro['contractNumber'] ?? '';
                        // $pentypename = $pro['penTypeName'] ?? '';
                        // $pentype = $pro['penType'] ?? '';
                        // $des = $pro['des'] ?? '';
                        // $penunit = $pro['penUnit'] ?? '';
                        // $orgcode = $pro['orgCode'] ?? '';
                        // $role = $pro['role'];
                        // $shoppingmethod = $pro['shoppingMethod'] ?? '';
                        // $orderid = $pro['orderId'] ?? '';
                        // $pencontent = $pro['penContent'] ?? '';
                        // if (!empty($orgcode)) {
                        // $new_sql = 'SELECT id FROM ' . BUSINESS_PREFIX_GLOBAL . '_info WHERE orgcode = ' . $db->quote($orgcode);
                        // $new_query = $db->query($new_sql);
                        // $new_count = $new_query->rowCount();
                        // if ($new_count == 1) {
                        // $new_row = $new_query->fetch();
                        // $bussiness_id = $new_row['id'];
                        // }
                        // } else {
                        // $new_sql_info = 'SELECT companyname, id FROM ' . BUSINESS_PREFIX_GLOBAL . '_info WHERE companyname = ' . $db->quote($name_ct);
                        // $new_query_info = $db->query($new_sql_info);
                        // $new_count_info = $new_query_info->rowCount();
                        // if ($new_count_info == 1) {
                        // $new_row_info = $new_query_info->fetch();
                        // $bussiness_id = $new_row_info['id'];
                        // }
                        // }
                        // try {
                        // $stmt1->bindParam(':id_row', $id_row, PDO::PARAM_INT); //
                        // $stmt1->bindParam(':name', $name_ct, PDO::PARAM_STR); //
                        // $stmt1->bindParam(':address', $address, PDO::PARAM_STR); //
                        // $stmt1->bindParam(':effdate', $effdate, PDO::PARAM_STR); //
                        // $stmt1->bindParam(':expdate', $expdate, PDO::PARAM_STR); //
                        // $stmt1->bindParam(':contractnumber', $contractnumber, PDO::PARAM_STR); //
                        // $stmt1->bindParam(':pentypename', $pentypename, PDO::PARAM_STR); //
                        // $stmt1->bindParam(':pentype', $pentype, PDO::PARAM_STR); //
                        // $stmt1->bindParam(':des', $des, PDO::PARAM_STR); //
                        // $stmt1->bindParam(':penunit', $penunit, PDO::PARAM_STR); //
                        // $stmt1->bindParam(':orgcode', $orgcode, PDO::PARAM_STR); //
                        // $stmt1->bindParam(':role', $role, PDO::PARAM_STR); //
                        // $stmt1->bindParam(':shoppingmethod', $shoppingmethod, PDO::PARAM_STR); //
                        // $stmt1->bindParam(':orderid', $orderid, PDO::PARAM_STR); //
                        // $stmt1->bindParam(':pencontent', $pencontent, PDO::PARAM_STR); //
                        // $stmt1->bindParam(':bussiness_id', $bussiness_id, PDO::PARAM_STR); //
                        // $stmt1->execute();
                        // echo "News: " . $id_row . " \n";
                        // } catch (PDOException $e1) {
                        // if (!preg_match('/Integrity constraint violation\: ([0-9]+) Duplicate entry (.*) for key \'id_row\'/', $e1->getMessage())) {
                        // // print_r($row);
                        // print_r($e1);
                        // $db->rollBack();
                        // exit();
                        // }
                        // }
                        // }
                        // $stmt1->closeCursor();
                        // }
                        // } else {
                        $db->exec('DELETE FROM nv4_vi_organizations_violators_detail WHERE id_row=' . $id_row);
                        echo "...Bat dau insert nv4_vi_organizations_detail... \n";
                        $pro = [];
                        $stmt1 = $db->prepare("INSERT INTO `nv4_vi_organizations_violators_detail`(id_row, name, address, effdate, expdate, contractnumber, pentypename, pentype, des, penunit, orgcode, role, shoppingmethod, orderid, pencontent, bussiness_id) VALUES (:id_row, :name, :address, :effdate, :expdate, :contractnumber, :pentypename, :pentype, :des, :penunit, :orgcode, :role, :shoppingmethod, :orderid, :pencontent, :bussiness_id)");
                        if (!empty($array_data['userViolates'])) {
                            $bussiness_id = 0;
                            foreach ($array_data['userViolates'] as $pro) {
                                $name_ct = $pro['name'];
                                $address = $pro['address'];
                                $effdate = 0;
                                if (!empty($pro['effDate'])) {
                                    $effdate = strtotime($pro['effDate']);
                                }
                                $expdate = 0;
                                if (!empty($pro['expDate'])) {
                                    $expdate = strtotime($pro['expDate']);
                                }
                                $contractnumber = $pro['contractNumber'] ?? '';
                                $pentypename = $pro['penTypeName'] ?? '';
                                $pentype = $pro['penType'] ?? '';
                                $des = $pro['des'] ?? '';
                                $penunit = $pro['penUnit'] ?? '';
                                $orgcode = $pro['orgCode'] ?? '';
                                $role = $pro['role'] ?? '';
                                $shoppingmethod = $pro['shoppingMethod'] ?? '';
                                $orderid = $pro['orderId'] ?? '';
                                $pencontent = $pro['penContent'] ?? '';
                                if (!empty($orgcode)) {
                                    $new_sql = 'SELECT id FROM ' . BUSINESS_PREFIX_GLOBAL . '_info WHERE orgcode = ' . $db->quote($orgcode);
                                    $new_query = $db->query($new_sql);
                                    $new_count = $new_query->rowCount();
                                    if ($new_count == 1) {
                                        $new_row = $new_query->fetch();
                                        $bussiness_id = $new_row['id'];
                                        $db->exec('UPDATE ' . BUSINESS_PREFIX_GLOBAL . '_info SET update_data = 0 WHERE id = ' . $bussiness_id);
                                    }
                                } else {
                                    $new_sql_info = 'SELECT companyname, id FROM ' . BUSINESS_PREFIX_GLOBAL . '_info WHERE companyname = ' . $db->quote($name_ct);
                                    $new_query_info = $db->query($new_sql_info);
                                    $new_count_info = $new_query_info->rowCount();
                                    if ($new_count_info == 1) {
                                        $new_row_info = $new_query_info->fetch();
                                        $bussiness_id = $new_row_info['id'];
                                        $db->exec('UPDATE ' . BUSINESS_PREFIX_GLOBAL . '_info SET update_data = 0 WHERE id = ' . $bussiness_id);
                                    }
                                }
                                try {
                                    $stmt1->bindParam(':id_row', $id_row, PDO::PARAM_INT); //
                                    $stmt1->bindParam(':name', $name_ct, PDO::PARAM_STR); //
                                    $stmt1->bindParam(':address', $address, PDO::PARAM_STR); //
                                    $stmt1->bindParam(':effdate', $effdate, PDO::PARAM_STR); //
                                    $stmt1->bindParam(':expdate', $expdate, PDO::PARAM_STR); //
                                    $stmt1->bindParam(':contractnumber', $contractnumber, PDO::PARAM_STR); //
                                    $stmt1->bindParam(':pentypename', $pentypename, PDO::PARAM_STR); //
                                    $stmt1->bindParam(':pentype', $pentype, PDO::PARAM_STR); //
                                    $stmt1->bindParam(':des', $des, PDO::PARAM_STR); //
                                    $stmt1->bindParam(':penunit', $penunit, PDO::PARAM_STR); //
                                    $stmt1->bindParam(':orgcode', $orgcode, PDO::PARAM_STR); //
                                    $stmt1->bindParam(':role', $role, PDO::PARAM_STR); //
                                    $stmt1->bindParam(':shoppingmethod', $shoppingmethod, PDO::PARAM_STR); //
                                    $stmt1->bindParam(':orderid', $orderid, PDO::PARAM_STR); //
                                    $stmt1->bindParam(':pencontent', $pencontent, PDO::PARAM_STR); //
                                    $stmt1->bindParam(':bussiness_id', $bussiness_id, PDO::PARAM_STR); //
                                    $stmt1->execute();

                                    // Cập nhật số lần vi phạm cho nhà thầu
                                    if (!empty($bussiness_id)) {
                                        $num_violations = $db->query('SELECT COUNT(DISTINCT(id_row)) as total FROM nv4_vi_organizations_violators_detail WHERE bussiness_id = ' . $bussiness_id)->fetchColumn();
                                        if (!empty($num_violations)) {
                                            $exc = $db->exec("UPDATE " . BUSINESS_PREFIX_GLOBAL . "_info SET num_violations = " . $num_violations . " WHERE id=" . $bussiness_id);
                                        }
                                    }
                                    echo "\033[35mNews: " . $id_row . " \033(B\033[m\n";
                                } catch (PDOException $e1) {
                                    if (!preg_match('/Integrity constraint violation\: ([0-9]+) Duplicate entry (.*) for key \'id_row\'/', $e1->getMessage())) {
                                        // print_r($row);
                                        print_r($e1);
                                    }
                                }
                            }
                        }
                        $stmt1->closeCursor();
                        // }
                    }
                } catch (PDOException $e) {
                    print_r("\033[31m- Da xay ra loi trong qua trinh convert, organizations_violators url id: " . $_data['id'] . "\033(B\033[m\n");
                    print_r($e);
                    nv_insert_log('error_' . date('d_m_y'), print_r($e, true));
                    $dbcr->query("UPDATE nv22_organizations_violators_url SET dauthau_info=-2, err_code = 3000 WHERE id=" . $_data['id']);
                }
            } else {
                print_r("\033[31m- Da xay ra loi trong qua trinh convert, organizations_violators url id: " . $_data['id'] . "\033(B\033[m\n");
                print_r($result['error']);
                $dbcr->query("UPDATE nv22_organizations_violators_url SET dauthau_info=-1, err_code = '" . implode(",", $result['err_code']) . "' WHERE id=" . $_data['id']);
                nv_insert_log('error_' . date('d_m_y'), $result['error']);
            }
        } else {
            if (empty($detail2['http_code'])) {
                $detail2['http_code'] = NV_CURRENTTIME;
            }
            $dbcr->query("UPDATE nv22_organizations_violators_url SET url_run='-" . $detail2['http_code'] . "', detail2    =" . $dbcr->quote(json_encode($detail2, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT)) . " WHERE id=" . $_data['id']);
            echo "NO: bidoBidStatus\n";
            // print_r($info);
        }
        sleep(1);
    }

    echo "Đã chạy hết các url\n\n";
    echo "\nThoi gian thuc hien = " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";
} catch (PDOException $e) {
    print_r($e);
    die("Dừng để test\n\n");
}

function getdata($detail2)
{
    global $db, $dbcr;

    $err_code = [];
    $error = [];

    // name_violate -> orgName
    $array_data['name_violate'] = (!empty($detail2['orgName']) ? $detail2['orgName'] : '');
    if (empty($array_data['name_violate'])) {
        $err_code[] = 2003;
        $error[] = 'name_violate is required';
    }

    $array_data['decisionno'] = !empty($detail2['decisionNo']) ? $detail2['decisionNo'] : '';
    if (empty($array_data['decisionno'])) {
        $err_code[] = 2004;
        $error[] = 'decisionno is required';
    }

    $detail2['createdDate'] = !empty($detail2['createdDate']) ? $detail2['createdDate'] : '';
    $array_data['date_post'] = 0;
    if (!empty($detail2['createdDate'])) {
        $array_data['date_post'] = strtotime($detail2['createdDate']);
    }

    $file_id = $file_name = $repdecisionno = $version = $position = $issued_date = $userViolates = [];
    if (!empty($detail2['violates'])) {
        $violates = [];
        foreach ($detail2['violates'] as $violates) {
            $file_name = $violates['fileName'];
            $file_id = $violates['fileId'];
            $repdecisionno = $violates['repDecisionNo'];
            $version = $violates['version'];
            $position = $violates['position'];
            $issued_date = $detail2['issuedDate'];
            $userViolates = $violates['userViolates'];
        }
    }
    $array_data['file_id'] = $file_id;
    $array_data['file_name'] = $file_name;
    $array_data['repdecisionno'] = $repdecisionno ?? '';
    $array_data['version'] = $version;
    $array_data['position'] = $position ?? '';
    $array_data['issued_date'] = 0;
    if (!empty($issued_date)) {
        $array_data['issued_date'] = strtotime($issued_date);
    }
    $array_data['userViolates'] = $userViolates;

    $result = [];
    if (!empty($error)) {
        $result['status'] = 'error';
        $result['error'] = $error;
        $result['err_code'] = $err_code;
    } else {
        $result['status'] = 'success';
        $result['data'] = $array_data;
    }

    return $result;
}

function geturlpage($_row, $reload = 1)
{
    global $dbcr, $num_run;
    ++$num_run;

    $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-org-ind-violating/services/get-detail-violation';
    $body = '{"idViolateDec":"' . $_row['id_msc'] . '"}';
    // $referer = 'https://muasamcong.mpi.gov.vn/web/guest/organizations-violators?p_p_id=violationthymeleafuiportlet_WAR_egpportalviolation&p_p_lifecycle=0&p_p_state=normal&p_p_mode=view&_violationthymeleafuiportlet_WAR_egpportalviolation_render=detailv2&id=' . $_row['id_msc'] . '&decisionNo=' . $_row['decisionno'] . '&orgName=' . str_replace(' ', '%20', $_row['orgname']);
    $referer = 'https://muasamcong.mpi.gov.vn/web/guest/organizations-violators?p_p_id=egpportalorgindviolating_WAR_egpportalorgindviolating&p_p_lifecycle=0&p_p_state=normal&p_p_mode=view&_egpportalorgindviolating_WAR_egpportalorgindviolating_render=detail&id=' . $_row['id_msc'];
    $arr_agent = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:104.0) Gecko/20100101 Firefox/104.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/105.0.1343.42',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 9.0; SAMSUNG SM-F900U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.77 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ];

    srand((int) microtime() * 10000000);
    $rand = array_rand($arr_agent);
    $agent = $arr_agent[$rand];
    $_proxy = $dbcr->query("SELECT * FROM nv4_proxy WHERE status=2 ORDER BY lasttime ASC LIMIT 1")->fetch();
    $ch = curl_init();
    if (isset($_proxy['proxy'])) {
        $dbcr->query("UPDATE nv4_proxy SET lasttime = " . time() . ", number_run=number_run+1 WHERE id = " . $_proxy['id']);
        echo $_proxy['proxy'] . "\n";
        curl_setopt($ch, CURLOPT_PROXY, $_proxy['proxy']);
        if (isset($_proxy['proxyuserpwd']) and !empty($_proxy['proxyuserpwd'])) {
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, $_proxy['proxyuserpwd']);
        }
        curl_setopt($ch, CURLOPT_PROXYTYPE, $_proxy['proxytype']);
        curl_setopt($ch, CURLOPT_PROXYPORT, $_proxy['proxyport']);
    }
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 60);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
    curl_setopt($ch, CURLOPT_REFERER, $referer);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json'
    ));

    $json = curl_exec($ch);
    curl_close($ch);
    $data = json_decode($json, true);
    // var_dump($data);die;
    if (isset($data['decisionNo'])) {
        return $data;
    } elseif ($reload and $num_run < 5) {
        return geturlpage($_row, 1);
    } elseif ($reload) {
        return geturlpage($_row, 0);
    }
    return [];
}
