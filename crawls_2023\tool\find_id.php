<?php

// Cập nhật ID cho TBMT
// SQL xem lại các loại tin cần bóc: SELECT `type`, count(*) FROM `nv23_crawls` GROUP BY `type`;
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

$vnd_id = 0;
do {
    $array_data = [];
    $_sql = 'SELECT `vnd_id`, `notifyno`, `notifyversion` FROM `nv23_url` WHERE `vnd_id` > ' . $vnd_id . ' ORDER BY `vnd_id` ASC LIMIT 1000';
    $_result = $dbcr->query($_sql);
    $array_id = [];
    $vnd_id = 0;
    while ($row = $_result->fetch()) {
        $vnd_id = $row['vnd_id'];
        $_code = $row['notifyno'] . '-' . $row['notifyversion'];
        $array_id[$row['vnd_id']] = $_code;
        $array_data[$_code] = ['vnd_id' => $vnd_id, 'id_tbmt' => -1, 'id_mothau' => -1, 'id_kqlcnt' => -1];
    }
    $_result->closeCursor();

    if ($vnd_id > 0) {

        $_sql = "SELECT `id`, `so_tbmt` FROM `nv4_vi_bidding_row` WHERE `so_tbmt` IN ('" . implode("', '", $array_id) . "')";
        $_result = $db->query($_sql);
        while ($row = $_result->fetch()) {
            $array_data[$row['so_tbmt']]['id_tbmt'] = $row['id'];
        }
        $_result->closeCursor();

        $_sql = "SELECT `so_tbmt` FROM `nv4_vi_bidding_open` WHERE `so_tbmt` IN ('" . implode("', '", $array_id) . "')";
        $_result = $db->query($_sql);
        while ($row = $_result->fetch()) {
            $array_data[$row['so_tbmt']]['id_mothau'] = 1;
        }
        $_result->closeCursor();

        $_sql = "SELECT id, `code` FROM `nv4_vi_bidding_result` WHERE `code` IN ('" . implode("', '", $array_id) . "')";
        $_result = $db->query($_sql);
        while ($row = $_result->fetch()) {
            $array_data[$row['code']]['id_kqlcnt'] = $row['id'];
        }
        $_result->closeCursor();
        unset($array_id);
        try {
            foreach ($array_data as $_code => $row) {
                $_code_arr = explode('-', $_code); //$dbcr->query
                $_sql = 'UPDATE `nv23_url` SET `vnd_id_tbmt` = ' . $row['id_tbmt'] . ', `vnd_id_mothau` = ' . $row['id_mothau'] . ', `vnd_id_kqlcnt` = ' . $row['id_kqlcnt'] . ' WHERE `vnd_id`=' . $row['vnd_id'];
                $_result = $dbcr->query($_sql);
                //echo $_sql . "\n";
            }
        } catch (PDOException $e) {
            print_r($e);
            die($_sql);
        }
    }
    unset($array_data);
    echo $vnd_id . "\n";
} while ($vnd_id > 0);
