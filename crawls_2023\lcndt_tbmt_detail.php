<?php
/**
 * @Project Crawls.DauThau.Info
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2022 VINADES.,JSC. All rights reserved
 * @Code Ngọc <PERSON> (<EMAIL>)
 * Tool bóc danh sách: Thông báo mời đầu tư
 * https://vinades.org/dauthau/dauthau.info/-/issues/1847
 */

define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME))));
require NV_ROOTDIR . '/mainfile.php';

$id = (int) $request_mode->get('id', 0); // php kqlcnt_detail.php --id=285003
if ($id > 0) {
    $_tbmdt = $dbcr->query('SELECT * FROM `nv23_lcndt_tbmt_url` WHERE id = ' . $id)->fetch();
} else {
    $uniqid = uniqid('', true);
    //$dbcr->query("UPDATE nv23_lcndt_tbmt_url SET url_run='-" . NV_CURRENTTIME . "', count_url=count_url+1, uniqid='" . $uniqid . "'  WHERE id = 11");
    $dbcr->query("UPDATE nv23_lcndt_tbmt_url SET url_run='-" . NV_CURRENTTIME . "', count_url=count_url+1, uniqid='" . $uniqid . "'  WHERE `url_run`=0 AND uniqid='' ORDER BY `id` DESC LIMIT 1");
    $_tbmdt = $dbcr->query("SELECT * FROM `nv23_lcndt_tbmt_url` WHERE `uniqid`='" . $uniqid . "' ORDER BY `id` DESC LIMIT 1")->fetch();
}

if (!empty($_tbmdt)) {
    echo ("id TBMT: " . $_tbmdt['id'] . "\n");
    $num_run = 0;
    $data = geturlpage($_tbmdt, 1);
    if (isset($data['notifyNo'])) {
        $dbcr->query("UPDATE nv23_lcndt_tbmt_url SET url_run=" . NV_CURRENTTIME . ", detail2	=" . $dbcr->quote(json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT)) . ", dauthau_info='-1' WHERE id=" . $_tbmdt['id']);
        echo "OK\n";
        getdata($_tbmdt, $data);
    } else {
        if (empty($info['http_code'])) {
            $info['http_code'] = NV_CURRENTTIME;
        }
        $dbcr->query("UPDATE nv23_lcndt_tbmt_url SET url_run='-" . $info['http_code'] . "', detail2	=" . $dbcr->quote(json_encode($info, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT)) . " WHERE id=" . $_tbmdt['id']);
        echo "NO: notifyNo\n";
        print_r($info);
    }
    echo "\nThoi gian thuc hien = " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";
} else {
    echo "No Data\n";
}


function getdata($_tbmdt, $detail)
{
    global $db, $dbcr, $array_name_staff;

    $date = [
        'D' => 'Ngày',
        'M' => 'Tháng',
        'Y' => 'Năm'
    ];

    $bidGuaranteeForm  = [
        'DC' => 'Đặt cọc',
        'KQ' => 'Ký quỹ',
        'TBL' => 'Thư bảo lãnh của tổ chức tín dụng hoặc chi nhánh ngân hàng nước ngoài được thành lập theo pháp luật Việt Nam'
    ];

    $row = array();
    $content = '';
    if (!empty($detail['content']) and $detail['content'] != '{}') {
        $result = str_replace(array("\n", "\r", "\t"), '', $detail['content']);
        $content = json_decode($result, true);
    } else if (!empty($detail['contentOld']) and $detail['contentOld'] != '{}') {
        $result = str_replace(array("\n", "\r", "\t"), '', $detail['contentOld']);
        $content = json_decode($result, true);
    }

    $content_vi = !empty($content) ? $content['VI'] : '';
    $content_en = !empty($content) ? $content['EN'] : '';
    if (!empty($content_vi)) {
        foreach ($content_vi as $key => $ctvi) {
            if ($ctvi == 'null' or $ctvi == 'N/A') {
                $content_vi[$key] = '';
            }
        }
    }
    if (!empty($content_en)) {
        foreach ($content_en as $key => $cten) {
            if ($cten == 'null' or $cten == 'N/A') {
                $content_en[$key] = '';
            }
        }
    }
    $row['content_vi'] = json_encode($content_vi, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    $row['content_en'] = json_encode($content_en, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    $row['id_msc'] = $detail['id'];

    // Quyết định
    $quyet_dinh = [
        'approvalNo' => isset($detail['approvalNo']) ? $detail['approvalNo'] : '',
        'approvalAgency' => isset($detail['approvalAgency']) ? $detail['approvalAgency'] : '',
        'approvalDate' => isset($detail['approvalDate']) ? strtotime($detail['approvalDate']) : 0,
        'approvalFileName' => isset($detail['approvalFileName']) ? $detail['approvalFileName'] : '',
        'approvalFileId' => isset($detail['approvalFileId']) ? $detail['approvalFileId'] : ''
    ];

    foreach ($quyet_dinh as $key => $qd) {
        if ($qd == 'null' or $qd == 'N/A') {
            $quyet_dinh[$key] = '';
        }
    }

    if ($_tbmdt['type_project'] == 'es-bido-notify-invest-sdd-p') {
        $row['loai_hop_dong'] = 'Hợp đồng dự án đầu tư có sử dụng đất';
        $row['ten_du_an'] = isset($detail['projectName']) ? $detail['projectName'] : '';
        $row['ma_du_an'] = isset($detail['projectNo']) ? $detail['projectNo'] : '';
        $row['tong_chi_phi_thuc_hien_du_an'] = !empty($content_vi['feeTotal']) ? $content_vi['feeTotal'] : 0;
        $row['so_hieu_khlcnt'] = isset($detail['planNo']) ? $detail['planNo'] . '-' . $detail['planVersion'] : '';
        $row['thoi_diem_dong_thau'] = isset($detail['closeDate']) ? strtotime($detail['closeDate']) : 0;
        $row['thoi_diem_mo_thau'] = isset($detail['openDate']) ? strtotime($detail['openDate']) : 0;
        $row['muc_tieu_cua_du_an'] = isset($content_vi['investTarget']) ? $content_vi['investTarget'] : '';
        $row['quy_mo_du_an'] = isset($detail['investScale']) ? $detail['investScale'] : '';
        $row['linh_vuc_thong_bao'] = 7;
        $row['dien_tich_dat'] = isset($content_vi['landArea']) ? $content_vi['landArea'] : '';
        $row['co_quan_tham_quyen'] = isset($content_vi['agencyName']) ? $content_vi['agencyName'] : '';
        $id_msc_kh = isset($detail['planId']) ? $detail['planId'] : '';
        $row['dia_diem_phat_hanh_hsmt'] = '';
        $row['thoi_gian_thuc_hien'] = '';
        // Lấy thông tin thời gian thực hiện hợp đồng
        if (!empty($id_msc_kh)) {
            $_khlcndt = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_bidding_plans_project WHERE id_msc=' . $db->quote($id_msc_kh))->fetch();
            if (!empty($_khlcndt)) {
                $row['thoi_gian_thuc_hien'] = $_khlcndt['thoi_gian_thuc_hien'];
            } else {
                $_khlcndt = [];
                $_khlcndt['id_msc'] = $id_msc_kh;
                $_khlcndt['planno'] = $row['so_hieu_khlcnt'];
                if (!empty($_khlcndt['id_msc']) and !empty($_khlcndt['planno'])) {
                    $row['thoi_gian_thuc_hien'] = getkhlcndt($_khlcndt, 1);
                }
            }
        }

    } else if ($_tbmdt['type_project'] == 'es-bido-notify-invest-ppp-p') {
        $row['ten_du_an'] = $detail['pname'] ?? '';
        $row['ma_du_an'] = $detail['pcode'] ?? '';
        $row['tong_chi_phi_thuc_hien_du_an'] = isset($detail['investTotal']) ? $detail['investTotal'] : 0;
        $row['so_hieu_khlcnt'] = '';
        $row['thoi_diem_dong_thau'] = isset($detail['bidCloseDate']) ? strtotime($detail['bidCloseDate']) : 0;
        $row['thoi_diem_mo_thau'] = isset($detail['bidOpenDate']) ? strtotime($detail['bidOpenDate']) : 0;
        $row['muc_tieu_cua_du_an'] = isset($detail['investTarget']) ? $detail['investTarget'] : '';
        $row['quy_mo_du_an'] = isset($detail['investScale']) ? $detail['investScale'] : '';
        $row['linh_vuc_thong_bao'] = 8;
        $row['dien_tich_dat'] = isset($detail['landArea']) ? $detail['landArea'] : '';
        $row['co_quan_tham_quyen'] = '';
        $row['loai_hop_dong'] = isset($detail['ctype']) ? $detail['ctype'] : '';
        $row['thoi_gian_thuc_hien']  = isset($detail['pperiod']) ? $detail['pperiod'] . ' ' . $date[$detail['pperiodUnit']] : '';
        $row['dia_diem_phat_hanh_hsmt'] = isset($detail['issueLocation']) ? $detail['issueLocation'] : '';
    } else if ($_tbmdt['type_project'] == 'es-bido-notify-social-p') {
        $row['ten_du_an'] = $detail['pname'] ?? '';
        $row['ma_du_an'] = $detail['pno'] ?? '';
        $row['tong_chi_phi_thuc_hien_du_an'] = $detail['feeTotal'] ?? 0;
        $row['so_hieu_khlcnt'] = !empty($detail['planNo']) ? $detail['planNo'] . '-' . $detail['planVersion'] : '';
        $row['thoi_diem_dong_thau'] = isset($detail['closeDate']) ? strtotime($detail['closeDate']) : 0;
        $row['thoi_diem_mo_thau'] = isset($detail['openDate']) ? strtotime($detail['openDate']) : 0;
        $row['muc_tieu_cua_du_an'] = '';
        $row['quy_mo_du_an'] = '';
        $row['linh_vuc_thong_bao'] = 9;
        $row['dien_tich_dat'] = '';
        $row['co_quan_tham_quyen'] = '';
        $row['loai_hop_dong'] = 'Hợp đồng Dự án chuyên ngành/xã hội hóa';
        if (!empty($row['pid'])) {
            $_khlcndt = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_bidding_project_proposal WHERE id_msc=' . $db->quote($row['pid']))->fetch();
            if (!empty($_khlcndt)) {
                $row['thoi_gian_thuc_hien'] = $_khlcndt['thoi_gian'];
            } else {
                $row['thoi_gian_thuc_hien'] = '';
            }
        }
        $row['dia_diem_phat_hanh_hsmt'] = isset($detail['issueLocation']) ? $detail['issueLocation'] : '';
    } else {
        $row['ten_du_an'] = $detail['pname'] ?? '';
        $row['ma_du_an'] = $detail['pno'] ?? '';
        $row['tong_chi_phi_thuc_hien_du_an'] = $detail['feeTotal'] ?? 0;
        $row['so_hieu_khlcnt'] = !empty($detail['planNo']) ? $detail['planNo'] . '-' . $detail['planVersion'] : '';
        $row['thoi_diem_dong_thau'] = isset($detail['closeDate']) ? strtotime($detail['closeDate']) : 0;
        $row['thoi_diem_mo_thau'] = isset($detail['openDate']) ? strtotime($detail['openDate']) : 0;
        $row['muc_tieu_cua_du_an'] = $detail['biddingLawDTO']['investTarget'] ?? '';
        $row['quy_mo_du_an'] = $detail['biddingLawDTO']['investScale'] ?? '';
        $row['linh_vuc_thong_bao'] = 10;
        $row['dien_tich_dat'] = '';
        $row['co_quan_tham_quyen'] = '';
        $row['loai_hop_dong'] = $detail['contractType'] ?? '';
        $row['thoi_gian_thuc_hien'] = !empty($detail['planLaw']['contractPeriod']) ? $detail['planLaw']['contractPeriod'] . ' ' . $date[$detail['planLaw']['contractPeriodUnit']] : '';

        $row['dia_diem_phat_hanh_hsmt'] = isset($detail['issueLocation']) ? $detail['issueLocation'] : '';
    }

    $row['phuong_thuc_hop_dong'] = '';
    $row['don_vi_cong_bo_du_an'] = isset($detail['announceUnit']) ? $detail['announceUnit'] : '';
    $row['ten_khlcnt'] = '';
    $row['quyet_dinh_phe_duyet'] = json_encode($quyet_dinh, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    $row['so_tbmt'] = $detail['notifyNo'] . '-' . $detail['notifyVersion'];
    $row['ben_moi_thau'] = isset($detail['procuringEntityName']) ? $detail['procuringEntityName'] : '';
    $org_code_bmt = isset($detail['procuringEntityCode']) ? $detail['procuringEntityCode'] : '';
    $row['dia_diem_thuc_hien_du_an'] = $_tbmdt['location'];
    $row['content'] = $row['so_tbmt'] . ' ' . $row['ten_du_an'] . ' ' . $row['ben_moi_thau'] . ' ' . $row['dia_diem_thuc_hien_du_an'];
    $row['content'] .= ' ' . strip_tags($row['muc_tieu_cua_du_an']);
    $row['content'] = substr($row['content'], 0, 65000);
    $row['content'] = nv_compound_unicode($row['content']);
    $row['so_tien_chu_gia_goi'] = ($row['tong_chi_phi_thuc_hien_du_an'] > 0 ) ? VndText(strip_tags($row['tong_chi_phi_thuc_hien_du_an'])) : '';
    $row['solicitor_id'] = get_solicitor_id($row['ben_moi_thau'], $org_code_bmt);
    $row['get_time'] = $row['notify_chance_time'] = NV_CURRENTTIME;
    $row['get_ho_so'] = 0;
    $row['loai_thong_bao'] = '';
    $row['chu_dau_tu'] = '';
    $row['hinh_thuc_thong_bao'] = $detail['status'] == '01' ? 'Đã đăng tải' : ($detail['status'] == '03' ? 'Đã hủy' : '');
    $row['hinh_thuc_lua_chon_nha_dau_tu'] = isset($detail['bidForm']) ? $detail['bidForm'] : '';
    $row['thoi_gian_bat_dau_phat_hanh_hsmt'] = 0;
    $row['dia_diem'] = '';
    if ($detail['feeType'] == 'F') {
        $row['gia_ban'] = 'Miễn phí';
    } else if (!empty($detail['feeValue'])) {
        $row['gia_ban'] = number_format($detail['feeValue'], 0, '', '.') . ' VND';
    } else if (empty($detail['feeType'])) {
        $row['gia_ban'] = 'Có phí';
    }
    $row['thoi_diem_dang_tai'] = isset($detail['publicDate']) ? strtotime($detail['publicDate']) : 0;
    $row['hinh_thuc_dam_bao'] = isset($bidGuaranteeForm[$detail['bidGuaranteeForm']]) ? $bidGuaranteeForm[$detail['bidGuaranteeForm']] : '';
    $row['phuong_thuc'] = '';
    $row['so_tien_dam_bao'] = isset($detail['bidGuaranteeValue']) ? $detail['bidGuaranteeValue'] : 0;
    $row['so_tien_bang_chu'] = ($row['so_tien_dam_bao'] > 0 ) ? VndText(strip_tags($row['so_tien_dam_bao'])) : '';
    $row['ho_so_moi_thau'] = isset($detail['bidDocumentFile']) ? $detail['bidDocumentFile'] : '';
    $row['hinh_thuc'] = '';
    $row['thong_bao_lien_quan'] = '';
    $row['cac_hop_phan_cua_du_an'] = '';
    $row['von_nha_nuoc'] = '';
    $row['english'] = 0;
    $row['phan_loai'] = '';
    /*$nguon_von = array(
        'stateCapital' => $detail['stateCapital'],
        'stateCapitalUnit' => $detail['stateCapitalUnit'],
        'stateInvestor' => $detail['stateInvestor'],
        'stateInvestorUnit' => $detail['stateInvestorUnit'],
        'stateOther' => $detail['stateOther'],
        'stateOtherUnit' => $detail['stateOtherUnit'],
    );*/
    $row['nguon_von'] = ''; //json_encode($nguon_von, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    $row['thoi_gian_ban_hsyc_tu'] = $row['thoi_gian_nhan_hsdt_tu'] = 0;
    $row['dia_diem_mo_thau'] = isset($detail['bidOpenLocation']) ? $detail['bidOpenLocation'] : '';

    $row['dien_thoai'] = $row['mua_ho_so_moi_thau'] = $row['kieu_lien_danh'] = '';
    $row['dia_diem_nhan_hsdt'] = isset($detail['receiveLocation']) ? $detail['receiveLocation'] : '';
    $row['thoi_diem_ket_thuc_nop'] = 0;
    $row['dia_diem_nop_tien'] = '';
    $row['note'] = '';
    $row['is_online'] = !empty($detail['isInternet']) ? $detail['isInternet'] : 0;
    $row['hinh_thuc_nhan_hs'] = empty($row['is_online']) ? 'Không qua mạng' : 'Qua mạng';

    $id_hinhthucluachon = !empty($detail['isDomestic']) ? $detail['isDomestic'] : 0;
    $detail1 = json_decode($_tbmdt['detail1'], true);
    $province_id = empty($detail1['locations']) ? 0 : (!empty(array_column($detail1['locations'], 'provCode')) ? implode(',', array_unique(array_column($detail1['locations'], 'provCode'))) : 0);
    $required_array = array();
    $required_array[] = 'so_tbmt';
    $required_array[] = 'ben_moi_thau';
    foreach ($required_array as $required_key) {
        if ($row[$required_key] == '' || $row[$required_key] == '-') {
            echo '<br>' . 'Không đủ thông tin bắt buộc' . '<br>';
            echo '<br>' . $required_key . '<br>';
            print_r($row);
            $dbcr->query("UPDATE `nv23_lcndt_tbmt_url` SET url_run='-3', crawls_info='" . $required_key . "' WHERE id=" . $_tbmdt['id']);
            return false;
        }
    }

    // Lấy id mã dự án
    if (!empty($row['ma_du_an'])) {
        $row['id_project'] = $db->query("SELECT id FROM nv4_vi_bidding_project_proposal WHERE code LIKE " . $db->quote($row['ma_du_an'] . '%') . " ORDER BY id DESC LIMIT 1")->fetchColumn();
        empty($row['id_project']) && $row['id_project'] = 0;
    } else {
        $row['id_project'] = 0;
    }

    $row['status'] = $detail['status'];

    // Lưu thông tin vào csdl
    $db->beginTransaction();
    try {
        // Lấy Id của tbmt đã có trong csdl
        $id_tbmt = 0;
        $id_tbmt = $db->query('SELECT id FROM ' . NV_PREFIXLANG . '_bidding_project_row WHERE so_tbmt=' . $db->quote($row['so_tbmt']))->fetchColumn();
        $update_data = $id_tbmt > 0 ? 1 : 0;
        $type_org = 0;
        $type_bid = 0;
        if ($update_data > 0) {
            $row_old = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_bidding_project_row a INNER JOIN ' . NV_PREFIXLANG . '_bidding_project_detail b ON a.id=b.id WHERE a.id=' . $id_tbmt)->fetch();
            $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_bidding_project_row SET ngay_dang_tai = :ngay_dang_tai,linh_vuc_thong_bao = :linh_vuc_thong_bao,goi_thau = :goi_thau,ben_moi_thau = :ben_moi_thau,den_ngay = :den_ngay,content = :content,notify_chance_time=:notify_chance_time,price= :price,money_bid = :money_bid,type_org = :type_org, solicitor_id = :solicitor_id, type_bid=:type_bid, id_hinhthucluachon=:id_hinhthucluachon, province_id = :province_id, translator=0, status=:status, id_project=:id_project WHERE id=' . $id_tbmt);
            $stmt->bindParam(':notify_chance_time', $row['notify_chance_time'], PDO::PARAM_INT);
        } else {
            $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_bidding_project_row (so_tbmt, id_msc, is_new_msc, ngay_dang_tai, linh_vuc_thong_bao, goi_thau, ben_moi_thau, den_ngay, content, price, money_bid, type_org, solicitor_id, get_time, type_bid,classification, get_ho_so, id_hinhthucluachon, province_id, elasticsearch, status, id_project) VALUES
             ( :so_tbmt, :id_msc, 1, :ngay_dang_tai, :linh_vuc_thong_bao, :goi_thau, :ben_moi_thau, :den_ngay, :content, :price, :money_bid, :type_org, :solicitor_id, :get_time, :type_bid ,1, :get_ho_so, :id_hinhthucluachon, :province_id, 0, :status, :id_project)');
             $stmt->bindParam(':so_tbmt', $row['so_tbmt'], PDO::PARAM_STR);
             $stmt->bindParam(':id_msc', $row['id_msc'], PDO::PARAM_STR);
             $stmt->bindParam(':get_time', $row['get_time'], PDO::PARAM_INT);
             $stmt->bindParam(':get_ho_so', $row['get_ho_so'], PDO::PARAM_INT);
        }

        $stmt->bindParam(':ngay_dang_tai', $row['thoi_diem_dang_tai'], PDO::PARAM_INT);
        $stmt->bindParam(':linh_vuc_thong_bao', $row['linh_vuc_thong_bao'], PDO::PARAM_STR);
        $stmt->bindParam(':goi_thau', $row['ten_du_an'], PDO::PARAM_STR);
        $stmt->bindParam(':ben_moi_thau', $row['ben_moi_thau'], PDO::PARAM_STR);
        $stmt->bindParam(':den_ngay', $row['thoi_diem_dong_thau'], PDO::PARAM_INT);
        $stmt->bindParam(':content', $row['content'], PDO::PARAM_STR);
        $stmt->bindParam(':price', $row['tong_chi_phi_thuc_hien_du_an'], PDO::PARAM_STR);
        $stmt->bindParam(':money_bid', $row['so_tien_dam_bao'], PDO::PARAM_STR);
        $stmt->bindParam(':type_org', $type_org, PDO::PARAM_INT);
        $stmt->bindParam(':solicitor_id', $row['solicitor_id'], PDO::PARAM_INT);
        $stmt->bindParam(':type_bid', $type_bid, PDO::PARAM_INT);
        $stmt->bindParam(':id_hinhthucluachon', $id_hinhthucluachon, PDO::PARAM_INT);
        $stmt->bindParam(':province_id', $province_id, PDO::PARAM_STR);
        $stmt->bindParam(':status', $row['status'], PDO::PARAM_STR);
        $stmt->bindParam(':id_project', $row['id_project'], PDO::PARAM_STR);
        $exc = $stmt->execute();
        if ($update_data == 0) {
            $id_tbmt = $db->lastInsertId();
        }
        if ($exc) {
            if ($update_data == 1) {
                $stmt1 = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_bidding_project_detail SET loai_thong_bao= :loai_thong_bao, chu_dau_tu = :chu_dau_tu, don_vi_cong_bo_du_an=:don_vi_cong_bo_du_an, co_quan_tham_quyen=:co_quan_tham_quyen, loai_hop_dong=:loai_hop_dong,is_online=:is_online, hinh_thuc_thong_bao = :hinh_thuc_thong_bao,phan_loai=:phan_loai,ten_du_an = :ten_du_an, ma_du_an=:ma_du_an,nguon_von=:nguon_von,khlcnt_code=:khlcnt_code,khlcnt_title=:khlcnt_title,hinh_thuc_lua_chon = :hinh_thuc_lua_chon,thoi_gian_ban_hsyc_tu=:thoi_gian_ban_hsyc_tu,thoi_gian_nhan_hsdt_tu=:thoi_gian_nhan_hsdt_tu, dia_diem_mo_thau=:dia_diem_mo_thau,gia_goi_thau=:gia_goi_thau,so_tien_chu_gia_goi=:so_tien_chu_gia_goi,thoi_gian_ban_hsmt_tu = :thoi_gian_ban_hsmt_tu, dia_diem = :dia_diem, dien_thoai=:dien_thoai,gia_ban = :gia_ban, thoi_diem_mo_thau = :thoi_diem_mo_thau, hinh_thuc_dam_bao = :hinh_thuc_dam_bao, hinh_thuc_nhan_hs=:hinh_thuc_nhan_hs,phuong_thuc = :phuong_thuc,
                        so_tien_dam_bao = :so_tien_dam_bao, so_tien_bang_chu = :so_tien_bang_chu, thoi_gian_thuc_hien = :thoi_gian_thuc_hien, noi_dung = :noi_dung, quy_mo_du_an=:quy_mo_du_an, ho_so = :ho_so, note=:note,mua_ho_so_moi_thau=:mua_ho_so_moi_thau,phuong_thuc_hop_dong = :phuong_thuc_hop_dong,kieu_lien_danh=:kieu_lien_danh,dia_diem_nhan_hsdt=:dia_diem_nhan_hsdt,dia_diem_phat_hanh_hsmt=:dia_diem_phat_hanh_hsmt,thoi_diem_ket_thuc_nop=:thoi_diem_ket_thuc_nop,hinh_thuc= :hinh_thuc, dia_diem_nop_tien=:dia_diem_nop_tien,thong_bao_lien_quan= :thong_bao_lien_quan, quyet_dinh_phe_duyet=:quyet_dinh_phe_duyet,dia_diem_thuc_hien_goi_thau = :dia_diem_thuc_hien_goi_thau,  dien_tich_dat = :dien_tich_dat, cac_hop_phan = :cac_hop_phan, von_nha_nuoc = :von_nha_nuoc, english = :english, name_staff=:name_staff WHERE id=' . $id_tbmt);
            } else {
                $stmt1 = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_bidding_project_detail  (id, loai_thong_bao, chu_dau_tu, don_vi_cong_bo_du_an, co_quan_tham_quyen, loai_hop_dong,is_online, hinh_thuc_thong_bao, phan_loai, ten_du_an, ma_du_an,nguon_von, khlcnt_code, khlcnt_title, hinh_thuc_lua_chon, thoi_gian_ban_hsyc_tu, thoi_gian_nhan_hsdt_tu, dia_diem_mo_thau, gia_goi_thau, so_tien_chu_gia_goi, thoi_gian_ban_hsmt_tu, dia_diem, dien_thoai, gia_ban, thoi_diem_mo_thau, hinh_thuc_dam_bao, hinh_thuc_nhan_hs, phuong_thuc, so_tien_dam_bao, so_tien_bang_chu, thoi_gian_thuc_hien, noi_dung, quy_mo_du_an, ho_so, note, mua_ho_so_moi_thau, phuong_thuc_hop_dong, kieu_lien_danh, dia_diem_nhan_hsdt, dia_diem_phat_hanh_hsmt, thoi_diem_ket_thuc_nop, hinh_thuc, dia_diem_nop_tien, thong_bao_lien_quan, quyet_dinh_phe_duyet, dia_diem_thuc_hien_goi_thau, dien_tich_dat, cac_hop_phan, von_nha_nuoc, english, name_staff ) VALUES
                        (:id, :loai_thong_bao, :chu_dau_tu, :don_vi_cong_bo_du_an, :co_quan_tham_quyen, :loai_hop_dong,:is_online, :hinh_thuc_thong_bao, :phan_loai, :ten_du_an, :ma_du_an,:nguon_von, :khlcnt_code, :khlcnt_title, :hinh_thuc_lua_chon, :thoi_gian_ban_hsyc_tu, :thoi_gian_nhan_hsdt_tu, :dia_diem_mo_thau, :gia_goi_thau, :so_tien_chu_gia_goi, :thoi_gian_ban_hsmt_tu, :dia_diem, :dien_thoai, :gia_ban, :thoi_diem_mo_thau, :hinh_thuc_dam_bao, :hinh_thuc_nhan_hs, :phuong_thuc, :so_tien_dam_bao, :so_tien_bang_chu, :thoi_gian_thuc_hien, :noi_dung, :quy_mo_du_an, :ho_so, :note, :mua_ho_so_moi_thau, :phuong_thuc_hop_dong, :kieu_lien_danh, :dia_diem_nhan_hsdt, :dia_diem_phat_hanh_hsmt, :thoi_diem_ket_thuc_nop, :hinh_thuc, :dia_diem_nop_tien, :thong_bao_lien_quan, :quyet_dinh_phe_duyet, :dia_diem_thuc_hien_goi_thau, :dien_tich_dat, :cac_hop_phan, :von_nha_nuoc, :english, :name_staff)');

                $stmt1->bindParam(':id', $id_tbmt, PDO::PARAM_INT);
            }

            $stmt1->bindParam(':phan_loai', $row['phan_loai'], PDO::PARAM_STR);
            $stmt1->bindParam(':nguon_von', $row['nguon_von'], PDO::PARAM_STR);
            $stmt1->bindParam(':khlcnt_code', $row['so_hieu_khlcnt'], PDO::PARAM_STR);
            $stmt1->bindParam(':khlcnt_title', $row['ten_khlcnt'], PDO::PARAM_STR);
            $stmt1->bindParam(':thoi_gian_ban_hsyc_tu', $row['thoi_gian_ban_hsyc_tu'], PDO::PARAM_INT);
            $stmt1->bindParam(':thoi_gian_nhan_hsdt_tu', $row['thoi_gian_nhan_hsdt_tu'], PDO::PARAM_INT);
            $stmt1->bindParam(':dia_diem_mo_thau', $row['dia_diem_mo_thau'], PDO::PARAM_STR);
            $stmt1->bindParam(':gia_goi_thau', $row['tong_chi_phi_thuc_hien_du_an'], PDO::PARAM_STR);
            $stmt1->bindParam(':so_tien_chu_gia_goi', $row['so_tien_chu_gia_goi'], PDO::PARAM_STR);
            $stmt1->bindParam(':dien_thoai', $row['dien_thoai'], PDO::PARAM_STR);
            $stmt1->bindParam(':hinh_thuc_nhan_hs', $row['hinh_thuc_nhan_hs'], PDO::PARAM_STR);
            $stmt1->bindParam(':mua_ho_so_moi_thau', $row['mua_ho_so_moi_thau'], PDO::PARAM_STR);
            $stmt1->bindParam(':kieu_lien_danh', $row['kieu_lien_danh'], PDO::PARAM_STR);
            $stmt1->bindParam(':dia_diem_nhan_hsdt', $row['dia_diem_nhan_hsdt'], PDO::PARAM_STR);
            $stmt1->bindParam(':dia_diem_phat_hanh_hsmt', $row['dia_diem_phat_hanh_hsmt'], PDO::PARAM_STR);
            $stmt1->bindParam(':thoi_diem_ket_thuc_nop', $row['thoi_diem_ket_thuc_nop'], PDO::PARAM_INT);
            $stmt1->bindParam(':dia_diem_nop_tien', $row['dia_diem_nop_tien'], PDO::PARAM_STR);
            $stmt1->bindParam(':quyet_dinh_phe_duyet', $row['quyet_dinh_phe_duyet'], PDO::PARAM_STR);
            $stmt1->bindParam(':note', $row['note'], PDO::PARAM_STR);
            $stmt1->bindParam(':loai_thong_bao', $row['loai_thong_bao'], PDO::PARAM_STR);
            $stmt1->bindParam(':chu_dau_tu', $row['chu_dau_tu'], PDO::PARAM_STR);
            $stmt1->bindParam(':don_vi_cong_bo_du_an', $row['don_vi_cong_bo_du_an'], PDO::PARAM_STR);
            $stmt1->bindParam(':co_quan_tham_quyen', $row['co_quan_tham_quyen'], PDO::PARAM_STR);
            $stmt1->bindParam(':loai_hop_dong', $row['loai_hop_dong'], PDO::PARAM_STR);
            $stmt1->bindParam(':is_online', $row['is_online'], PDO::PARAM_INT);
            $stmt1->bindParam(':hinh_thuc_thong_bao', $row['hinh_thuc_thong_bao'], PDO::PARAM_STR);
            $stmt1->bindParam(':ten_du_an', $row['ten_du_an'], PDO::PARAM_STR);
            $stmt1->bindParam(':ma_du_an', $row['ma_du_an'], PDO::PARAM_STR);
            $stmt1->bindParam(':hinh_thuc_lua_chon', $row['hinh_thuc_lua_chon_nha_dau_tu'], PDO::PARAM_STR);
            $stmt1->bindParam(':thoi_gian_ban_hsmt_tu', $row['thoi_gian_bat_dau_phat_hanh_hsmt'], PDO::PARAM_INT);
            $stmt1->bindParam(':dia_diem', $row['dia_diem'], PDO::PARAM_STR);
            $stmt1->bindParam(':gia_ban', $row['gia_ban'], PDO::PARAM_STR);
            $stmt1->bindParam(':thoi_diem_mo_thau', $row['thoi_diem_mo_thau'], PDO::PARAM_INT);
            $stmt1->bindParam(':hinh_thuc_dam_bao', $row['hinh_thuc_dam_bao'], PDO::PARAM_STR);
            $stmt1->bindParam(':phuong_thuc', $row['phuong_thuc'], PDO::PARAM_STR);
            $stmt1->bindParam(':so_tien_dam_bao', $row['so_tien_dam_bao'], PDO::PARAM_STR);
            $stmt1->bindParam(':so_tien_bang_chu', $row['so_tien_bang_chu'], PDO::PARAM_STR);
            $stmt1->bindParam(':thoi_gian_thuc_hien', $row['thoi_gian_thuc_hien'], PDO::PARAM_STR);
            $stmt1->bindParam(':noi_dung', $row['muc_tieu_cua_du_an'], PDO::PARAM_STR);
            $stmt1->bindParam(':quy_mo_du_an', $row['quy_mo_du_an'], PDO::PARAM_STR);
            $stmt1->bindParam(':ho_so', $row['ho_so_moi_thau'], PDO::PARAM_STR);
            $stmt1->bindParam(':phuong_thuc_hop_dong', $row['phuong_thuc_hop_dong'], PDO::PARAM_STR);
            $stmt1->bindParam(':hinh_thuc', $row['hinh_thuc'], PDO::PARAM_STR);
            $stmt1->bindParam(':thong_bao_lien_quan', $row['thong_bao_lien_quan'], PDO::PARAM_STR);
            $stmt1->bindParam(':dia_diem_thuc_hien_goi_thau', $row['dia_diem_thuc_hien_du_an'], PDO::PARAM_STR);
            $stmt1->bindParam(':dien_tich_dat', $row['dien_tich_dat'], PDO::PARAM_STR);
            $stmt1->bindParam(':cac_hop_phan', $row['cac_hop_phan_cua_du_an'], PDO::PARAM_STR);
            $stmt1->bindParam(':von_nha_nuoc', $row['von_dau_tu_cua_nha_nuoc_tham_gia_thuc_hien_du_an'], PDO::PARAM_STR);
            $stmt1->bindParam(':english', $row['english'], PDO::PARAM_INT);
            $name_staff = $array_name_staff[array_rand($array_name_staff)];
            $stmt1->bindParam(':name_staff', $name_staff, PDO::PARAM_STR);
            $exc1 = $stmt1->execute();
            if ($id_tbmt > 0 and $update_data > 0) {
                $row_new = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_bidding_project_row a INNER JOIN ' . NV_PREFIXLANG . '_bidding_project_detail b ON a.id=b.id WHERE a.id=' . $id_tbmt)->fetch();
                $status_insert = insert_log_crawls($id_tbmt, 'TBMTDT', $row_old, $row_new);
                if ($status_insert > 0) {
                    echo("LOG: TBMTDT - ID: " . $id_tbmt . "- OK<br/>\n");
                }
            }
        }

        $dbcr->query('UPDATE `nv23_lcndt_tbmt_url` SET dauthau_info = 1 WHERE `id`=' . intval($_tbmdt['id']));
        echo "Cập nhật xong = " . $_tbmdt['id'];
        echo "\n";
        $db->commit();
    } catch (PDOException $e) {
        $db->rollBack();
        print_r($e);
        $dbcr->query("UPDATE `nv23_lcndt_tbmt_url` SET url_run='-4', crawls_info=" . $db->quote(print_r($e, true)) . " WHERE id=" . $_tbmdt['id']);
        return false;
    }
}

function getkhlcndt($_khlcndt, $reload = 1)
{
    global $dbcr, $num_run;
    ++$num_run;
    $_khlcndt['type_project'] = 'es-bidp-plan-project-invest-sdd-p';
    $date = [
        'D' => 'Ngày',
        'M' => 'Tháng',
        'Y' => 'Năm'
    ];
    $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-investor-selection-v2/services/invest-sdd/project/detail';

    $body = '{"id":"' . $_khlcndt['id_msc'] . '"}';

    $referer = 'https://muasamcong.mpi.gov.vn/en/web/guest/investor-selection?p_p_id=egpportalinvestorselectionv2_WAR_egpportalinvestorselectionv2&p_p_lifecycle=0&p_p_state=normal&p_p_mode=view&_egpportalinvestorselectionv2_WAR_egpportalinvestorselectionv2_render=detail&id=' . $_khlcndt['id_msc'] . '&type=' . $_khlcndt['type_project'] . '&no=' . $_khlcndt['planno'];

    $arr_agent = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:104.0) Gecko/20100101 Firefox/104.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/105.0.1343.42',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 9.0; SAMSUNG SM-F900U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.77 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ];

    srand((float) microtime() * 10000000);
    $rand = array_rand($arr_agent);
    $agent = $arr_agent[$rand];
    $_proxy = $dbcr->query("SELECT * FROM `nv4_proxy` WHERE `status`=2 ORDER BY `lasttime` ASC LIMIT 1")->fetch();
    $ch = curl_init();

    if (isset($_proxy['proxy'])) {
        $dbcr->query("UPDATE `nv4_proxy` SET `lasttime` = " . time() . ", number_run=number_run+1 WHERE `id` = " . $_proxy['id']);
        echo $_proxy['proxy'] . "\n";
        curl_setopt($ch, CURLOPT_PROXY, $_proxy['proxy']);
        if (isset($_proxy['proxyuserpwd']) and !empty($_proxy['proxyuserpwd'])) {
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, $_proxy['proxyuserpwd']);
        }
        curl_setopt($ch, CURLOPT_PROXYTYPE, $_proxy['proxytype']);
        curl_setopt($ch, CURLOPT_PROXYPORT, $_proxy['proxyport']);
    }

    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
    curl_setopt($ch, CURLOPT_REFERER, $referer);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json'
    ));

    $json = curl_exec($ch);
    $info = curl_getinfo($ch);
    curl_close($ch);
    $data = json_decode($json, true);
    if (isset($data['planNo'])) {
        $thoi_gian_thuc_hien = isset($data['contractPeriod']) ? $data['contractPeriod'] . ' ' . $date[$data['contractPeriodUnit']] : '';
        return $thoi_gian_thuc_hien;
    } elseif ($reload and $num_run < 5) {
        return geturlpage($_khlcndt, 1);
    } elseif ($reload) {
        return geturlpage($_khlcndt, 0);
    }
    return [];
}

function geturlpage($_tbmdt, $reload = 1)
{
    global $dbcr, $num_run;
    ++$num_run;
    if ($_tbmdt['type_project'] == 'es-bido-notify-invest-ppp-p') {
        $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-investor-selection-v2/services/bido-notify-invest-ppp-m-getDetailById';
    } else if ($_tbmdt['type_project'] == 'es-bido-notify-invest-sdd-p') {
        $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-investor-selection-v2/services/get-detail-notify-sdd';
    } else if ($_tbmdt['type_project'] == 'es-bido-notify-social-p') {
        $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-investor-selection-v2/services/ebidsdd/notify-social/get-detail';
    } else {
        $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-investor-selection-v2/services/notify-out-law/get-detail';
    }

    $body = '{"id":"' . $_tbmdt['id_msc'] . '"}';

    $referer = 'https://muasamcong.mpi.gov.vn/en/web/guest/investor-selection?p_p_id=egpportalinvestorselectionv2_WAR_egpportalinvestorselectionv2&p_p_lifecycle=0&p_p_state=normal&p_p_mode=view&_egpportalinvestorselectionv2_WAR_egpportalinvestorselectionv2_render=detail&id=' . $_tbmdt['id_msc'] . '&type=' . $_tbmdt['type_project'] . '&no=' . $_tbmdt['notifyno'];

    $arr_agent = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:104.0) Gecko/20100101 Firefox/104.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/105.0.1343.42',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 9.0; SAMSUNG SM-F900U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.77 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ];

    srand((float) microtime() * 10000000);
    $rand = array_rand($arr_agent);
    $agent = $arr_agent[$rand];
    $_proxy = $dbcr->query("SELECT * FROM `nv4_proxy` WHERE `status`=2 ORDER BY `lasttime` ASC LIMIT 1")->fetch();
    $ch = curl_init();

    if (isset($_proxy['proxy'])) {
        $dbcr->query("UPDATE `nv4_proxy` SET `lasttime` = " . time() . ", number_run=number_run+1 WHERE `id` = " . $_proxy['id']);
        echo $_proxy['proxy'] . "\n";
        curl_setopt($ch, CURLOPT_PROXY, $_proxy['proxy']);
        if (isset($_proxy['proxyuserpwd']) and !empty($_proxy['proxyuserpwd'])) {
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, $_proxy['proxyuserpwd']);
        }
        curl_setopt($ch, CURLOPT_PROXYTYPE, $_proxy['proxytype']);
        curl_setopt($ch, CURLOPT_PROXYPORT, $_proxy['proxyport']);
    }

    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
    curl_setopt($ch, CURLOPT_REFERER, $referer);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json'
    ));

    $json = curl_exec($ch);
    $info = curl_getinfo($ch);
    curl_close($ch);
    $data = json_decode($json, true);
    if (isset($data['notifyNo'])) {
        return $data;
    } elseif ($reload and $num_run < 5) {
        return geturlpage($_tbmdt, 1);
    } elseif ($reload) {
        return geturlpage($_tbmdt, 0);
    }
    return [];
}
