<?php
define('NV_SYSTEM', true);

// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

/*
 * INSERT INTO `nv4_duthau_cat` (`id`, `type`, `catid`, `lasttime`, `active`, `numberday`, `page_old`) VALUES
 * (NULL, 'kqlcnt_offline_check', 1, 0, 1, 0, 0),
 * (NULL, 'kqlcnt_offline_check', 3, 0, 1, 0, 0),
 * (NULL, 'kqlcnt_offline_check', 5, 0, 1, 0, 0),
 * (NULL, 'kqlcnt_offline_check', 10, 0, 1, 0, 0),
 * (NULL, 'kqlcnt_offline_check', 15, 0, 1, 0, 0),
 * (NULL, 'kqlcnt_offline_check', 20, 0, 1, 0, 0);
 */
$per_page = 200;
$_start_time_all = microtime(true);

$_logs_file = NV_ROOTDIR . '/check_kq_url/check_kqoffline_url_' . date('Y-m-d') . '.txt';
$query_url = $dbcr->query("SELECT * FROM nv4_duthau_cat WHERE type='kqlcnt_offline_check' AND active =1 ORDER BY lasttime ASC LIMIT 1");
while ($_row = $query_url->fetch()) {
    $dbcr->query('UPDATE nv4_duthau_cat SET lasttime=' . NV_CURRENTTIME . ' WHERE id=' . $_row['id']);
    $bidType = $_row['catid'];

    for ($irun = 1; $irun < 6; $irun ++) { // đọc lại mỗi lần 60 ngày cho 360 ngày
        $idate = $irun * 60;
        $fromDate_int = strtotime('-' . $idate . ' Day');
        $idate = ($irun - 1) * 60;
        $toDate_int = strtotime('-' . $idate . ' Day'); // MSC cho lấy tối đa 60 ngày
        $fromDate = date('d/m/Y', $fromDate_int);
        $toDate = date('d/m/Y', $toDate_int);

        file_put_contents($_logs_file, date('d-m-Y', $fromDate_int) . '_' . date('d-m-Y', $toDate_int) . '_new_' . $bidType . "\n", FILE_APPEND);
        $_error_file = NV_ROOTDIR . '/check_kq_url/check_kqoffline_url_' . date('d-m-Y', $fromDate_int) . '_' . date('d-m-Y', $toDate_int) . '_error_' . $bidType . '.txt';
        $pagetotal = 1;
        for ($page_no = 1; $page_no <= $pagetotal; $page_no ++) {
            echo "page_no = " . $page_no . "\t" . $fromDate . '-->' . $toDate . " \t bidType = " . $bidType . "\n";
            $_start_time = microtime(true);
            if ($bidType == 20) {
                $url = "http://muasamcong.mpi.gov.vn:8082/GG/EP_SSJ_VTT_GGQ803_VK.jsp?gubun=" . $bidType . "&pqCls=Y&bidMethod=00&viewType=0&instituName=&instituCode=&isInstitu=0&bidNM=&fromDate=01%2F01%2F2010&toDate=31%2F12%2F2050&fromOpenDate=" . $fromDate . "&toOpenDate=" . $toDate . "&refNumber=&Bid_succ_offline_yn=N&pageSize=" . $per_page . "&firstCall=N&pageNo=&page_no=" . $page_no;
            } else {
                $url = "http://muasamcong.mpi.gov.vn:8082/GG/EP_SSJ_GGQ803.jsp?gubun=" . $bidType . "&pqCls=Y&bidMethod=00&viewType=0&instituName&instituCode&isInstitu=0&fromDate=01/01/2010&toDate=31/12/2050&fromOpenDate=" . $fromDate . "&toOpenDate=" . $toDate . "&refNumber&Bid_succ_offline_yn=N&pageSize=" . $per_page . "&firstCall=N&page_no=" . $page_no;
            }

            $return = geturlpage($url, 1);
            if (!isset($return['pagetotal']) or empty($return['pagetotal'])) {
                file_put_contents($_error_file, $page_no . "\n", FILE_APPEND);
            } else {
                if ($return['number_new_id'] > 0) {
                    file_put_contents($_logs_file, $page_no . "/" . $pagetotal . "\t" . $return['number_new_id'] . "\n", FILE_APPEND);
                }
                if ($return['pagetotal'] > $pagetotal) {
                    $pagetotal = $return['pagetotal'];
                }
            }
            $return['run_time'] = number_format((microtime(true) - $_start_time), 3, '.', '');
            print_r($return);
        }
        if (file_exists($_error_file)) {
            // Đọc lại các page bị lỗi
            $string = file_get_contents($_error_file);
            $error_array = explode("\n", $string);
            unlink($_error_file);
            foreach ($error_array as $page_no) {
                $page_no = intval($page_no);
                if ($page_no > 0) {
                    echo "page_no = " . $page_no . "\t" . $fromDate . '-->' . $toDate . " \t bidType = " . $bidType . "\n";
                    $_start_time = microtime(true);
                    if ($bidType == 20) {
                        $url = "http://muasamcong.mpi.gov.vn:8082/GG/EP_SSJ_VTT_GGQ803_VK.jsp?gubun=" . $bidType . "&pqCls=Y&bidMethod=00&viewType=0&instituName=&instituCode=&isInstitu=0&bidNM=&fromDate=01%2F01%2F2010&toDate=31%2F12%2F2050&fromOpenDate=" . $fromDate . "&toOpenDate=" . $toDate . "&refNumber=&Bid_succ_offline_yn=N&pageSize=" . $per_page . "&firstCall=N&pageNo=&page_no=" . $page_no;
                    } else {
                        $url = "http://muasamcong.mpi.gov.vn:8082/GG/EP_SSJ_GGQ803.jsp?gubun=" . $bidType . "&pqCls=Y&bidMethod=00&viewType=0&instituName&instituCode&isInstitu=0&fromDate=01/01/2010&toDate=31/12/2050&fromOpenDate=" . $fromDate . "&toOpenDate=" . $toDate . "&refNumber&Bid_succ_offline_yn=N&pageSize=" . $per_page . "&firstCall=N&page_no=" . $page_no;
                    }

                    $return = geturlpage($url, 1);
                    if (!isset($return['pagetotal']) or empty($return['pagetotal'])) {
                        file_put_contents($_error_file, $page_no . "\n", FILE_APPEND);
                    } else {
                        if ($return['number_new_id'] > 0) {
                            file_put_contents($_logs_file, $page_no . "/" . $pagetotal . "\t" . $return['number_new_id'] . "\n", FILE_APPEND);
                        }
                    }
                    $return['run_time'] = number_format((microtime(true) - $_start_time), 3, '.', '');
                    print_r($return);
                }
            }
        }
    }
}
$run_time_all = number_format((microtime(true) - $_start_time_all), 3, '.', '');
die('END run_time_all = ' . $run_time_all . "\n");

function geturlpage($url, $reload)
{
    global $per_page, $bidType, $dbcr, $db;
    // echo $url . "\n";
    $number_new_id = $number_new = $number_new_check = 0;

    $html = geturlhtml($url, 2);

    $dom = new DOMDocument();
    libxml_use_internal_errors(true);
    if (empty($html) or !$dom->loadHTML($html)) {
        if ($reload) {
            return geturlpage($url, 0);
        }
        return 0;
    }
    $return = [];
    $xpath = new DOMXPath($dom);
    $nodeList = $xpath->query('//table/tr/td[@class="page"]');
    if ($nodeList->length > 0) {
        $_html = trim(html_entity_decode(DOMinnerHTML($nodeList[0]), ENT_QUOTES, 'UTF-8'));
        if (preg_match_all("/([0-9])+/", $_html, $m)) {
            $return['pagetotal'] = ceil(($m[0][0] / $per_page));
        }
    }

    $nodeList = $xpath->query('//form[@name="ebid"]/table/tr');
    if ($nodeList->length > 0) {
        $arr_data = $arr_data_code = $arr_data_so_tbmt = array();
        foreach ($nodeList as $node) {
            $_html = DOMinnerHTML($node);
            $dom_i = new DOMDocument();
            if ($dom_i->loadHTML($_html)) {
                $xpath_i = new DOMXPath($dom_i);
                $nodeLis_i = $xpath_i->query('//td');
                if ($nodeLis_i->length == 8 or $nodeLis_i->length == 7) {
                    $openbider = trim(DOMinnerHTML($nodeLis_i[5]));
                    $openbider = html_entity_decode($openbider, ENT_QUOTES, 'UTF-8');
                    $type_url = 0;
                    $data = array();
                    if ($bidType == 20) {
                        if (preg_match("/go\_showBidSucc\(\'([0-9]+)\'\,\'([0-9]+)\'\,\'([0-9]+)\'\, \'([a-z]+)\'\,\'([a-zA-Z]+)\'\)\;\"\>\n*\s*\t*(.*?)\n*\s*\t*\<\/a\>/s", $openbider, $m) and $bidType == 20) {
                            $data['code'] = $m[1];
                            $type = $m[5];
                            if ($type == "K" || $type == "F" || $type == "H") {
                                $type = "0";
                            } else {
                                $type = "1";
                            }

                            if ($m[4] == 'true') {
                                $type_url = 1;
                                $data['link_url'] = 'http://muasamcong.mpi.gov.vn:8082/GG/SHOW_TBMT_QUICK_VK.jsp?bidNo=' . $m[1] . '&bidTurnNo=' . $m[2] . '&bidType=' . $m[3] . '&type=' . $type;
                            } else {
                                $type_url = 2;
                                $data['link_url'] = 'http://muasamcong.mpi.gov.vn:8082/GG/EP_SSJ_GGQ902_OCVN_VK.jsp?bidNo=' . $m[1] . '&bidTurnNo=' . $m[2] . '&bidType=' . $m[3];
                            }
                            $data['nha_trung_thau'] = trim($m[6]);
                        } else if (preg_match("/goBidSucc\_VTT\(\'([0-9]+)\'\,\'([0-9]+)\'\,\'([0-9]+)\'\)\;\"\>\n*\s*\t*(.*?)\n*\s*\t*\<\/a\>/s", $openbider, $m)) {
                            $data['code'] = $m[1];
                            $data['link_url'] = 'http://muasamcong.mpi.gov.vn:8082/GG/EP_SSJ_VTT_GGQ902_VK.jsp?bidNo=' . $m[1] . '&bidTurnNo=' . $m[2] . '&bidType=' . $m[3];
                            $data['nha_trung_thau'] = trim($m[4]);
                            // <a href="#" onclick="goBidSucc_VTT('20181173862','00','20');">Công ty TNHH Vạn Phú</a>
                        } elseif (preg_match("/goBidSuccFail\(\'([0-9]+)\'\,\'([0-9]+)\'\,\'([0-9]+)\'\, ([a-z]+)\,\'([a-zA-Z]+)\'\)\"\>\n*\s*\t*(.*)\n*\s*\t*\<\/a\>/s", $openbider, $m) or preg_match("/goBidSuccFail\(\'([0-9]+)\'\,\'([0-9]+)\'\,\'([0-9]+)\'\, ([a-z]+)\,\'([a-zA-Z]+)\'\)\;\"\>\n*\s*\t*(.*)\n*\s*\t*\<\/a\>/s", $openbider, $m) or preg_match("/goBidSuccFail\(\'([0-9]+)\'\,\'([0-9]+)\'\,\'([0-9]+)\'\, \'([a-z]+)\'\,\'\'\)\;\"\>\n*\s*\t*(.*)\n*\s*\t*\<\/a\>/s", $openbider, $m)) {
                            $data['code'] = $m[1];
                            if ($m[5] == null || $m[5] == '' || $m[5] == 'null') {
                                $type_url = 8;
                                $data['link_url'] = 'http://muasamcong.mpi.gov.vn:8082/GG/SHOW_TBMT_TT_VK.jsp?bidNo=' . $m[1] . '&bidTurnNo=' . $m[2] . '&bidType=' . $m[3];
                            } else if ($m[4] == 'true') {
                                $type_url = 3;
                                $data['link_url'] = 'http://muasamcong.mpi.gov.vn:8082/GG/EP_SSJ_GGQ911_VK.jsp?bidNo=' . $m[1] . '&bidTurnNo=' . $m[2] . '&bidType=' . $m[3];
                            } else {
                                $type_url = 4;
                                $data['link_url'] = 'http://muasamcong.mpi.gov.vn:8082/GG/EP_SSJ_GGQ902_VK.jsp?bidNo=' . $m[1] . '&bidTurnNo=' . $m[2] . '&bidType=' . $m[3];
                            }
                            $data['nha_trung_thau'] = (isset($m[6])) ? trim($m[6]) : trim($m[5]);
                        } elseif (preg_match("/goBidSuccFail\_VTT\(\'([0-9]+)\'\,\'([0-9]+)\'\,\'([0-9]+)\'\, ([a-z]+)\)\"\>\n*\s*\t*(.*)\n*\s*\t*\<\/a\>/s", $openbider, $m) or preg_match("/goBidSuccFail_VTT\(\'([0-9]+)\'\,\'([0-9]+)\'\,\'([0-9]+)\'\, ([a-z]+)\)\;\"\>\n*\s*\t*(.*)\n*\s*\t*\<\/a\>/s", $openbider, $m)) {
                            $data['code'] = $m[1];
                            if ($m[4] == 'Y') {
                                $type_url = 3;
                                $data['link_url'] = 'http://muasamcong.mpi.gov.vn:8082/GG/EP_SSJ_VTT_GGQ902_VK.jsp?bidNo=' . $m[1] . '&bidTurnNo=' . $m[2] . '&bidType=' . $m[3] . '&no_bid_yn=Y';
                            } else {
                                $type_url = 4;
                                $data['link_url'] = 'http://muasamcong.mpi.gov.vn:8082/GG/TBVK_KhongCoNTTrungThau_VK.jsp?bidNo=' . $m[1] . '&bidTurnNo=' . $m[2] . '&bidType=' . $m[3];
                            }
                            $data['nha_trung_thau'] = trim($m[5]);
                        } elseif (preg_match("/viewDthc\(\'([0-9]+)\'\,\'([0-9]+)\',\'([0-9]+)\'\)\;\"\>\n*\s*\t*(.*)\n*\s*\t*\<\/a\>/s", $openbider, $m)) {
                            $data['code'] = $m[1];
                            $type_url = 6;
                            $data['link_url'] = 'http://muasamcong.mpi.gov.vn:8081/webentry_adb/view/dthc?bidNo=' . $m[1] . '&bidTurnNo=' . $m[2] . '&bidType=' . $m[3];
                            $data['nha_trung_thau'] = trim($m[4]);
                        } elseif (preg_match("/goKQTBMST\(\'([0-9]+)\'\,\'([0-9]+)\',\'([0-9]+)\'\)\;\"\>\n*\s*\t*(.*)\n*\s*\t*\<\/a\>/s", $openbider, $m)) {
                            $data['code'] = $m[1];
                            $type_url = 7;
                            $data['link_url'] = 'http://muasamcong.mpi.gov.vn:8082/GG/EP_SSJ_KQST_OCVN_VK.jsp?bidNo=' . $m[1] . '&bidTurnNo=' . $m[2] . '&bidType=' . $m[3];
                            $data['nha_trung_thau'] = trim($m[4]);
                        } elseif ($openbider != 'Nhà thầu trúng thầu') {
                            continue;
                        }
                    } else {
                        if (preg_match("/go\_showBidSucc\(\'([0-9]+)\'\,\'([0-9]+)\'\,\'([0-9]+)\'\, \'([a-z]+)\'\)\;\"\>\n*\s*\t*(.*?)\n*\s*\t*\<\/a\>/s", $openbider, $m)) {
                            $data['code'] = $m[1];
                            if ($m[4] == 'true') {
                                $type_url = 1;
                                $data['link_url'] = 'http://muasamcong.mpi.gov.vn:8082/GG/SHOW_TBMT_QUICK.jsp?bidNo=' . $m[1] . '&bidTurnNo=' . $m[2] . '&bidType=' . $m[3];
                            } else {
                                $type_url = 2;
                                $data['link_url'] = 'http://muasamcong.mpi.gov.vn:8082/GG/EP_SSJ_GGQ902_OCVN.jsp?bidNo=' . $m[1] . '&bidTurnNo=' . $m[2] . '&bidType=' . $m[3];
                            }
                            $data['nha_trung_thau'] = trim($m[5]);
                        } elseif (preg_match("/goBidSuccFail\(\'([0-9]+)\'\,\'([0-9]+)\'\,\'([0-9]+)\'\, ([a-z]+)\)\"\>\n*\s*\t*(.*)\n*\s*\t*\<\/a\>/s", $openbider, $m) or preg_match("/goBidSuccFail\(\'([0-9]+)\'\,\'([0-9]+)\'\,\'([0-9]+)\'\, ([a-z]+)\)\;\"\>\n*\s*\t*(.*)\n*\s*\t*\<\/a\>/s", $openbider, $m)) {
                            $data['code'] = $m[1];
                            if ($m[4] == 'true') {
                                $type_url = 3;
                                $data['link_url'] = 'http://muasamcong.mpi.gov.vn:8082/GG/EP_SSJ_GGQ913.jsp?bidNo=' . $m[1] . '&bidTurnNo=' . $m[2] . '&bidType=' . $m[3];
                            } else {
                                $type_url = 4;
                                $data['link_url'] = 'http://muasamcong.mpi.gov.vn:8082/GG/EP_SSJ_GGQ902.jsp?bidNo=' . $m[1] . '&bidTurnNo=' . $m[2] . '&bidType=' . $m[3];
                            }
                            $data['nha_trung_thau'] = trim($m[5]);
                        } elseif (preg_match("/showCancelReason\(\'([0-9]+)\'\,\'([0-9]+)\'\)\;\"\>\n*\s*\t*(.*)\n*\s*\t*\<\/a\>/s", $openbider, $m)) {
                            $data['code'] = $m[1];
                            $type_url = 5;
                            $data['link_url'] = 'http://muasamcong.mpi.gov.vn:8081/webentry/bid_cancel/get_cancel_reason?bidNo=' . $m[1] . '&bidTurnNo=' . $m[2] . '&allUser=Y';
                            $data['nha_trung_thau'] = trim($m[3]);
                        } elseif (preg_match("/viewDthc\(\'([0-9]+)\'\,\'([0-9]+)\',\'([0-9]+)\'\)\;\"\>\n*\s*\t*(.*)\n*\s*\t*\<\/a\>/s", $openbider, $m)) {
                            // <a href="#" onclick="viewDthc('20180515048','00','3');">Công ty TNHH Chiếu sáng Điện tử ứng dụng</a>
                            $data['code'] = $m[1];
                            $type_url = 6;
                            $data['link_url'] = 'http://muasamcong.mpi.gov.vn:8082/webentry/view/dthc?bidNo=' . $m[1] . '&bidTurnNo=' . $m[2] . '&bidType=' . $m[3];
                            $data['nha_trung_thau'] = trim($m[4]);
                        } elseif (preg_match("/goKQTBMST\(\'([0-9]+)\'\,\'([0-9]+)\',\'([0-9]+)\'\)\;\"\>\n*\s*\t*(.*)\n*\s*\t*\<\/a\>/s", $openbider, $m)) {
                            $data['code'] = $m[1];
                            $type_url = 7;
                            $data['link_url'] = 'http://muasamcong.mpi.gov.vn:8082/GG/EP_SSJ_KQST_OCVN2.jsp?bidNo=' . $m[1] . '&bidTurnNo=' . $m[2] . '&bidType=' . $m[3];
                            $data['nha_trung_thau'] = trim($m[4]);
                            // <td class="tdc"><a href="#" onclick="goKQTBMST('20180445463','00','5');">CÔNG TY TNHH TƯ VẤN XÂY DỰNG ĐỨC TÍN</a></td>
                        } elseif ($openbider != 'Nhà thầu trúng thầu') {
                            continue;
                        }
                    }

                    if (isset($data['code'])) {
                        $data['code'] = trim(str_replace(' ', '', $data['code']));
                        $data['nha_trung_thau'] = html_entity_decode($data['nha_trung_thau'], ENT_QUOTES, 'UTF-8');
                        $data['ten_du_an'] = html_entity_decode(DOMinnerHTML($nodeLis_i[3]), ENT_QUOTES, 'UTF-8');
                        $data['ben_moi_thau'] = html_entity_decode(DOMinnerHTML($nodeLis_i[2]), ENT_QUOTES, 'UTF-8');

                        $data['gia_trung_thau'] = trim(DOMinnerHTML($nodeLis_i[6]));
                        $data['gia_trung_thau'] = str_replace('VND', '', $data['gia_trung_thau']);
                        $data['gia_trung_thau'] = trim(str_replace('.', '', $data['gia_trung_thau']));
                        if ($bidType == 20) {
                            $data['gia_goi_thau'] = '';
                        } else {
                            $data['gia_goi_thau'] = trim(DOMinnerHTML($nodeLis_i[7]));
                            $data['gia_goi_thau'] = str_replace('VND', '', $data['gia_goi_thau']);
                            $data['gia_goi_thau'] = trim(str_replace('.', '', $data['gia_goi_thau']));
                        }
                        $data['type_url'] = $type_url;
                        $so_tbmt = html_entity_decode(DOMinnerHTML($nodeLis_i[1]), ENT_QUOTES, 'UTF-8');
                        $so_tbmt = trim(str_replace(' ', '', $so_tbmt));
                        $so_tbmt = strip_tags($so_tbmt);

                        $arr_data[$so_tbmt] = $data;

                        ++ $number_new;
                    }
                }
            }
        }

        if (!empty($arr_data)) {

            $_result = $db->query("SELECT code FROM nv4_vi_bidding_result WHERE code IN ('" . implode("','", array_keys($arr_data)) . "')");
            $arr_result_exit = array();
            while ($tmp = $_result->fetch()) {
                $arr_result_exit[] = $tmp['code'];
            }
            $_sql_insert = [];
            foreach ($arr_data as $so_tbmt => $data) {
                if (!in_array($so_tbmt, $arr_result_exit)) {
                    $_sql_insert[] = '(' . $dbcr->quote($data['code']) . ',' . $dbcr->quote($data['ten_du_an']) . ',' . $dbcr->quote($data['ben_moi_thau']) . ', ' . $dbcr->quote($data['nha_trung_thau']) . ', ' . $dbcr->quote($data['gia_trung_thau']) . ',' . $dbcr->quote($data['gia_goi_thau']) . ', ' . NV_CURRENTTIME . ',' . $dbcr->quote($data['link_url']) . ', ' . $data['type_url'] . ')';
                }
            }
            if (!empty($_sql_insert)) {
                $query = 'INSERT IGNORE INTO nv4_vi_bidding_result_offline_url (code, title, owner, nha_trung_thau, gia_trung_thau, gia_goi_thau, addtime, url, type_url) VALUES ' . implode(', ', $_sql_insert);
                $number_new_id = $dbcr->exec($query);
            }
        }
    } elseif ($reload) {
        return geturlpage($url, 0);
    }
    $return['number_new'] = $number_new;
    $return['number_new_id'] = $number_new_id;
    return $return;
}

