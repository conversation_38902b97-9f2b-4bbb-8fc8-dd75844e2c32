MAILTO=""
CRON_TZ=Asia/Ho_Chi_Minh

# Bóc MSC mới
20 6-20 * * * bash /home/<USER>/private/crawls_2023/every_hour.sh

# Bóc MSC mới theo token
* * * * * php /home/<USER>/private/crawls_2023_manual/expried_token.php
* * * * * bash /home/<USER>/private/crawls_2023_manual/every_minute1.sh
* * * * * bash /home/<USER>/private/crawls_2023_manual/every_minute2.sh
* * * * * bash /home/<USER>/private/crawls_2023_manual/every_minute3.sh

* * * * * bash /home/<USER>/private/every_minute.sh
10 * * * * bash /home/<USER>/private/every_hour.sh
30 2,12,18 * * * bash /home/<USER>/private/every_day.sh

10 7 * * * php /home/<USER>/private/crawls_2023/nhathau_send_mail_alert_msc_renewal.php
20 7 * * * php /home/<USER>/private/crawls_new_2022/nhathau_send_mail_active_profile_dtnet.php

# Bóc MSC cũ
#* * * * * bash /home/<USER>/private/crawls_old/crawls.sh
#* * * * * bash /home/<USER>/private/crawls.sh
#* * * * * bash /home/<USER>/private/crawls_tbmt_khlcnt.sh
# Bóc lại url 1 năm kết quả lựa chọn nhà thầu, chay vong tron => bỏ k chạy url
#28 * * * * bash /home/<USER>/private/check_kq_url/check_kq_url.sh
#57 23 * * * php /home/<USER>/private/daily_statistic_msc.php => tạm bỏ, url thống kê cũ

# Bóc dữ liệu công khai kết quả thầu của Bộ Y Tế: congkhaiketquathau.moh.gov.vn chạy 21 hàng ngày
0 21 * * * php /home/<USER>/private/crawls_boyte/boyte_url.php

# Đấu giá
* * * * * bash /home/<USER>/private/dau-gia.sh
0 * * * * bash /home/<USER>/private/dau-gia-every-hour.sh
50 23 * * * php /home/<USER>/private/dau-gia/getAllOrganization.php
53 23 * * * bash /home/<USER>/private/dau-gia/dgts_organization2024.sh
0 2 * * * bash /home/<USER>/private/dau-gia/dgts_updateAllAuctioneer.sh

# Kiểm tra các proxy có bị lỗi không
#45 7-17 * * * php /home/<USER>/private/proxy_number_error.php
#*/5 * * * * php /home/<USER>/private/proxy_tinsoftproxy.php

# Bóc cá nhân
*/10 * * * * bash /home/<USER>/private/ca-nhan/crawls.sh

# Bóc phòng TN chuyên ngành và đồng bộ với DauThau.Net
*/10 * * * * bash /home/<USER>/private/phong-thi-nghiem/crawls.sh
0 3 * * * php /home/<USER>/private/phong-thi-nghiem/dinhchi.php

# Bóc ICB
15 1 * * 0 php /home/<USER>/private/icb-industry/icb_save.php
15 */6 * * * php /home/<USER>/private/icb-industry/icb_save.php/update_icb_industry.php

# Bóc chứng khoán
* * * * * bash /home/<USER>/private/stocks.sh

# Bóc văn phòng công nhận chất lượng
#22 22 * * * bash /home/<USER>/private/vilas/every_day.sh
#* * * * * bash /home/<USER>/private/vilas/every_minute1.sh

# Bóc quy hoạch
20 6-20 * * * bash /home/<USER>/private/quyhoach/crawl.sh

# Bóc KQLCNT Của MSC cũ trên Website mới (bóc lại lúc 8h tối thứ 7)
0 20 * * 6 bash /home/<USER>/private/crawl_2024_msc_cu/crawl.sh
* * * * * bash /home/<USER>/private/crawl_2024_msc_cu/every_minute.sh

# Bóc lại toàn bộ danh sách tổ chức lúc 8h tối thứ 7
0 20 * * 6 bash /home/<USER>/private/to-chuc/crawls_to_chuc_url.sh

# Dọn dẹp log, file tạm
20 23 * * * bash /home/<USER>/private/temp_destroy.sh

# Kiểm tra lại dữ liệu bóc Tin (Di chuyển từ kho crontab)
0 8 * * *  bash /home/<USER>/private/notification_oneday.sh
50 23 * * * php /home/<USER>/private/cron-notify/cr-kiemtrakhlcnt.php
55 23 * * * php /home/<USER>/private/cron-notify/cr-kiemtratbmt.php

# Thực hiện quét lại nhà đầu tư để cập nhật lại trạng thái status 1 tuần 1 lần
0 20 * * 6 php /home/<USER>/private/investor-approved/cdt_url.php --mode=getall
