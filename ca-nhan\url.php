<?php

/**
 * @Project DAUTHAU.INFO
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2021 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Sat, 08 Feb 2014 06:33:39 GMT
 */

define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME) . '/../')));
define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME))));

require NV_ROOTDIR . '/mainfile.php';

$help = '
/**
 * <PERSON><PERSON>y toàn bộ list url detail ở https://nangluchdxd.gov.vn/<PERSON>han
 * Lưu vào CSDL để bóc chi tiết
 *
 * Chạy trực tiếp hoặc thêm các tham số có thể thêm nhiều, mỗi tham số cách nhau space
 * url.php --mode=pre => Chạy hết từ page 1 đến page cuối cùng nếu không chỉ chạy page 1 đến page 3
 * url.php --max=10 => quét thường kỳ từ page 1 đến page 10, nếu không quét đến 3
 * url.php --handler=local => dùng file_get_content để lấy thay vì proxy
 * url.php --recrawl=yes => yes thì các url tìm thấy mà đã bóc sẽ được đánh dấu bóc lại
 */
';
echo $help . "\n";

$fetch_mode = $request_mode->get('mode', '');
$recrawl = $request_mode->get('recrawl', 'no');

if (file_exists(NV_CONSOLE_DIR . '/data/canhan_page_no.txt')) {
    $page_no = file_get_contents(NV_CONSOLE_DIR . '/data/canhan_page_no.txt');
    $page_no = intval($page_no);
} else {
    $page_no = 1;
}

$channelslack_file = 'canhan_page_error.txt';
$url = 'https://nangluchdxd.gov.vn/Canhan?page=' . $page_no . '&pagesize=20';
$fetch_data = getdataurl($url, 1);

if ($fetch_data[0] > 0) {
    $max_page = $fetch_mode == 'pre' ? $fetch_data[1] : $request_mode->get('max', 3);
    $page_no++;
    if ($page_no > $max_page) {
        $page_no = 1;
        if ($fetch_mode == 'pre') {
            notifySlack($channelslack_file, 'Đã lấy hết đến trang cuối cùng, mời dừng tiến trình pre để chạy tiến trình chính thức', $url, false);
        }
    }
    file_put_contents(NV_CONSOLE_DIR . '/data/canhan_page_no.txt', $page_no);
} else {
    if ($fetch_data[0] == -1) {
        // Bỏ qua trang bị lỗi
        notifySlack($channelslack_file, 'Trang bị lỗi', $url);
        $page_no++;
        file_put_contents(NV_CONSOLE_DIR . '/data/canhan_page_no.txt', $page_no);
        die("Trang " . ($page_no - 1) . " bị lỗi \n");
    }
}
die("Kết thúc, với " . $fetch_data[0] . " bản ghi mới, tổng số trang tìm thấy " . $fetch_data[1] . "\n");

/**
 * @param string $url
 * @param boolean $reload
 * @return number[]
 */
function getdataurl($url, $reload)
{
    global $dbcr, $config_proxy, $channelslack_file, $request_mode, $recrawl;

    echo "Bắt đầu bóc tại: " . $url . "\n";

    $number_data = 0;

    if ($request_mode->get('handler', 'system') == 'local') {
        echo "Local get...\n";
        $html = file_get_contents($url);
    } else {
        echo "Proxy get...\n";
        $html = geturlhtml($url);
    }

    // Đọc dữ liệu html
    $dom = new DOMDocument();
    libxml_use_internal_errors(true);
    if (empty($html) or (!empty($html) and !$dom->loadHTML($html))) {
        echo "Không đọc được trang để lấy HTML\n";
        if ($reload) {
            echo "Đang thử lại lần nữa\n";
            sleep($config_proxy['sleep']);
            return getdataurl($url, 0);
        }
        notifySlack($channelslack_file, 'Không đọc được trang để lấy HTML', $url);
        return [0, 0];
    }

    // Đọc html tìm lỗi
    if (preg_match_all('/\(Int32\spagesize\,\sInt32\spage\,\sString\skeyword\)/', $html, $m)) {
        if (!file_exists(NV_CONSOLE_DIR . '/data/canhan_max_page.txt')) {
            notifySlack($channelslack_file, 'Case lỗi nhưng không có file canhan_max_page.txt', $url);
            return [0, 0];
        }
        $max_page = file_get_contents(NV_CONSOLE_DIR . '/data/canhan_max_page.txt');
        $max_page = intval($max_page);
        return [-1, $max_page];
    }

    // Đọc thanh phân trang để tìm ra trang lớn nhất hiện tại
    $max_page = 0;
    if (preg_match('/\<ul[^\>]+class\=\"pagination\"[^\>]*\>[\r\n\s\t]*(.*?)[\r\n\s\t]*\<\/ul\>/u', $html, $m)) {
        $html1 = $m[1];
        unset($m);
        preg_match_all('/\<li[^\>]*\>[\r\n\s\t]*\<a(.*?)\>[\r\n\s\t]*(.*?)[\r\n\s\t]*\<\/a\>/u', $html1, $m);
        if (empty($m[1])) {
            notifySlack($channelslack_file, 'Không xử lý được các thẻ li ở phần phân trang', $url);
            return [0, 0];
        }
        $key = sizeof($m[1]) - 1;
        if (preg_match('/^([0-9]+)$/', trim($m[2][$key]))) {
            $max_page = intval($m[2][$key]);
        } elseif (preg_match('/page\=([0-9]+)/', $m[1][$key], $n)) {
            $max_page = intval($n[1]);
        } else {
            notifySlack($channelslack_file, 'Không tìm được số trang', $url);
            return [0, 0];
        }
    }
    if (file_exists(NV_CONSOLE_DIR . '/data/canhan_max_page.txt')) {
        $max_page_log = file_get_contents(NV_CONSOLE_DIR . '/data/canhan_max_page.txt');
        $max_page_log = intval($max_page_log);
        $max_page = ($max_page_log < $max_page) ? $max_page : $max_page_log;
    }

    file_put_contents(NV_CONSOLE_DIR . '/data/canhan_max_page.txt', $max_page);

    $xpath = new DOMXPath($dom);
    $_html = '';
    $nodeList = $xpath->query('//table/tr/td[@class="td_info"]');
    if ($nodeList->length > 0) {
        $_html = trim(html_entity_decode(DOMinnerHTML($nodeList[0]), ENT_QUOTES, 'UTF-8'));
    }
    if ($nodeList->length > 0) {
        foreach ($nodeList as $node) {
            $node_html = trim(html_entity_decode(DOMinnerHTML($node)));
            if ($node_html == 'Thông tin cá nhân') {
                continue;
            }

            if (preg_match('/class\=\"btnchitiet\sa_title\" data-id\=\"([^`]*?)\"/', $node_html, $m)) {
                $query_check_url_exist = $dbcr->query("SELECT id FROM nv4_canhan_url WHERE id_url = " . $m[1]);
                $data_url_exist = $query_check_url_exist->fetch();
                $curent_time = NV_CURRENTTIME;
                $url_boc_tin = 'https://nangluchdxd.gov.vn/api/Canhan/GetDetail?id=' . $m[1];
                if (empty($data_url_exist)) {
                    try {
                        $stmt = $dbcr->prepare('INSERT INTO nv4_canhan_url (
                            id_url, url_boc_tin, thoi_gian_lay_url
                        ) VALUES (
                            :id_url, :url_boc_tin, :thoi_gian_lay_url
                        )');
                        $stmt->bindParam(':id_url', $m[1], PDO::PARAM_INT);
                        $stmt->bindParam(':url_boc_tin', $url_boc_tin, PDO::PARAM_STR);
                        $stmt->bindParam(':thoi_gian_lay_url', $curent_time, PDO::PARAM_INT);
                        $stmt->execute();
                        $number_data++;
                    } catch (Exception $e) {
                        notifySlack($channelslack_file, "```\n" . print_r($e->getMessage(), true) . "\n```", $url);
                    }
                } else {
                    try {
                        $stmt = $dbcr->prepare('UPDATE nv4_canhan_url SET
                            url_boc_tin = :url_boc_tin, thoi_gian_lay_url = :thoi_gian_lay_url' . ($recrawl == 'yes' ? ', trang_thai=0' : '') . '
                        WHERE id_url = :id_url');
                        $stmt->bindParam(':id_url', $m[1], PDO::PARAM_INT);
                        $stmt->bindParam(':url_boc_tin', $url_boc_tin, PDO::PARAM_STR);
                        $stmt->bindParam(':thoi_gian_lay_url', $curent_time, PDO::PARAM_INT);
                        $stmt->execute();
                        $number_data++;
                    } catch (Exception $e) {
                        notifySlack($channelslack_file, "```\n" . print_r($e->getMessage(), true) . "\n```", $url);
                    }
                }
            } else {
                notifySlack($channelslack_file, 'Không tìm thấy thẻ a đến link chi tiết tổ chức', $url);
            }
        }
    } else {
        notifySlack($channelslack_file, 'Không tìm thấy thẻ td nào chứa link chi tiết', $url);
    }

    return [$number_data, $max_page];
}
