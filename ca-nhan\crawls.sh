#!/bin/bash

# Xác định PATH
SOURCE="${BASH_SOURCE[0]}"
while [ -h "$SOURCE" ]; do
  TARGET="$(readlink "$SOURCE")"
  if [[ $TARGET == /* ]]; then
    #echo "SOURCE '$SOURCE' is an absolute symlink to '$TARGET'"
    SOURCE="$TARGET"
  else
    DIR="$( dirname "$SOURCE" )"
    #echo "SOURCE '$SOURCE' is a relative symlink to '$TARGET' (relative to '$DIR')"
    SOURCE="$DIR/$TARGET"
  fi
done
#echo "SOURCE is '$SOURCE'"
RDIR="$( dirname "$SOURCE" )"
DIR="$( cd -P "$( dirname "$SOURCE" )" >/dev/null 2>&1 && pwd )"
#if [ "$DIR" != "$RDIR" ]; then
  #echo "DIR '$RDIR' resolves to '$DIR'"
#fi
#echo "DIR is '$DIR'"

if [ -f "$DIR/data/crawls.txt" ]; then
    if [ -n "$(find $DIR/data/ -type f -name crawls.txt -mmin +10)" ]; then
        # nếu 5 phút mà chưa chạy thì sẽ xóa file để tiếp tục chạy
        rm -f "$DIR/data/crawls.txt"
    else
        echo "Tiến trình này còn đang chạy, không thực thi đè";
        exit
    fi
fi

echo "$(date +%Y-%m-%d-%T)" > "$DIR/data/crawls.txt"

START=$(date +%s)

# Bóc chi tiết cá nhân
php "$DIR/detail.php" --limit=10
sleep 1

# Lấy URL cá nhân
php "$DIR/url.php" --mode=pre
sleep 1

rm -f "$DIR/data/crawls.txt"

echo ""
echo ""
echo "Hết tiến trình. Thời gian thực thi $(($(date +%s) - $START)) giây"
