#!/bin/bash

#Kill tiến trình nào > 200 Ram và thời gian qúa 20 phút
#* * * * * bash /home/<USER>/private/killps/main.sh

ps aux --sort -%mem | grep '/home/<USER>/private/crawls' | head -5 > /home/<USER>/private/killps/killps.txt

rm -f /home/<USER>/private/killps/kill.sh

php /home/<USER>/private/killps/killps.php

if [ -f /home/<USER>/private/killps/kill.sh ]; then
	bash /home/<USER>/private/killps/kill.sh
else
	echo "Không có tiến trình bào chạy quá";
fi