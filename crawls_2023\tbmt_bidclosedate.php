<?php

// <PERSON><PERSON><PERSON> danh sách TBMT chưa đóng thầu thay đổi thời điểm đóng thầu
// D<PERSON> kiến chạy 1 tiếng 1 lần do có 4.500 dữ liệu
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

$file_key = 'tbmt_url';
$mode = $request_mode->get('mode', ''); // php tbmt_open.php --mode=getall

$to_date = NV_CURRENTTIME;

$table_column = 'id, notifyId, inputResultId, bidOpenId, bidCloseDate, bidForm, bidMode, investField, isInternet, notifyNo, notifyVersion, processApply, procuringEntityCode, publicDate, publicDateKqlcnt, status, bidOpenDate, planType, planNo, stepCode, type, statusForNotify, bidId, createdBy';
$array_column = explode(',', $table_column);
$array_column = array_map('trim', $array_column);
$array_column_lower = array_map('strtolower', $array_column);
$sql_insert = "INSERT INTO `nv23_url` (vnd_updatetime, " . implode(', ', $array_column_lower) . ", other_data) VALUES (:vnd_updatetime, :" . implode(', :', $array_column_lower) . ",  :other_data)";
$sql_update = "UPDATE `nv23_url` SET vnd_updatetime=:vnd_updatetime, other_data=:other_data";
foreach ($array_column_lower as $_column) {
    if ($_column != 'id') {
        $sql_update .= ", " . $_column . "=:" . $_column;
    }
}
$sql_update .= " WHERE id=:id";

// Lấy từ thời điểm hiện tại ngược về trước 1 ngày trước
$_start_time = microtime(true);
$totalPages = 1;
$pageNumber = 0;

$new_data_sum = 0;

if ($mode == 'getall') {
    file_put_contents(NV_ROOTDIR . '/crawls_2023/logs/' . $file_key . '.log', date('H:i:s d/m/y') . " --> " . date('Y-m-d H:i:s', $from_date) . " -> " . date('Y-m-d H:i:s', $to_date) . " ===> to_date = " . $to_date . " \t totalElements=" . number_format($_totalElements) . "\n", FILE_APPEND);
}

$totalElements = 0;
$totalElements_i = 0;
$bidForm_no_TBMT = [
    'CDTRG',
    'CDT',
    'TTH',
    'MSTT',
    'LCNT_DB',
    'TGTHCD',
    'TVCN',
    'TCTVCN'
];

do {
    $new_data = 0;
    $num_data = 0;
    $num_run = 0;
    $data = geturlpage($pageNumber, 1);
    if (isset($data['page']['content'])) {
        $_totalPages = intval($data['page']['totalPages']);
        if ($_totalPages > $totalPages) {
            $totalPages = $_totalPages;
        }
        $_totalElements = intval($data['page']['totalElements']);
        if ($_totalElements > $totalElements) {
            $totalElements = $_totalElements;
        }

        $data = $data['page']['content'];

        $array_id_msc = [];
        foreach ($data as $key => $row) {
            $array_id_msc[] = $dbcr->quote($row['id']);
            ++$totalElements_i;
        }

        if (!empty($array_id_msc)) {
            $q = $dbcr->query("SELECT id, bidclosedate, publicdatekqlcnt, stepcode, bidopenid FROM `nv23_url` WHERE id IN (" . implode(',', $array_id_msc) . ")");
            $array_id_msc = [];
            while ($_r = $q->fetch()) {
                $array_id_msc[$_r['id']] = $_r;
            }
            $q->closeCursor();

            $vnd_updatetime = time();
            foreach ($data as $row) {
                ++$num_data;

                $row['bidCloseDate'] = isset($row['bidCloseDate']) ? $row['bidCloseDate'] : '';
                $row['publicDateKqlcnt'] = isset($row['publicDateKqlcnt']) ? $row['publicDateKqlcnt'] : '';
                $row['publicDateKqlcnt'] = isset($row['publicDateKqlcnt']) ? $row['publicDateKqlcnt'] : '';
                $row['planNo'] = isset($row['planNo']) ? $row['planNo'] : '';
                $row['bidOpenId'] = isset($row['bidOpenId']) ? $row['bidOpenId'] : '';
                $row['bidMode'] = isset($row['bidMode']) ? $row['bidMode'] : '';
                $row['bidPrice'] = isset($row['bidPrice']) ? $row['bidPrice'] : 0;
                unset($row['goods']);

                if (!isset($array_id_msc[$row['id']])) {
                    echo "News: " . $row['notifyNo'] . " " . $row['publicDateKqlcnt'] . "\n";
                    // Có TBMT mới, cần cho bóc mới
                    if (in_array($row['bidForm'], $bidForm_no_TBMT)) {
                        $dbcr->exec("INSERT INTO `nv23_crawls_kqlcnt` (`type`, `notifyno`, `notifyversion`, `id_msc`, `processapply`, `inputresultid`,  `bidopenid`, `bidmode`, `planno`) VALUES ( 'bidclosedate'," . $dbcr->quote($row['notifyNo']) . ", " . $dbcr->quote($row['notifyVersion']) . ", " . $dbcr->quote($row['id']) . ", " . $dbcr->quote($row['processApply']) . ", " . $dbcr->quote($row['inputResultId']) . ", " . $dbcr->quote($row['bidOpenId']) . ", " . $dbcr->quote($row['bidMode']) . ", " . $dbcr->quote($row['planNo']) . ")");
                    } else {
                        $dbcr->exec("INSERT INTO `nv23_crawls_tbmt` (`type`, `notifyno`, `notifyversion`, `id_msc`, `processapply`) VALUES ( 'bidclosedate'," . $dbcr->quote($row['notifyNo']) . ", " . $dbcr->quote($row['notifyVersion']) . ", " . $dbcr->quote($row['id']) . ", " . $dbcr->quote($row['processApply']) . ")");
                    }
                    $prepared = $dbcr->prepare($sql_insert);
                } elseif ($array_id_msc[$row['id']]['publicdatekqlcnt'] != $row['publicDateKqlcnt']) {
                    echo "Update kqlcnt: " . $row['notifyNo'] . " " . $row['publicDateKqlcnt'] . "\n";
                    // Có TBMT KQLCNT, cần cho bóc
                    $dbcr->exec("INSERT INTO `nv23_crawls_kqlcnt` (`type`, `notifyno`, `notifyversion`, `id_msc`, `processapply`, `inputresultid`,  `bidopenid`, `bidmode`, `planno`) VALUES ( 'bidclosedate'," . $dbcr->quote($row['notifyNo']) . ", " . $dbcr->quote($row['notifyVersion']) . ", " . $dbcr->quote($row['id']) . ", " . $dbcr->quote($row['processApply']) . ", " . $dbcr->quote($row['inputResultId']) . ", " . $dbcr->quote($row['bidOpenId']) . ", " . $dbcr->quote($row['bidMode']) . ", " . $dbcr->quote($row['planNo']) . ")");

                    // Nếu có KQLCNT mà chưa có bidopenid cũng thay đổi so với trước
                    if ($array_id_msc[$row['id']]['bidopenid'] != $row['bidOpenId']) {
                        $step = ($row['bidMode'] == '1_HTHS' and (strpos($row['stepCode'], 'step-3') or strpos($row['stepCode'], 'step-4'))) ? 3 : 2;
                        $dbcr->exec("INSERT INTO `nv23_crawls_kqmt` (`id_msc`, `notifyno`, `notifyid`, `bidopenid`, `planno`,`processapply`, `bidmode`, `stepcode`, `step`, `status`, `detail1`, url_time) VALUES (" . $dbcr->quote($row['id']) . ", " . $dbcr->quote($row['notifyNo']) . ", " . $dbcr->quote($row['notifyId']) . ", " . $dbcr->quote($row['bidOpenId']) . ", " . $dbcr->quote($row['planNo']) . ", " . $dbcr->quote($row['processApply']) . ", " . $dbcr->quote($row['bidMode']) . ", " . $dbcr->quote($row['stepCode']) . ", " . $step . ", " . $dbcr->quote($row['status']) . ", " . $dbcr->quote(json_encode($row, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT)) . ", " . $vnd_updatetime . ")");
                    }
                    $prepared = $dbcr->prepare($sql_update);
                } elseif ($array_id_msc[$row['id']]['stepcode'] != $row['stepCode']) {
                    echo "Update KQMT: " . $row['notifyNo'] . " " . $row['stepCode'] . "\n";
                    // Có TBMT có cập nhật stepCode cần cho bóc lại
                    $step = ($row['bidMode'] == '1_HTHS' and (strpos($row['stepCode'], 'step-3') or strpos($row['stepCode'], 'step-4'))) ? 3 : 2;
                    $dbcr->exec("INSERT INTO `nv23_crawls_kqmt` (`id_msc`, `notifyno`, `notifyid`, `bidopenid`, `planno`,`processapply`, `bidmode`, `stepcode`, `step`, `status`, `detail1`, url_time) VALUES (" . $dbcr->quote($row['id']) . ", " . $dbcr->quote($row['notifyNo']) . ", " . $dbcr->quote($row['notifyId']) . ", " . $dbcr->quote($row['bidOpenId']) . ", " . $dbcr->quote($row['planNo']) . ", " . $dbcr->quote($row['processApply']) . ", " . $dbcr->quote($row['bidMode']) . ", " . $dbcr->quote($row['stepCode']) . ", " . $step . ", " . $dbcr->quote($row['status']) . ", " . $dbcr->quote(json_encode($row, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT)) . ", " . $vnd_updatetime . ")");

                    $prepared = $dbcr->prepare($sql_update);
                } elseif ($array_id_msc[$row['id']]['bidclosedate'] != $row['bidCloseDate']) {
                    echo "Update TBMT: " . $row['notifyNo'] . " " . $row['bidCloseDate'] . "\n";
                    // Có TBMT có cập nhật, cần cho bóc lại
                    $dbcr->exec("INSERT INTO `nv23_crawls_tbmt` (`type`, `notifyno`, `notifyversion`, `id_msc`, `processapply`) VALUES ( 'bidclosedate'," . $dbcr->quote($row['notifyNo']) . ", " . $dbcr->quote($row['notifyVersion']) . ", " . $dbcr->quote($row['id']) . ", " . $dbcr->quote($row['processApply']) . ")");

                    $prepared = $dbcr->prepare($sql_update);
                } else {
                    continue;
                }

                $other_data = $row;

                try {
                    $prepared->bindValue(':vnd_updatetime', $vnd_updatetime, PDO::PARAM_STR);
                    foreach ($array_column as $_column) {
                        $_column_lower = strtolower($_column);
                        if ($_column_lower == 'isinternet') {
                            $_value = isset($row[$_column]) ? $row[$_column] : 0;
                            $prepared->bindValue(':' . $_column_lower, $_value, PDO::PARAM_INT);
                        } else {
                            $_value = isset($row[$_column]) ? $row[$_column] : '';
                            $prepared->bindValue(':' . $_column_lower, $_value, PDO::PARAM_STR);
                        }
                        unset($other_data[$_column]);
                    }
                    $prepared->bindValue(':other_data', json_encode($other_data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT), PDO::PARAM_STR);
                    $prepared->execute();
                    ++$new_data;
                } catch (PDOException $e) {
                    if (!preg_match('/Integrity constraint violation\: ([0-9]+) Duplicate entry (.*) for key \'id_msc\'/', $e->getMessage())) {
                        print_r($row);
                        print_r($e);
                        echo "ERROR: " . $row['notifyNo'] . " " . $row['publicDateKqlcnt'] . "\n";
                        die();
                    }
                }
            }
        }
    }
    echo "pageNumber: " . number_format($pageNumber) . "/" . number_format($totalPages) . "\n";
    echo "new_data: " . $new_data . "/" . $num_data . "\n";
    $new_data_sum += $new_data;
    ++$pageNumber;

    if ($pageNumber > $totalPages) {
        break;
    }
} while (1);

function geturlpage($pageNumber, $reload = 1)
{
    global $dbcr, $num_run, $from_date, $to_date;
    ++$num_run;

    $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-contractor-selection-v2/services/smart/search';
    $body = '{
        "pageSize": 10,
        "pageNumber": ' . $pageNumber . ',
        "query": [
          {
            "index": "es-contractor-selection",
            "keyWord": "",
            "matchType": "all",
            "matchFields": [
              "notifyNo",
              "bidName"
            ],
            "filters": [
              {
                "fieldName": "type",
                "searchType": "in",
                "fieldValues": [
                  "es-notify-contractor"
                ]
              },
              {
                "fieldName": "bidCloseDate",
                "searchType": "range",
                "from": "' . date('Y-m-d\TH:i:s', NV_CURRENTTIME) . '.000Z",
                "to": null
              }
            ]
          }
        ]
      }';

    $referer = 'https://muasamcong.mpi.gov.vn/web/guest/contractor-selection?render=index';
    $arr_agent = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:104.0) Gecko/20100101 Firefox/104.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/105.0.1343.42',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 9.0; SAMSUNG SM-F900U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.77 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ];

    srand((float) microtime() * 10000000);
    $rand = array_rand($arr_agent);
    $agent = $arr_agent[$rand];

    $_proxy = $dbcr->query("SELECT * FROM `nv4_proxy` WHERE `status`=2 ORDER BY `lasttime` ASC LIMIT 1")->fetch();
    $ch = curl_init();
    if (isset($_proxy['proxy'])) {
        $dbcr->query("UPDATE `nv4_proxy` SET `lasttime` = " . time() . ", number_run=number_run+1 WHERE `id` = " . $_proxy['id']);
        echo $_proxy['proxy'] . "\n";
        curl_setopt($ch, CURLOPT_PROXY, $_proxy['proxy']);
        if (isset($_proxy['proxyuserpwd']) and !empty($_proxy['proxyuserpwd'])) {
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, $_proxy['proxyuserpwd']);
        }
        curl_setopt($ch, CURLOPT_PROXYTYPE, $_proxy['proxytype']);
        curl_setopt($ch, CURLOPT_PROXYPORT, $_proxy['proxyport']);
    }
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
    curl_setopt($ch, CURLOPT_REFERER, $referer);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json'
    ));

    $json = curl_exec($ch);
    curl_close($ch);

    $data = json_decode($json, true);
    if (isset($data['page']['content'])) {
        return $data;
    } elseif ($reload and $num_run < 5) {
        return geturlpage($pageNumber, 1);
    } elseif ($reload) {
        return geturlpage($pageNumber, 0);
    }
    return [];
}
