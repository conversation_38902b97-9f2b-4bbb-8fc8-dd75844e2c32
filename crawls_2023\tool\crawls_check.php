<?php

// L<PERSON>y danh sách thông báo mời thầu mới, chỉ quét được: không phải chỉ định thầu
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

$sql = "SELECT MAX(`publicdate`) FROM `nv23_crawls_check`";
$_publicdatedb = $dbcr->query($sql)->fetchColumn();

$_currenttime = NV_CURRENTTIME;
do {
    $publicDate = date('Y-m-d', $_currenttime);
    if ($publicDate == $_publicdatedb) {
        break;
    }
    $dbcr->query("INSERT IGNORE INTO `nv23_crawls_check`(`publicdate`, `tbmt_db`, `tbmt_msc`) VALUES ('" . $publicDate . "',0,0)");
    echo $publicDate . "\n";
    $_currenttime = $_currenttime - 86400;
} while ($publicDate != '2023-03-23');

$query = $dbcr->query("SELECT * FROM `nv23_crawls_check` WHERE `tbmt_msc`=0 OR publicdate='" . date('Y-m-d', NV_CURRENTTIME) . "' ORDER BY `publicdate` ASC");
while ($_tbmt = $query->fetch()) {
    $publicDate = $_tbmt['publicdate'];
    echo $publicDate . "\n";
    $num_run = 0;
    $data = totalElements_TBMT($publicDate, 1);
    $tbmt_msc = intval($data['page']['totalElements']);

    $sql = "SELECT count(DISTINCT `notifyno`) FROM `nv23_crawls_tbmt` WHERE `publicdate` LIKE '" . $publicDate . "T%'";
    $tbmt_db = $dbcr->query($sql)->fetchColumn();

    $dbcr->query('UPDATE `nv23_crawls_check` SET `tbmt_db`=' . intval($tbmt_db) . ', `tbmt_msc`=' . intval($tbmt_msc) . ' WHERE publicdate = ' . $db->quote($publicDate));
}

$query = $dbcr->query("SELECT * FROM `nv23_crawls_check` WHERE `kqlcnt_msc`=0 OR publicdate='" . date('Y-m-d', NV_CURRENTTIME) . "' ORDER BY `publicdate` ASC");
while ($_tbmt = $query->fetch()) {
    $publicDate = $_tbmt['publicdate'];
    echo $publicDate . "\n";
    $num_run = 0;
    $data = totalElements_KQLCNT($publicDate, 1);
    $kqlcnt_msc = intval($data['page']['totalElements']);

    $sql = "SELECT count(DISTINCT `notifyno`) FROM `nv23_crawls_kqlcnt` WHERE `publicdatekqlcnt` LIKE '" . $publicDate . "T%'";
    $kqlcnt_db = $dbcr->query($sql)->fetchColumn();

    $dbcr->query('UPDATE `nv23_crawls_check` SET `kqlcnt_db`=' . intval($kqlcnt_db) . ', `kqlcnt_msc`=' . intval($kqlcnt_msc) . ' WHERE publicdate = ' . $db->quote($publicDate));
}

function totalElements_KQLCNT($publicDate, $pageNumber, $reload = 1)
{
    global $dbcr, $num_run, $type;
    ++$num_run;

    switch ($type) {
        case 'pre':
            $type_kq = '"es-pre-notify-contractor"';
            break;
        case 'nopre':
            $type_kq = '"es-notify-contractor"';
            break;
        default:
            $type_kq = '"es-notify-contractor","es-pre-notify-contractor"';
    }
    $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-contractor-selection-v2/services/smart/search';
    $body = '[{
        "pageSize": 10,
        "pageNumber": ' . $pageNumber . ',
        "query": [
            {
                "index": "es-contractor-selection",
                "keyWord": "",
                "matchType": "all",
                "matchFields": [
                    "notifyNo",
                    "bidName"
                ],
                "filters": [
                    {
                        "fieldName": "type",
                        "searchType": "in",
                        "fieldValues": [
                            ' . $type_kq . '
                        ]
                    },
                    {
                        "fieldName": "publicDateKqlcnt",
                        "searchType": "range",
                        "from": "' . $publicDate . 'T00:00:00.000Z",
                        "to": "' . $publicDate . 'T23:59:59.059Z"
                    }
                ]
            }
        ]
    }]';

    $referer = 'https://muasamcong.mpi.gov.vn/web/guest/contractor-selection?render=index';
    $arr_agent = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:104.0) Gecko/20100101 Firefox/104.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/105.0.1343.42',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 9.0; SAMSUNG SM-F900U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.77 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ];

    srand((float) microtime() * 10000000);
    $rand = array_rand($arr_agent);
    $agent = $arr_agent[$rand];

    $_proxy = $dbcr->query("SELECT * FROM `nv4_proxy` WHERE `status`=2 ORDER BY `lasttime` ASC LIMIT 1")->fetch();
    $ch = curl_init();
    if (isset($_proxy['proxy'])) {
        $dbcr->query("UPDATE `nv4_proxy` SET `lasttime` = " . time() . ", number_run=number_run+1 WHERE `id` = " . $_proxy['id']);
        echo $_proxy['proxy'] . "\n";
        curl_setopt($ch, CURLOPT_PROXY, $_proxy['proxy']);
        if (isset($_proxy['proxyuserpwd']) and !empty($_proxy['proxyuserpwd'])) {
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, $_proxy['proxyuserpwd']);
        }
        curl_setopt($ch, CURLOPT_PROXYTYPE, $_proxy['proxytype']);
        curl_setopt($ch, CURLOPT_PROXYPORT, $_proxy['proxyport']);
    }
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
    curl_setopt($ch, CURLOPT_REFERER, $referer);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json'
    ));

    $json = curl_exec($ch);
    curl_close($ch);

    $data = json_decode($json, true);
    if (isset($data['page']['content'])) {
        return $data;
    } elseif ($reload and $num_run < 5) {
        return totalElements_KQLCNT($pageNumber, 1);
    } elseif ($reload) {
        return totalElements_KQLCNT($pageNumber, 0);
    }
    return [];
}

function totalElements_TBMT($publicDate, $reload = 1)
{
    global $dbcr, $num_run;
    ++$num_run;

    $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-contractor-selection-v2/services/smart/search';
    $body = '[{
        "pageSize": 10,
        "pageNumber": 0,
        "query": [
          {
            "index": "es-contractor-selection",
            "keyWord": "",
            "matchType": "all",
            "matchFields": [
              "notifyNo",
              "bidName"
            ],
            "filters": [
                {
                    "fieldName": "publicDate",
                    "searchType": "range",
                    "from": "' . $publicDate . 'T00:00:00.000Z",
                    "to": "' . $publicDate . 'T23:59:59.059Z"
                },
                {
                    "fieldName": "type",
                    "searchType": "in",
                    "fieldValues": [
                      "es-notify-contractor"
                    ]
                },
                {
                    "fieldName": "caseKHKQ",
                    "searchType": "not_in",
                    "fieldValues": [
                      "1"
                    ]
                }
            ]
          }
        ]
    }]';

    $referer = 'https://muasamcong.mpi.gov.vn/web/guest/contractor-selection?render=index';
    $arr_agent = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:104.0) Gecko/20100101 Firefox/104.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/105.0.1343.42',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 9.0; SAMSUNG SM-F900U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.77 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ];

    srand((float) microtime() * 10000000);
    $rand = array_rand($arr_agent);
    $agent = $arr_agent[$rand];

    $_proxy = $dbcr->query("SELECT * FROM `nv4_proxy` WHERE `status`=2 ORDER BY `lasttime` ASC LIMIT 1")->fetch();
    $ch = curl_init();
    if (isset($_proxy['proxy'])) {
        $dbcr->query("UPDATE `nv4_proxy` SET `lasttime` = " . time() . ", number_run=number_run+1 WHERE `id` = " . $_proxy['id']);
        echo $_proxy['proxy'] . "\n";
        curl_setopt($ch, CURLOPT_PROXY, $_proxy['proxy']);
        if (isset($_proxy['proxyuserpwd']) and !empty($_proxy['proxyuserpwd'])) {
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, $_proxy['proxyuserpwd']);
        }
        curl_setopt($ch, CURLOPT_PROXYTYPE, $_proxy['proxytype']);
        curl_setopt($ch, CURLOPT_PROXYPORT, $_proxy['proxyport']);
    }
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
    curl_setopt($ch, CURLOPT_REFERER, $referer);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json'
    ));

    $json = curl_exec($ch);
    curl_close($ch);

    $data = json_decode($json, true);
    if (isset($data['page']['content'])) {
        return $data;
    } elseif ($reload and $num_run < 5) {
        return totalElements_TBMT($publicDate, 1);
    } elseif ($reload) {
        return totalElements_TBMT($publicDate, 0);
    }
    return [];
}
