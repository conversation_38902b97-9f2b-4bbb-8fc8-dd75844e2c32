<?php
/* Copy phone, email, fax from nv4_vi_businesslistings_old -> phone, email, fax nv4_vi_businesslistings_info */
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME))));
require NV_ROOTDIR . '/mainfile.php';

$limit = 100;
$query_old = $db->query('SELECT id, code, phone, email, fax FROM ' . BUSINESS_PREFIX_GLOBAL . '_old WHERE convert_info = 0 AND code != "" ORDER BY id LIMIT ' . $limit);
while ($old_business = $query_old->fetch()) {
    // Đ<PERSON>h dấu là đã chạy
    $db->query('UPDATE ' . BUSINESS_PREFIX_GLOBAL . '_old SET convert_info = -1 WHERE id = ' . $old_business['id']);

    // Kiểm tra xem code có trong bảng nv4_vi_businesslistings_info không
    $info_business = $db->query('SELECT id, code, phone, email, fax FROM ' . BUSINESS_PREFIX_GLOBAL . '_info WHERE code = ' . $db->quote($old_business['code']) . ' LIMIT 1')
        ->fetch();
    if (!empty($info_business)) {
        // xác định giá trị cần cập nhật
        $phone = !empty($old_business['phone']) ? $old_business['phone'] : (!empty($info_business['phone']) ? $info_business['phone'] : '');
        $email = !empty($old_business['email']) ? $old_business['email'] : (!empty($info_business['email']) ? $info_business['email'] : '');
        $fax = !empty($old_business['fax']) ? $old_business['fax'] : (!empty($info_business['fax']) ? $info_business['fax'] : '');

        $stmt = $db->prepare('UPDATE ' . BUSINESS_PREFIX_GLOBAL . '_info SET phone = :phone, fax = :fax, email = :email, elasticsearch = 8 WHERE id = ' . $info_business['id']);
        $stmt->bindParam(':phone', $phone, PDO::PARAM_STR);
        $stmt->bindParam(':email', $email, PDO::PARAM_STR);
        $stmt->bindParam(':fax', $fax, PDO::PARAM_STR);
        $exc = $stmt->execute();
        if ($exc) {
            // Đánh dấu id
            $db->query('UPDATE ' . BUSINESS_PREFIX_GLOBAL . '_old SET convert_info = ' . $info_business['id'] . ' WHERE id = ' . $old_business['id']);
            echo '- Update success, id old: ' . $old_business['id'] . ', id info: ' . $info_business['id'] . "\n";
        } else {
            // Đánh dấu id
            $db->query('UPDATE ' . BUSINESS_PREFIX_GLOBAL . '_old SET convert_info = -2 WHERE id = ' . $old_business['id']);
            echo '-- Error: Update fail, id old: ' . $old_business['id'];
        }
    } else {
        $db->query('UPDATE ' . BUSINESS_PREFIX_GLOBAL . '_old SET convert_info = -3 WHERE id = ' . $old_business['id']);
        echo '-- Error: info_business is empty, id old: ' . $old_business['id'];
    }
}
$query_old->closeCursor();
die('Finish' . "\n");
