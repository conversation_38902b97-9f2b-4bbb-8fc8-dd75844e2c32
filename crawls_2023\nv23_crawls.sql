CREATE TABLE `nv23_crawls_kqlcnt` (
  `id` int(11) NOT NULL,
  `type` varchar(15) NOT NULL,
  `notifyno` varchar(12) NOT NULL,
  `notifyversion` varchar(2) NOT NULL,
  `id_msc` varchar(36) NOT NULL DEFAULT '',
  `processapply` varchar(20) NOT NULL DEFAULT '',
  `inputresultid` varchar(36) NOT NULL DEFAULT '',
  `bidopenid` varchar(36) NOT NULL DEFAULT '',
  `bidmode` varchar(20) NOT NULL DEFAULT '',
  `planno` varchar(12) NOT NULL DEFAULT '',
  `bidfield` varchar(10) NOT NULL DEFAULT '',
  `bidform` varchar(20) NOT NULL DEFAULT '',
  `bidprice` bigint(20) NOT NULL DEFAULT 0,
  `url_run` int(11) NOT NULL DEFAULT 0,
  `uniqid` varchar(25) NOT NULL DEFAULT '',
  `count_url` smallint(6) NOT NULL DEFAULT 0,
  `dauthau_info` int(11) NOT NULL DEFAULT 0,
  `detail` mediumtext COMPRESSED NOT NULL DEFAULT '',
  `detail_goods` mediumtext COMPRESSED NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `nv23_crawls_kqmt`
--

CREATE TABLE `nv23_crawls_kqmt` (
  `id` int(11) NOT NULL,
  `url_run` int(11) NOT NULL DEFAULT 0,
  `count_url` tinyint(4) NOT NULL DEFAULT 0,
  `id_msc` varchar(36) NOT NULL DEFAULT '',
  `notifyno` varchar(15) NOT NULL DEFAULT '',
  `notifyid` varchar(36) NOT NULL DEFAULT '',
  `bidopenid` varchar(36) NOT NULL DEFAULT '' COMMENT 'ID kqmt',
  `planno` varchar(12) NOT NULL DEFAULT '' COMMENT 'Mã KDLCNT',
  `processapply` varchar(20) NOT NULL DEFAULT '',
  `bidmode` varchar(20) NOT NULL DEFAULT '',
  `status` varchar(20) NOT NULL DEFAULT '',
  `stepcode` varchar(40) NOT NULL DEFAULT '',
  `step` tinyint(4) NOT NULL DEFAULT 2 COMMENT '2 -> kqmt; 3 -> dsntdkt',
  `detail1` text NOT NULL DEFAULT '',
  `detail2` mediumtext COMPRESSED NOT NULL DEFAULT '',
  `detail3` mediumtext COMPRESSED NOT NULL DEFAULT '',
  `dauthau_info` tinyint(4) NOT NULL DEFAULT 0,
  `crawls_info` text NOT NULL DEFAULT '',
  `url_time` int(11) NOT NULL DEFAULT 0,
  `uniqid` varchar(25) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `nv23_crawls_tbmt`
--

CREATE TABLE `nv23_crawls_tbmt` (
  `id` int(11) NOT NULL,
  `type` varchar(15) NOT NULL,
  `notifyno` varchar(12) NOT NULL,
  `notifyversion` varchar(2) NOT NULL,
  `id_msc` varchar(36) NOT NULL DEFAULT '',
  `processapply` varchar(20) NOT NULL DEFAULT '',
  `url_run` int(11) NOT NULL DEFAULT 0,
  `uniqid` varchar(25) NOT NULL DEFAULT '',
  `count_url` smallint(6) NOT NULL DEFAULT 0,
  `dauthau_info` int(11) NOT NULL DEFAULT 0,
  `detail` mediumtext COMPRESSED NOT NULL DEFAULT '\'\''
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `nv23_url`
--

CREATE TABLE `nv23_url` (
  `vnd_id` int(11) NOT NULL,
  `vnd_updatetime` int(11) NOT NULL DEFAULT 0,
  `vnd_id_tbmt` int(11) NOT NULL DEFAULT 0,
  `vnd_id_mothau` int(11) NOT NULL DEFAULT 0,
  `vnd_id_kqlcnt` int(11) NOT NULL DEFAULT 0,
  `id` varchar(36) NOT NULL,
  `notifyid` varchar(36) NOT NULL DEFAULT '',
  `inputresultid` varchar(36) NOT NULL DEFAULT '',
  `bidopenid` varchar(36) NOT NULL DEFAULT '',
  `bidclosedate` varchar(23) NOT NULL DEFAULT '',
  `bidform` varchar(20) NOT NULL DEFAULT '',
  `bidmode` varchar(20) NOT NULL DEFAULT '',
  `investfield` varchar(20) NOT NULL DEFAULT '',
  `isinternet` tinyint(4) NOT NULL DEFAULT 0,
  `notifyno` varchar(12) NOT NULL DEFAULT '',
  `notifyversion` varchar(2) NOT NULL DEFAULT '',
  `processapply` varchar(5) NOT NULL DEFAULT '',
  `procuringentitycode` varchar(12) NOT NULL DEFAULT '',
  `publicdate` varchar(23) NOT NULL DEFAULT '',
  `publicdatekqlcnt` varchar(23) NOT NULL DEFAULT '',
  `status` varchar(12) NOT NULL DEFAULT '',
  `bidopendate` varchar(23) NOT NULL DEFAULT '',
  `plantype` varchar(12) NOT NULL DEFAULT '',
  `planno` varchar(12) NOT NULL DEFAULT '',
  `stepcode` varchar(40) NOT NULL,
  `type` varchar(30) NOT NULL DEFAULT '',
  `statusfornotify` varchar(12) NOT NULL DEFAULT '',
  `bidid` varchar(40) NOT NULL,
  `createdby` varchar(15) NOT NULL,
  `other_data` text COMPRESSED NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `nv23_crawls_kqlcnt`
--
ALTER TABLE `nv23_crawls_kqlcnt`
  ADD PRIMARY KEY (`id`),
  ADD KEY `url_run` (`url_run`),
  ADD KEY `id_msc` (`id_msc`);

--
-- Indexes for table `nv23_crawls_kqmt`
--
ALTER TABLE `nv23_crawls_kqmt`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `id_msc` (`id_msc`),
  ADD KEY `uniqid` (`uniqid`),
  ADD KEY `notifyno` (`notifyno`);

--
-- Indexes for table `nv23_crawls_tbmt`
--
ALTER TABLE `nv23_crawls_tbmt`
  ADD PRIMARY KEY (`id`),
  ADD KEY `url_run` (`url_run`),
  ADD KEY `id_msc` (`id_msc`);

--
-- Indexes for table `nv23_url`
--
ALTER TABLE `nv23_url`
  ADD PRIMARY KEY (`vnd_id`),
  ADD KEY `notifyno` (`notifyno`),
  ADD KEY `id` (`id`),
  ADD KEY `notifyno_2` (`notifyno`,`notifyversion`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `nv23_crawls_kqlcnt`
--
ALTER TABLE `nv23_crawls_kqlcnt`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `nv23_crawls_kqmt`
--
ALTER TABLE `nv23_crawls_kqmt`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `nv23_crawls_tbmt`
--
ALTER TABLE `nv23_crawls_tbmt`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `nv23_url`
--
ALTER TABLE `nv23_url`
  MODIFY `vnd_id` int(11) NOT NULL AUTO_INCREMENT;
COMMIT;