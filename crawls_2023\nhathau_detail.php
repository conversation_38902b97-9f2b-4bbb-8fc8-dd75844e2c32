<?php

// Danh sách nhà thầu được phê duyệt
// https://muasamcong.mpi.gov.vn/web/guest/approved-contractors-list
define('NV_SYSTEM', true);

// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME))));
require NV_ROOTDIR . '/mainfile.php';

$id = (int) $request_mode->get('id', 0); // php kqlcnt_detail.php --id=285003
$uniqid = uniqid('', true);
if ($id > 0) {
    $exec = $dbcr->query("UPDATE nv22_nhathau_url SET url_run='-" . NV_CURRENTTIME . "', count_url=count_url+1, uniqid=" . $dbcr->quote($uniqid) . "  WHERE id = " . $id);
} else {
    $uniqid = uniqid('', true);
    $_start_time = time();
    $exec = $dbcr->exec("UPDATE nv22_nhathau_url SET url_run='-" . NV_CURRENTTIME . "', count_url=count_url+1, uniqid=" . $dbcr->quote($uniqid) . "  WHERE `url_run`=0 AND uniqid='' ORDER BY `re_crawls` ASC LIMIT 10");
}

if ($exec) {
    $_query = $dbcr->query("SELECT id, orgcode, detail1 FROM `nv22_nhathau_url`  WHERE url_run='-" . NV_CURRENTTIME . "' AND uniqid = " . $dbcr->quote($uniqid));
    while ($_contractor = $_query->fetch()) {
        print_r($_contractor);
        if (empty($_contractor['detail1'])) {
            $data = get_nhathau_detail1($_contractor['orgcode'], 1);
            if (isset($data['content'])) {
                foreach ($data['content'] as $row) {
                    $detail1 = json_encode($row, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                    $dbcr->query("UPDATE `nv22_nhathau_url` SET `detail1` = " . $dbcr->quote($detail1) . " WHERE id = " . $_contractor['id']);
                }
            }
        }
        $data = geturlpage($_contractor, 1);
        if (isset($data['orgCode'])) {
            $dbcr->query("UPDATE nv22_nhathau_url SET uniqid = '', url_run=" . NV_CURRENTTIME . ", detail2=" . $dbcr->quote(json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT)) . " WHERE id=" . $_contractor['id']);
            $num_run = 0;
            $data = getfeepage($_contractor['orgcode']);
            if (isset($data['responseCode'])) {
                $dbcr->query("UPDATE nv22_nhathau_url SET uniqid = '', url_run=" . NV_CURRENTTIME . ", request_fee=" . $dbcr->quote(json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT)) . " WHERE id=" . $_contractor['id']);
            }

            // Bóc thông tin bổ sung của nhà thầu
            $data = get_org_infor_spm_page($_contractor['orgcode'], 1);
            if (!empty($data)) {
                $lastest_version_key = array_key_last($data);
                if (isset($data[$lastest_version_key]['orgCode'])) {
                    $dbcr->query("UPDATE nv22_nhathau_url SET uniqid = '', url_run=" . NV_CURRENTTIME . ", request_spm=" . $dbcr->quote(json_encode($data[$lastest_version_key], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT)) . " WHERE id=" . $_contractor['id']);
                }
            }

            echo "OK\n";
        } else {
            echo "NO: orgCode\n";
        }
    }
} else {
    $exec = $dbcr->exec("UPDATE nv22_nhathau_url SET url_run=0, count_url=0, uniqid='', re_crawls=" . NV_CURRENTTIME . ", business_id=0  WHERE `url_run` > 999 ORDER BY `url_run` ASC LIMIT 30");
    if (empty($exec)) {
        echo "No Data, re_crawls\n";
    }
}
echo "Runtime = " . (time() - $_start_time) . "\n\n";

function geturlpage($_contractor, $reload = 1)
{
    global $dbcr;
    $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-contractors-approved/services/get-detail-approve-bidder';

    $body = '{"orgCode":"' . $_contractor['orgcode'] . '"}';

    $referer = 'https://muasamcong.mpi.gov.vn/web/guest/approved-contractors-list?p_p_id=egpportalcontractorsapproved_WAR_egpportalcontractorsapproved&p_p_lifecycle=0&p_p_state=normal&p_p_mode=view&_egpportalcontractorsapproved_WAR_egpportalcontractorsapproved_render=detail&orgCode=' . $_contractor['orgcode'];
    // &taxCode=0100101523&orgFullname=CÔNG TY CỔ PHẦN MỸ NGHỆ XUẤT NHẬP KHẨU HÀ NỘI

    $arr_agent = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:104.0) Gecko/20100101 Firefox/104.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/105.0.1343.42',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 9.0; SAMSUNG SM-F900U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.77 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ];

    srand((float) microtime() * 10000000);
    $rand = array_rand($arr_agent);
    $agent = $arr_agent[$rand];

    $_proxy = $dbcr->query("SELECT * FROM `nv4_proxy` WHERE `status`=2 ORDER BY `lasttime` ASC LIMIT 1")->fetch();
    $ch = curl_init();
    if (isset($_proxy['proxy'])) {
        $dbcr->query("UPDATE `nv4_proxy` SET `lasttime` = " . time() . ", number_run=number_run+1 WHERE `id` = " . $_proxy['id']);
        echo "geturlpage: " . $_proxy['proxy'] . "\n";
        curl_setopt($ch, CURLOPT_PROXY, $_proxy['proxy']);
        if (isset($_proxy['proxyuserpwd']) and !empty($_proxy['proxyuserpwd'])) {
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, $_proxy['proxyuserpwd']);
        }
        curl_setopt($ch, CURLOPT_PROXYTYPE, $_proxy['proxytype']);
        curl_setopt($ch, CURLOPT_PROXYPORT, $_proxy['proxyport']);
    }
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 60);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
    curl_setopt($ch, CURLOPT_REFERER, $referer);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json'
    ));

    $json = curl_exec($ch);
    curl_close($ch);

    $data = json_decode($json, true);
    if (isset($data['orgCode'])) {
        return $data;
    } elseif ($reload) {
        return geturlpage($_contractor, 0);
    }
    return [];
}

function getfeepage($contractorCode, $reload = 1)
{
    global $dbcr, $num_run;
    $num_run = $num_run + 1;
    $url = 'https://muasamcong.mpi.gov.vn/api/unau/epayclaimr/pm-trans/query-for-payment';

    $referer = 'https://muasamcong.mpi.gov.vn/egp/epaymentfe/payment-info/request-fee';
    $agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36';

    $body = '{"body":{"pageNumber":"0","pageSize":10000,"criteria":{"contractorCode":{"equals":"' . $contractorCode . '"},"feeType":{"in":[1,6]},"status":{"equals":0}}}}';

    $_proxy = $dbcr->query("SELECT * FROM `nv4_proxy` WHERE `status`=2 ORDER BY `lasttime` ASC LIMIT 1")->fetch();
    $ch = curl_init();
    if (isset($_proxy['proxy'])) {
        $dbcr->query("UPDATE `nv4_proxy` SET `lasttime` = " . time() . ", number_run=number_run+1 WHERE `id` = " . $_proxy['id']);
        echo "getfeepage: " . $_proxy['proxy'] . "\n";
        curl_setopt($ch, CURLOPT_PROXY, $_proxy['proxy']);
        if (isset($_proxy['proxyuserpwd']) and !empty($_proxy['proxyuserpwd'])) {
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, $_proxy['proxyuserpwd']);
        }
        curl_setopt($ch, CURLOPT_PROXYTYPE, $_proxy['proxytype']);
        curl_setopt($ch, CURLOPT_PROXYPORT, $_proxy['proxyport']);
    }
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 60);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
    curl_setopt($ch, CURLOPT_REFERER, $referer);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json'
    ));

    $json = curl_exec($ch);
    curl_close($ch);
    $data = json_decode($json, true);
    if (isset($data['responseCode'])) {
        return $data;
    } elseif ($reload and $num_run < 5) {
        return getfeepage($contractorCode, 1);
    }
    return [];
}
function get_nhathau_detail1($orgcode, $reload = 1)
{
    global $dbcr, $num_run, $totalPages;
    $num_run = $num_run + 1;
    $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-contractors-approved/services/get-list';

    $referer = 'https://muasamcong.mpi.gov.vn/web/guest/approved-contractors-list';
    $arr_agent = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:104.0) Gecko/20100101 Firefox/104.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/105.0.1343.42',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 9.0; SAMSUNG SM-F900U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.77 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ];
    srand((float) microtime() * 10000000);
    $rand = array_rand($arr_agent);
    $agent = $arr_agent[$rand];

    $body = '{
      "pageSize": 7,
      "pageNumber": 0,
      "queryParams": {
        "officePro": {
          "contains": null
        },
        "effRoleDate": {
          "greaterThanOrEqual": null,
          "lessThanOrEqual": null
        },
        "isForeignInvestor": {
          "equals": null
        },
        "roleType": {
          "equals": "NT"
        },
        "decNo": {
          "contains": null
        },
        "orgName": {
          "contains": null
        },
        "taxCode": {
          "contains": null
        },
        "orgNameOrOrgCode": {
          "contains": "' . $orgcode . '"
        },
        "agencyName": {
          "in": null
        }
      }
    }';
    $_proxy = $dbcr->query("SELECT * FROM `nv4_proxy` WHERE `status`=2 ORDER BY `lasttime` ASC LIMIT 1")->fetch();
    $ch = curl_init();
    if (isset($_proxy['proxy'])) {
        $dbcr->query("UPDATE `nv4_proxy` SET `lasttime` = " . time() . ", number_run=number_run+1 WHERE `id` = " . $_proxy['id']);
        echo $_proxy['proxy'] . "\n";
        curl_setopt($ch, CURLOPT_PROXY, $_proxy['proxy']);
        if (isset($_proxy['proxyuserpwd']) and !empty($_proxy['proxyuserpwd'])) {
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, $_proxy['proxyuserpwd']);
        }
        curl_setopt($ch, CURLOPT_PROXYTYPE, $_proxy['proxytype']);
        curl_setopt($ch, CURLOPT_PROXYPORT, $_proxy['proxyport']);
    }
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 60);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
    curl_setopt($ch, CURLOPT_REFERER, $referer);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json'
    ));

    $json = curl_exec($ch);
    curl_close($ch);
    $data = json_decode($json, true);
    if (isset($data['content'])) {
        return $data;
    } elseif ($reload and $num_run < 5) {
        return get_nhathau_detail1($orgcode, 1);
    }
    return [];
}

function get_org_infor_spm_page($contractorCode, $reload = 1)
{
    global $dbcr, $num_run;
    $num_run = $num_run + 1;
    $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-contractors-approved/services/get-org-infor-spm';

    $referer = 'https://muasamcong.mpi.gov.vn/web/guest/approved-contractors-list?p_p_id=egpportalcontractorsapproved_WAR_egpportalcontractorsapproved&p_p_lifecycle=0&p_p_state=normal&p_p_mode=view&_egpportalcontractorsapproved_WAR_egpportalcontractorsapproved_render=detail&orgCode=' . $contractorCode . '&effRoleDate=';
    $agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';

    $body = '{"body": "' . $contractorCode . '"}';

    $_proxy = $dbcr->query("SELECT * FROM `nv4_proxy` WHERE `status`=2 ORDER BY `lasttime` ASC LIMIT 1")->fetch();
    $ch = curl_init();
    if (isset($_proxy['proxy'])) {
        $dbcr->query("UPDATE `nv4_proxy` SET `lasttime` = " . time() . ", number_run=number_run+1 WHERE `id` = " . $_proxy['id']);
        echo "get_org_infor_spm_page: " . $_proxy['proxy'] . "\n";
        curl_setopt($ch, CURLOPT_PROXY, $_proxy['proxy']);
        if (isset($_proxy['proxyuserpwd']) and !empty($_proxy['proxyuserpwd'])) {
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, $_proxy['proxyuserpwd']);
        }
        curl_setopt($ch, CURLOPT_PROXYTYPE, $_proxy['proxytype']);
        curl_setopt($ch, CURLOPT_PROXYPORT, $_proxy['proxyport']);
    }
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 60);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
    curl_setopt($ch, CURLOPT_REFERER, $referer);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json'
    ));

    $json = curl_exec($ch);
    curl_close($ch);
    $data = json_decode($json, true);
    if (isset($data[0]['orgCode'])) {
        return $data;
    } elseif ($reload and $num_run < 5) {
        return get_org_infor_spm_page($contractorCode, 1);
    }
    return [];
}
