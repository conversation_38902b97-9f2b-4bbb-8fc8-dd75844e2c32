<?php

/*
 * <PERSON><PERSON> tả:
 * <PERSON>óc lại các TBMT do thiếu HSMT: tbmt hỗn hợp yêu cầu dùng api https://muasamcong.mpi.gov.vn/o/egp-portal-contractor-selection-v2/services/lcnt_tbmt_hsmt mới bóc được hsmt
 */

define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__FILE__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

$tbmt_id = 1620680; //chỉ kiểm tra từ id này đổ về hiện tại

$file_name = NV_ROOTDIR . '/tools_2025/hsmt_hh.txt';
if (file_exists($file_name)) {
    [$tbmt_id, $max_id] = array_map('intval', explode('_', file_get_contents($file_name)));
} else {
    $max_id = $db->query('SELECT MAX(t2.id) FROM nv4_vi_bidding_detail t1 INNER JOIN nv4_vi_bidding_row t2 ON t1.id = t2.id WHERE t2.linh_vuc_thong_bao = 5 AND t1.ho_so = "" AND t1.quyet_dinh_phe_duyet != ""')
        ->fetch(PDO::FETCH_NUM);
}

$tbmt_id2 = $tbmt_id + 1000;
//Lấy id các gói thầu
$result = $db->query('SELECT t2.id, t2.id_msc, t2.type_bid FROM nv4_vi_bidding_detail t1 INNER JOIN nv4_vi_bidding_row t2 ON t1.id = t2.id WHERE t2.id > ' . $tbmt_id . ' AND t2.id <= ' . $tbmt_id2 . ' AND t2.linh_vuc_thong_bao = 5 AND t1.ho_so = "" AND t1.quyet_dinh_phe_duyet != "" ORDER BY t1.id ASC LIMIT 10');

if ($tbmt_id2 >= $max_id && $result->rowCount() == 0) {
    //hết dữ liệu và id lớn hơn id_max
    echo(">> \033[91mHết dữ liệu \033[0m");
    unlink($file_name);
    sleep(10); //sleep 10s để check lại
    exit(1);
}
$tbmt_id = $tbmt_id2;

echo "---------\n";

while ($data = $result->fetch()) {
    $tbmt_id = $data['id'];

    echo "Kiểm tra id: " . $tbmt_id . "\n";

    //bóc lại hsmt
    $_tbmt = $dbcr->query("SELECT * FROM `nv23_crawls_tbmt` WHERE id_msc = " . $dbcr->quote($data['id_msc']))
        ->fetch();
    $data_hsmt = get_hsmt($_tbmt, 1);
    $detail = [];
    if (!empty($data_hsmt)) {
        foreach ($data_hsmt as $key => $value) {
            $detail[$key] = $value;
        }

        // ho_so
        $ho_so_array = [];
        $offline_ho_so = [];
        $arr_code = [];
        $row = [];
        if ($data['type_bid'] and isset($detail['bidoInvBiddingDTO']) and !in_array($data['processapply'], ['KHAC', 'CPTPP', 'EVFTA', 'UKFTA'])) {
            $arr_file = [];
            foreach ($detail['bidoInvBiddingDTO'] as $bidoInvBiddingDTO) {
                $formValue = json_decode($bidoInvBiddingDTO['formValue'], true);
                foreach ($formValue as $_file) {
                    if (!empty($_file['fileName']) && !empty($_file['fileId'])) {
                        $row['get_ho_so'] = 1; // Set để hệ thống biết có file cần download
                        $arr_file[$bidoInvBiddingDTO['formCode']][] = [
                            'fileName' => $_file['fileName'],
                            'fileId' => $_file['fileId']
                        ];
                        $arr_file[$bidoInvBiddingDTO['chapterCode']][] = [
                            'fileName' => $_file['fileName'],
                            'fileId' => $_file['fileId']
                        ];
                    }
                }
            }
            foreach ($detail['bidaInvChapterConfList'] as $hsmt) {
                if ($hsmt['pcode'] == null && $hsmt['code'] != '') {
                    $arr_code[] = $hsmt['code'];
                } else {
                    $arr_code[] = $hsmt['pcode'];
                }
                if ($hsmt['lev'] == 0 || $hsmt['lev'] == 1) {
                    if ($hsmt['pcode'] == '') {
                        $ho_so_array[$hsmt['code']][-1] = [
                            'pcode' => '',
                            'code' => $hsmt['code'],
                            'name' => $hsmt['name']
                        ];
                    } else {
                        $file = '';
                        if (!empty($arr_file[$hsmt['code']])) {
                            $file = $arr_file[$hsmt['code']];
                        }
                        $ho_so_array[$hsmt['pcode']][$hsmt['orderIndex']] = [
                            'pcode' => $hsmt['pcode'],
                            'code' => $hsmt['code'],
                            'name' => $hsmt['name'],
                            'arr_file' => $file
                        ];
                    }
                }
            }
        } else {
            if (!empty($detail['bidInvContractorOfflineDTO'])) {
                if (!empty($detail['bidInvContractorOfflineDTO']['decisionFileName'])) {
                    // Thông tin quyết định phê duyệt
                    // <div class="list-group-item"><a href="#"><span class="is_fast ctx-khlcnt-pl">2022-HSMT-G5.10.pdf</span></a></div>
                    $row['quyet_dinh_phe_duyet'] = '<div class="list-group-item"><a href="#"><span class="is_fast ctx-khlcnt-pl">' . $detail['bidInvContractorOfflineDTO']['decisionFileName'] . '</span></a></div>';
                    $quyet_dinh_download = [];
                    $quyet_dinh_download['file'][1]['arr_file'][] = [
                        'fileName' => $detail['bidInvContractorOfflineDTO']['decisionFileName'],
                        'fileId' => $detail['bidInvContractorOfflineDTO']['decisionFileId']
                    ];
                    $row['quyet_dinh_download'] = json_encode($quyet_dinh_download, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                    $row['co_quan_quyet_dinh'] = $detail['bidInvContractorOfflineDTO']['decisionAgency'];
                    $row['ngay_quyet_dinh'] = strtotime($detail['bidInvContractorOfflineDTO']['decisionDate']);
                    $row['so_quyet_dinh'] = $detail['bidInvContractorOfflineDTO']['decisionNo'];
                }
                // File
                if (!empty($detail['bidInvContractorOfflineDTO']['fileName'])) {
                    $row['get_ho_so'] = 1; // Set để hệ thống biết có file cần download
                    $offline_ho_so['file'][1]['arr_file'][] = [
                        'fileName' => $detail['bidInvContractorOfflineDTO']['fileName'],
                        'fileId' => $detail['bidInvContractorOfflineDTO']['fileId']
                    ];
                }
                if (!empty($detail['bidInvContractorOfflineDTO']['briefFileName'])) {
                    $offline_ho_so['briefFile'][1]['arr_file'][] = [
                        'fileName' => $detail['bidInvContractorOfflineDTO']['otherFileName'],
                        'fileId' => $detail['bidInvContractorOfflineDTO']['otherFileId']
                    ];
                }

                if (!empty($detail['bidInvContractorOfflineDTO']['listOtherFile'])) {
                    $listOtherFile = json_decode($detail['bidInvContractorOfflineDTO']['listOtherFile'], true);
                    $total_list = count($listOtherFile) / 2;
                    for ($i = 1; $i <= $total_list; $i++) {
                        if (!empty($listOtherFile['otherFileId' . $i])) {
                            $offline_ho_so['otherFile'][1]['arr_file'][] = [
                                'fileName' => $listOtherFile['otherFileName' . $i],
                                'fileId' => $listOtherFile['otherFileId' . $i]
                            ];
                        }
                    }
                    if (!empty($listOtherFile['listOtherFiles'])) {
                        foreach ($listOtherFile['listOtherFiles'] as $_listOtherFiles) {
                            $offline_ho_so['otherFile'][1]['arr_file'][] = [
                                'fileName' => $_listOtherFiles['fileName'],
                                'fileId' => $_listOtherFiles['fileId']
                            ];
                        }
                    }
                }
                if (!empty($detail['bidInvContractorOfflineDTO']['otherFileId'])) {
                    $offline_ho_so['otherFile1'][1]['arr_file'][] = [
                        'fileName' => $detail['bidInvContractorOfflineDTO']['otherFileName'],
                        'fileId' => $detail['bidInvContractorOfflineDTO']['otherFileId']
                    ];
                }
            }
        }

        $ho_so_html = '';
        $ho_so_download = '';
        $arr_code = array_unique($arr_code);
        $get_pdf = -99;
        if (!empty($ho_so_array)) {
            $get_pdf = 0;
            ksort($ho_so_array);
            foreach ($ho_so_array as $hoso_p) {
                ksort($hoso_p);
                foreach ($hoso_p as $key => $hoso) {
                    if ($hoso['pcode'] == '' || in_array($hoso['pcode'], $arr_code)) {
                        if ($key == -1) {
                            $ho_so_html .= '<div class="list-group-item list-group-item-light">' . $hoso['name'] . '</div>';
                        } else {
                            $file = '';
                            if (!empty($hoso['arr_file'])) {
                                foreach ($hoso['arr_file'] as $_file) {
                                    $file .= '<a href="#"><span class="is_fast ctx-khlcnt-pl">' . $_file['fileName'] . '</span></a>';
                                }
                            }
                            $ho_so_html .= '<div class="list-group-item"><a href="https://muasamcong.mpi.gov.vn/egp/contractorfe/viewer?formCode=' . $hoso['code'] . '&id=' . $data['id_msc'] . '" target="_blank" rel="noopener noreferrer nofollow"><span>' . $hoso['name'] . '</span></a>' . $file . '</div>';
                        }
                    }
                }
            }
            $ho_so_download = json_encode($ho_so_array, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        } elseif (!empty($offline_ho_so)) {
            foreach ($offline_ho_so as $type_file => $hoso_p) {
                if ($type_file == 'file') {
                    $ho_so_html .= '<div class="list-group-item list-group-item-light">Hồ sơ mời thầu</div>';
                } elseif ($type_file == 'otherFile') {
                    $ho_so_html .= '<div class="list-group-item list-group-item-light">Tệp đính kèm khác</div>';
                } elseif ($type_file == 'otherFile1') {
                    $ho_so_html .= '<div class="list-group-item list-group-item-light">Nội dung đính kèm khác</div>';
                } elseif ($type_file == 'briefFile') {
                    $ho_so_html .= '<div class="list-group-item list-group-item-light">Tóm tắt nội dung sửa đổi</div>';
                }
                foreach ($hoso_p as $hoso) {
                    $file = '';
                    if (!empty($hoso['arr_file'])) {
                        foreach ($hoso['arr_file'] as $_file) {
                            $file .= '<a href="#"><span class="is_fast ctx-khlcnt-pl">' . $_file['fileName'] . '</span></a>';
                        }
                    }
                    $ho_so_html .= '<div class="list-group-item">' . $file . '</div>';
                }
            }
            $ho_so_download = json_encode($offline_ho_so, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        }

        $row['ho_so'] = $ho_so_html;
        $row['ho_so_download'] = $ho_so_download;
        $row = nv_compound_unicode_recursion($row);

        //xoá bản ghi ở nv4_vi_bidding_download_new (nếu có)
        $db->query('DELETE FROM nv4_vi_bidding_download_new WHERE id = ' . $tbmt_id);

        //cập nhật bản ghi ở nv4_vi_bidding_detail
        $db->query('UPDATE `nv4_vi_bidding_detail` SET ho_so = ' . $db->quote($row['ho_so']) . ', ho_so_download = ' . $db->quote($row['ho_so_download']) . ' WHERE id = ' . $tbmt_id);

        //cập nhật bản ghi ở nv4_vi_bidding_row
        $db->query('UPDATE `nv4_vi_bidding_row` SET get_ho_so = ' . $row['get_ho_so'] . ' WHERE id = ' . $tbmt_id);
    }
}

$result->closeCursor();

//ghi lại file đánh dấu
file_put_contents($file_name, $tbmt_id . '_' . $max_id);

function get_hsmt ($_tbmt, $reload = 1)
{
    global $dbcr, $num_run_hsmt;
    ++$num_run_hsmt;

    $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-contractor-selection-v2/services/lcnt_tbmt_hsmt';

    $body = '{
        "id": "' . $_tbmt['id_msc'] . '",
        "processApply": "' . $_tbmt['processapply'] . '"
    }';
    $referer = 'https://muasamcong.mpi.gov.vn/web/guest/contractor-selection?p_p_id=egpportalcontractorselection_WAR_egpportalcontractorselection&p_p_lifecycle=0&p_p_state=normal&p_p_mode=view&_egpportalcontractorselection_WAR_egpportalcontractorselection_render=detailV2&id=' . $_tbmt['id'] . '&type=6&pno=' . $_tbmt['notifyno'] . '&subType=SUB_TBMT&processApply=' . $_tbmt['processapply'] . '&notifyId=undefined';
    $arr_agent = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:104.0) Gecko/20100101 Firefox/104.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/105.0.1343.42',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 9.0; SAMSUNG SM-F900U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.77 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ];
    if (MSC_TOKEN_USE) {
        $token = get_msc_token('hsmt_hh');
        if ($token) {
            $url = $url . '?token=' . $token['token'];
        } else {
            // Nếu hết token thì ngưng phiên bóc
            $dbcr->exec('UPDATE nv23_crawls_tbmt SET url_run = 0, uniqid = "", count_url=count_url-1 WHERE url_run = "' . NV_CURRENTTIME . '"');
            exit("\033[35mĐã hết token\033[0m\n");
        }
    }

    srand((float)microtime() * 10000000);
    $rand = array_rand($arr_agent);
    $agent = $arr_agent[$rand];
    $ch = curl_init();

    if (defined('USE_PROXY')) {
        $_proxy = $dbcr->query("SELECT * FROM `nv4_proxy` WHERE `status`=2 ORDER BY `lasttime` ASC LIMIT 1")
            ->fetch();
        if (isset($_proxy['proxy'])) {
            $dbcr->query("UPDATE `nv4_proxy` SET `lasttime` = " . time() . ", number_run=number_run+1 WHERE `id` = " . $_proxy['id']);
            echo $_proxy['proxy'] . "\n";
            curl_setopt($ch, CURLOPT_PROXY, $_proxy['proxy']);
            if (isset($_proxy['proxyuserpwd']) and !empty($_proxy['proxyuserpwd'])) {
                curl_setopt($ch, CURLOPT_PROXYUSERPWD, $_proxy['proxyuserpwd']);
            }
            curl_setopt($ch, CURLOPT_PROXYTYPE, $_proxy['proxytype']);
            curl_setopt($ch, CURLOPT_PROXYPORT, $_proxy['proxyport']);
        }
    }

    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
    curl_setopt($ch, CURLOPT_REFERER, $referer);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);

    $json = curl_exec($ch);
    curl_close($ch);
    $data = json_decode($json, true);
    if (MSC_TOKEN_USE) {
        if (is_numeric($data)) {
            // Xử lý khi token chết: Cập nhật cho nó usetime < 0 rồi cho chạy vòng mới để đổi proxy luôn
            update_token_die($token['id']);
        } else {
            update_token_success($token['id']);
        }
    }
    if (isset($data['bidoInvBiddingDTO']) || isset($data['bidInvContractorOfflineDTO'])) {
        return $data;
    }
    return [];
}

echo 'Update trong: ' . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";
die("Goodbye!!!\n");
