<?php

/**
 * @Project DAUTHAU.INFO
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2021 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Sat, 08 Feb 2014 06:33:39 GMT
 */

define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME) . '/../')));
define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME))));

require NV_ROOTDIR . '/mainfile.php';

$help = '
/**
 * <PERSON><PERSON><PERSON> thông tin chi tiết của tổ chức lưu về CSDL
 * Chạy trực tiếp hoặc thêm các tham số có thể thêm nhiều, mỗi tham số cách nhau space
 * --limit=2 => mỗi row 1 lần bóc, mặc định 1
 */
';
echo $help . "\n";

$channelslack_file = 'canhan_detail_error.txt';

// Lấy hết tỉnh
$query_province = $db->query("SELECT id, title, alias FROM nv4_vi_location_province");
$data_province = $query_province->fetchAll();
foreach ($data_province as $key => $value){
    $data_province[$value['alias']] = $value['id'];
}

$query_url = $dbcr->query('SELECT * FROM nv4_canhan_url WHERE trang_thai = 0 ORDER BY id_url ASC LIMIT ' . intval($request_mode->get('limit', 1)));
// $query_url = $dbcr->query('SELECT * FROM nv4_canhan_url WHERE id_url=271637 ORDER BY id_url ASC LIMIT ' . intval($request_mode->get('limit', 1)));
while ($result_data = $query_url->fetch()) {
    // Cập nhật ngay trạng thái đang bóc để tiến trình khác không bóc đè
    $dbcr->query("UPDATE nv4_canhan_url SET trang_thai=-1, thoi_gian_boc = " . NV_CURRENTTIME . " WHERE id=" . $result_data['id']);

    $status = getdata($result_data, 1);
    $dbcr->query("UPDATE nv4_canhan_url SET trang_thai=" . $status . " WHERE id=" . $result_data['id']);
    echo "Trạng thái trả về: " . $status . "\n\n";
}

die("Kết thúc\n");

/**
 * @param array $result_data
 * @param boolean $reload
 * @param array $data_province
 * @return boolean|number
 */
function getdata($result_data, $reload)
{
    global $dbcr, $config_proxy, $db, $request_mode, $channelslack_file, $data_province;

    $id_url = $result_data['id_url'];
    echo "Lấy thông tin tổ chức: " . $id_url . "\n";
    $body_post = [
        'Id' => $id_url,
        'is_FE' => true,
    ];
    $body_post = json_encode($body_post);
    $http_header = [
        'content-type: application/json;charset=UTF-8'
    ];
    $url = $result_data['url_boc_tin'];

    echo "Proxy get...\n";
    $data = geturlhtml('https://nangluchdxd.gov.vn/api/Canhan/GetDetail', false, 'nangluchdxd', 'post', $body_post, $http_header);

    // Đọc dữ liệu html
    if (empty($data)) {
        echo "Không đọc được trang để lấy XML\n";
        if ($reload) {
            echo "Đang thử lại lần nữa\n";
            sleep($config_proxy['sleep']);
            return getdata($result_data, 0);
        }
        notifySlack($channelslack_file, 'Không đọc được trang để lấy HTML', $url);
        return 0;
    }

    /**
     * Xác định thông tin chung của cá nhân
     */
    $array_canhan = [
        'ho_va_ten' => '',
        'cccd' => '',
        'ngay_cap_cccd' => '',
        'noi_cap_cccd' => '',
        'cmnd' => '',
        'ngay_cap_cmnd' => '',
        'noi_cap_cmnd' => '',
        'co_so_dao_tao' => '',
        'dia_chi' => '',
        'he_dao_tao' => '',
        'tu_khoa_ca_nhan' => '',
        'ma_chung_chi' => '',
        'ngay_sinh' => 0,
        'quoc_tich' => '',
        'so_chung_chi' => '',
        'trinh_do_chuyen_mon' => '',
        'thoi_gian_boc' => 0,
        'so_lan_boc_tin' => 1,
    ];
    $data_canhan = json_decode($data, true);
    if (!is_array($data_canhan)) {
        // echo 'Không phân tách được JSON';
        notifySlack($channelslack_file, 'Không phân tách được JSON', $url);
        return 0;
    }
    if (empty($data_canhan['Value'])) {
        echo 'Lỗi URL';
        return -2;
    }
    $array_date_process = [
        'Ngaycap_CMND',
        'NgaycapCCCD',
        'Ngaysinh'
    ];
    foreach ($array_date_process as $key) {
        if (!empty($data_canhan['Value'][$key])) {
            if ($key == 'Ngaycap_CMND' and in_array($id_url, [
                79895
            ])) {
                $data_canhan['Value'][$key] = 0;
                continue;
            }

            // Các case không hợp chuẩn đầu vào
            $data_canhan['Value'][$key] = preg_replace('/^[0-9]+[\s]+\(/iu', '', $data_canhan['Value'][$key]);
            $data_canhan['Value'][$key] = preg_replace('/^Cấp ngày[\s]+/iu', '', $data_canhan['Value'][$key]);
            $data_canhan['Value'][$key] = preg_replace('/^\_+/iu', '', $data_canhan['Value'][$key]);
            $data_canhan['Value'][$key] = preg_replace('/Công an(.*?)$/iu', '', $data_canhan['Value'][$key]);
            $data_canhan['Value'][$key] = str_replace('(check lại)', '', $data_canhan['Value'][$key]);
            $data_canhan['Value'][$key] = str_replace('\'', '', $data_canhan['Value'][$key]);
            $data_canhan['Value'][$key] = str_replace('20o7', '2007', $data_canhan['Value'][$key]);
            $data_canhan['Value'][$key] = rtrim($data_canhan['Value'][$key], ')');
            $data_canhan['Value'][$key] = ltrim($data_canhan['Value'][$key], ' :');
            $data_canhan['Value'][$key] = trim($data_canhan['Value'][$key], '.\'`');
            $data_canhan['Value'][$key] = trim($data_canhan['Value'][$key]);
            $data_canhan['Value'][$key] = preg_replace('/^l7/', '17', $data_canhan['Value'][$key]);
            $data_canhan['Value'][$key] = preg_replace('/\/[\s]+/', '/', $data_canhan['Value'][$key]);
            $data_canhan['Value'][$key] = preg_replace('/[\s]+\//', '/', $data_canhan['Value'][$key]);

            if (preg_match('/^([0-9]{1,3})[\/\-\.\+]+([0-9]{1,3})[\/\-\.\+]+([0-9]{2,5})$/', $data_canhan['Value'][$key], $m)) {
                /*
                 * dd/mm/yyyy
                 * dd từ 1 đến 3 chữ
                 * mm từ 1 đến 3 chữ
                 * yyyy từ 2 đến 5 chữ
                 */

                // Chuẩn hóa ngày
                $m[1] = intval($m[1]);
                if ($m[1] > 99) {
                    switch ($m[1]) {
                        case 117:
                            $m[1] = 17;
                            break;

                        case 211:
                            $m[1] = 21;
                            break;

                        case 125:
                            $m[1] = 15;
                            break;

                        case 223:
                            $m[1] = 23;
                            break;

                        case 224:
                            $m[1] = 24;
                            break;

                        default:
                            // echo 'Không chuẩn hóa ngày ' . $key . ' ' . $data_canhan['Value'][$key];
                            notifySlack($channelslack_file, 'Không chuẩn hóa ngày ' . $key . ' ' . $data_canhan['Value'][$key], $url);
                            return 0;
                    }
                } elseif ($m[1] > 31) {
                    // Ngày 31 => 99 thì bỏ qua
                    $data_canhan['Value'][$key] = 0;
                    continue;
                }

                // Chuẩn hóa tháng
                $m[2] = intval($m[2]);
                if ($m[2] > 99) {
                    switch ($m[2]) {
                        case 112:
                            $m[2] = 12;
                            break;

                        case 121:
                            $m[2] = 12;
                            break;

                        case 152:
                            $m[2] = 12;
                            break;

                        default:
                            // echo 'Không chuẩn hóa tháng ' . $key . ' ' . $data_canhan['Value'][$key];
                            notifySlack($channelslack_file, 'Không chuẩn hóa tháng ' . $key . ' ' . $data_canhan['Value'][$key], $url);
                            return 0;
                    }
                } elseif ($m[2] > 12) {
                    // Tháng 13 => 99 thì bỏ qua
                    $data_canhan['Value'][$key] = 0;
                    continue;
                }

                // Chuẩn hóa năm
                $m[3] = change_year($m[3]);
                $m[3] = intval($m[3]);
                $array_fix_year = [
                    20163 => 2016,
                    214 => 2014,
                    205 => 2015,
                    12008 => 2008,
                    1081 => 1981,
                    20174 => 2017,
                    22010 => 2010,
                    20118 => 2018,
                    20187 => 2018,
                    20111 => 2011,
                    12010 => 2010,
                    20146 => 2014,
                    963 => 1963,
                    2081 => 1981,
                    1016 => 2016,
                    971 => 1971,
                    1019 => 2019,
                    20131 => 2013,
                    213 => 2013,
                    210 => 2010,
                    220 => 2020,
                    20210 => 2021,
                    12016 => 2016,
                    962 => 1962,
                    2974 => 1974,
                    2969 => 1969,
                    2989 => 1989,
                    2120 => 2021,
                    987 => 1987,
                    20115 => 2015,
                    20120 => 2012,
                    215 => 2015,
                    21015 => 2015,
                    977 => 1977,
                    2983 => 1983, 983 => 1983,
                    2121 => 2021,
                    2323 => 2023,
                    2328 => 2028,
                    221 => 2021,
                    20211 => 2021,
                    988 => 1988,
                    20217 => 2017,
                    2984 => 1984
                ];
                if (isset($array_fix_year[$m[3]])) {
                    $m[3] = $array_fix_year[$m[3]];
                }
                if ($m[3] >= 10000) {
                    // Có 5 chữ số
                    if (preg_match('/^200([0-9]{2})$/', $m[3], $n)) {
                        $m[3] = intval('20' . $n[1]);
                    } else {
                        // echo 'Không xử lý được năm ' . $key . ' có dạng ' . $data_canhan['Value'][$key];
                        notifySlack($channelslack_file, 'Không xử lý được năm ' . $key . ' có dạng ' . $data_canhan['Value'][$key], $url);
                        return 0;
                    }
                } elseif ($m[3] < 10) {
                    // Nhỏ hơn 10 tức là 20xx
                    $m[3] = intval('200' . $m[3]);
                } elseif ($m[3] < 30 and $m[3] >= 10) {
                    // Từ 10 - 29 cũng xem là 20xx
                    $m[3] = intval('20' . $m[3]);
                } elseif ($m[3] > 50 and $m[3] < 99) {
                    // Từ 51 - 98 là 19xx
                    $m[3] = intval('19' . $m[3]);
                } elseif ($m[3] == 209) {
                    // Năm cố định 2019
                    $m[3] = 2019;
                } elseif ($m[3] == 201) {
                    // Năm cố định 2010
                    $m[3] = 2010;
                } elseif ($m[3] == 202) {
                    // Năm cố định 2020
                    $m[3] = 2020;
                } elseif ($m[3] < 1000) {
                    // echo 'Không xử lý được năm ' . $key . ' ' . $data_canhan['Value'][$key];
                    notifySlack($channelslack_file, 'Không xử lý được năm ' . $key . ' ' . $data_canhan['Value'][$key], $url);
                    return 0;
                }
                $m[3] = change_year(strval($m[3]));

                // Không có ngày cấp CCCD nhỏ như thế
                if ($key == 'NgaycapCCCD' and $m[3] < 1970) {
                    $data_canhan['Value'][$key] = 0;
                    continue;
                }

                $data_canhan['Value'][$key] = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
            } elseif (preg_match('/^([0-9]{3})[\/\-\.\+]+([0-9]{4})$/', $data_canhan['Value'][$key], $m)) {
                /*
                 * ddm/yyyy hoặc dmm/yyyy
                 */
                $da = intval(substr($m[1], 0, 2));
                $mo = intval(substr($m[1], 2, 1));
                if ($da < 1 or $da > 31 or $mo < 1) {
                    $da = intval(substr($m[1], 0, 1));
                    $mo = intval(substr($m[1], 1, 2));
                }
                if ($da < 1 or $da > 31 or $mo < 1) {
                    // echo 'Không xử lý được ddm/yyyy hoặc dmm/yyyy ' . $key . ' ' . $data_canhan['Value'][$key];
                    notifySlack($channelslack_file, 'Không xử lý được ddm/yyyy hoặc dmm/yyyy ' . $key . ' ' . $data_canhan['Value'][$key], $url);
                    return 0;
                }
                $data_canhan['Value'][$key] = mktime(0, 0, 0, $mo, $da, change_year($m[2]));
            } elseif (preg_match('/^([0-9]{4})[\/\-\.\+]+([0-9]{4})$/', $data_canhan['Value'][$key], $m)) {
                /*
                 * ddmm/yyyy
                 */
                $da = intval(substr($m[1], 0, 2));
                $mo = intval(substr($m[1], 2, 2));
                $data_canhan['Value'][$key] = mktime(0, 0, 0, $mo, $da, change_year($m[2]));
            } elseif (preg_match('/^([0-9]{1,2})[\/\-\.\+]+([0-9]{6})$/', $data_canhan['Value'][$key], $m)) {
                /*
                 * dd/mmyyyy
                 */
                $mo = intval(substr($m[2], 0, 2));
                $ye = intval(substr($m[2], 2, 4));
                $data_canhan['Value'][$key] = mktime(0, 0, 0, $mo, $m[1], change_year($ye));
            } elseif (preg_match('/^([0-9]{1,2})[\/\-\.\+]+([0-9]{5})$/', $data_canhan['Value'][$key], $m)) {
                /*
                 * dd/myyyy
                 */
                $mo = intval(substr($m[2], 0, 1));
                $ye = intval(substr($m[2], 1, 4));
                $data_canhan['Value'][$key] = mktime(0, 0, 0, $mo, $m[1], change_year($ye));
            } elseif (preg_match('/n\/a/i', $data_canhan['Value'][$key], $m)) {
                // Không có ngày
                $data_canhan['Value'][$key] = 0;
            } elseif (preg_match('/^([0-9]{2})[\/\-\.\+]+([0-9]{4})$/', $data_canhan['Value'][$key], $m)) {
                /*
                 * Chỉ có tháng/năm không biết ngày
                 */
                $data_canhan['Value'][$key] = 0;
            } elseif (preg_match('/^([0-9]{2})[\/\-\.\+]+([0-9]{2})$/', $data_canhan['Value'][$key], $m)) {
                /*
                 * Chỉ có ngày/tháng không biết năm
                 */
                $data_canhan['Value'][$key] = 0;
            } elseif (preg_match('/^([0-9]{4})$/', $data_canhan['Value'][$key], $m)) {
                /*
                 * Chỉ có năm
                 */
                $data_canhan['Value'][$key] = 0;
            } elseif (preg_match('/^[0-9]{5}$/', $data_canhan['Value'][$key], $m)) {
                // Ngày toàn số, xem như không có ngày
                // $data_canhan['Value'][$key] = 0;
                $data_canhan['Value'][$key] = ($data_canhan['Value'][$key] - 25569) * 86400;
            } elseif (preg_match('/^[0-9]{10}$/', $data_canhan['Value'][$key], $m)) {
                // Ví dụ 2210412010
                $hh = intval(substr($m[1], 0, 2));
                $mm = intval(substr($m[1], 2, 2));
                if ($hh > 23 or $mm > 59) {
                    // echo 'Không xử lý được dạng 2210412010 ' . $key . ' FULL ngày ' . $data_canhan['Value'][$key];
                    notifySlack($channelslack_file, 'Không xử lý được dạng 2210412010 ' . $key . ' FULL ngày ' . $data_canhan['Value'][$key], $url);
                    return 0;
                }

                $dd = intval(substr($m[1], 4, 1));
                $mo = intval(substr($m[1], 5, 1));
                $ye = intval(substr($m[1], 6, 4));

                $data_canhan['Value'][$key] = mktime($hh, $mm, 0, $mo, $dd, change_year($ye));
            } elseif (preg_match('/^[0-9]{8}$/', $data_canhan['Value'][$key], $m)) {
                // Ví dụ 07102005 ddmmyyyy
                $dd = intval(substr($m[1], 0, 2));
                $mo = intval(substr($m[1], 2, 2));
                $ye = intval(substr($m[1], 4, 4));

                $data_canhan['Value'][$key] = mktime(0, 0, 0, $mo, $dd, change_year($ye));
            } elseif (
                preg_match('/^[0-9]{9}$/', $data_canhan['Value'][$key]) or
                preg_match('/^M[0-9]+$/i', $data_canhan['Value'][$key])
            ) {
                // Ngày 9 chữ số, không lấy được quy luật
                $data_canhan['Value'][$key] = 0;
            } elseif (preg_match('/^(không|CA|REF)/iu', $data_canhan['Value'][$key], $m)) {
                // Ngày cấp có chữ
                $data_canhan['Value'][$key] = 0;
            } elseif ($key == 'NgaycapCCCD' || $key == 'Ngaycap_CMND') {
                // Ngày cấp có chữ
                $data_canhan['Value'][$key] = 0;
            } else {
                // echo 'Không xử lý được ' . $key . ' có dạng ' . $data_canhan['Value'][$key];
                notifySlack($channelslack_file, 'Không xử lý được ' . $key . ' có dạng ' . $data_canhan['Value'][$key], $url);
                return 0;
            }
        }
    }
    $array_canhan['id_url'] = $id_url;
    $array_canhan['ho_va_ten'] = $data_canhan['Value']['Hovaten'];
    $array_canhan['cccd'] = preg_replace('/[^\d]/', '', $data_canhan['Value']['CCCD'] ?? '');
    $array_canhan['ngay_cap_cccd'] = $data_canhan['Value']['NgaycapCCCD'];
    $array_canhan['noi_cap_cccd'] = $data_canhan['Value']['NoicapCCCD'];
    $array_canhan['cmnd'] = preg_replace('/[^\d]/', '', $data_canhan['Value']['CMND'] ?? '');
    $array_canhan['ngay_cap_cmnd'] = $data_canhan['Value']['Ngaycap_CMND'];
    $array_canhan['noi_cap_cmnd'] = $data_canhan['Value']['Noicap_CMND'];
    $array_canhan['co_so_dao_tao'] = $data_canhan['Value']['Coso_daotao'];
    $array_canhan['dia_chi'] = $data_canhan['Value']['Diachi'];
    $array_canhan['he_dao_tao'] = $data_canhan['Value']['Hedaotao'];
    $array_canhan['tu_khoa_ca_nhan'] = mb_substr($data_canhan['Value']['Keyword'], 0, 300);
    $array_canhan['ma_chung_chi'] = $data_canhan['Value']['Machungchi'];
    $array_canhan['ngay_sinh'] = $data_canhan['Value']['Ngaysinh'];
    $array_canhan['quoc_tich'] = $data_canhan['Value']['Quoctich'];
    $array_canhan['so_chung_chi'] = $data_canhan['Value']['Sochungchi'];
    $array_canhan['trinh_do_chuyen_mon'] = $data_canhan['Value']['Trinhdo_chuyenmon'];
    $array_canhan['thoi_gian_boc'] = NV_CURRENTTIME;

    // Không để các trường dữ liệu có NULL
    $array_not_null_string = [
        'ho_va_ten',
        'cccd',
        'noi_cap_cccd',
        'cmnd',
        'noi_cap_cmnd',
        'co_so_dao_tao',
        'dia_chi',
        'he_dao_tao',
        'tu_khoa_ca_nhan',
        'ma_chung_chi',
        'quoc_tich',
        'so_chung_chi',
        'trinh_do_chuyen_mon',
    ];
    $array_not_null_int = [
        'ngay_cap_cccd',
        'ngay_cap_cmnd',
        'ngay_sinh',
    ];
    foreach ($array_not_null_string as $key) {
        if (empty($array_canhan[$key])) {
            $array_canhan[$key] = '';
        }
    }
    foreach ($array_not_null_int as $key) {
        if (empty($array_canhan[$key])) {
            $array_canhan[$key] = 0;
        }
    }

    $array_chungchi = [];
    $cc_string_keys = [
        'Sochungchi',
        'MaLinhvuc',
        'tenlinhvuc',
        'SoQD',
        'Hang',
    ];

    foreach ($data_canhan['Value']['linhvucs'] as $value) {
        if(!empty($value['FileQuyetDinhPath'])) {
            $value['FileQuyetDinhPath'] = ltrim($value['FileQuyetDinhPath'], '~');
        }
        if (!empty($value['Ngayhieuluc']) and preg_match('/^([0-9]{4})\-([0-9]{1,2})\-([0-9]{1,2})([^`]*?)$/', $value['Ngayhieuluc'], $m)) {
            $m[1] = $array_fix_year[$m[1]] ?? $m[1];
            $value['Ngayhieuluc'] = mktime(0, 0, 0, $m[2], $m[3], change_year($m[1]));
        }
        if (!empty($value['Ngayhethan']) and preg_match('/^([0-9]{4})\-([0-9]{1,2})\-([0-9]{1,2})([^`]*?)$/', $value['Ngayhethan'], $m)) {
            $m[1] = $array_fix_year[$m[1]] ?? $m[1];
            $value['Ngayhethan'] = mktime(0, 0, 0, $m[2], $m[3], change_year($m[1]));
        }
        $value['tenlinhvuc'] = (!empty($value['tenlinhvuc'])) ? $value['tenlinhvuc'] : $value['tenLinhvuc'];

        foreach ($cc_string_keys as $key) {
            if (empty($value[$key])) {
                $value[$key] = '';
            }
        }

        $array_chungchi[] = [
            'so_chung_chi' => $value['Sochungchi'],
            'ma_linh_vuc' => $value['MaLinhvuc'],
            'ten_linh_vuc' => $value['tenlinhvuc'],
            'so_quyet_dinh' => $value['SoQD'],
            'file_quyet_dinh' => $value['FileQuyetDinhPath'] ? $value['FileQuyetDinhPath'] : '',
            'hang' => $value['Hang'],
            'ngay_hieu_luc' => $value['Ngayhieuluc'],
            'ngay_het_han' => $value['Ngayhethan'],
        ];
    }

    /**
     * Bước lưu vào CSDL
     */
    $content_id = 0;
    if (!empty($id_url) and !empty($array_canhan['ho_va_ten'])) {
        $query_check_ca_nhan_exist = $db->query("SELECT id FROM nv4_canhan WHERE id_url = " . $id_url);
        $data_ca_nhan_exist = $query_check_ca_nhan_exist->fetch();
        if (empty($data_ca_nhan_exist)) {
            try {
                $stmt = $db->prepare('INSERT INTO nv4_canhan (
                    id_url, ho_va_ten, cccd, ngay_cap_cccd, noi_cap_cccd, cmnd, ngay_cap_cmnd, noi_cap_cmnd, co_so_dao_tao,
                    dia_chi, he_dao_tao, tu_khoa_ca_nhan, ma_chung_chi, ngay_sinh, quoc_tich, so_chung_chi, trinh_do_chuyen_mon, thoi_gian_boc, so_lan_boc_tin
                ) VALUES (
                    :id_url, :ho_va_ten, :cccd, :ngay_cap_cccd, :noi_cap_cccd, :cmnd, :ngay_cap_cmnd, :noi_cap_cmnd,
                    :co_so_dao_tao, :dia_chi, :he_dao_tao, :tu_khoa_ca_nhan, :ma_chung_chi, :ngay_sinh, :quoc_tich, :so_chung_chi,
                    :trinh_do_chuyen_mon, :thoi_gian_boc, :so_lan_boc_tin
                )');
                $stmt->bindParam(':id_url', $id_url, PDO::PARAM_INT);
                $stmt->bindParam(':ho_va_ten', $array_canhan['ho_va_ten'], PDO::PARAM_STR);
                $stmt->bindParam(':cccd', $array_canhan['cccd'], PDO::PARAM_STR);
                $stmt->bindParam(':ngay_cap_cccd', $array_canhan['ngay_cap_cccd'], PDO::PARAM_INT);
                $stmt->bindParam(':noi_cap_cccd', $array_canhan['noi_cap_cccd'], PDO::PARAM_STR);
                $stmt->bindParam(':cmnd', $array_canhan['cmnd'], PDO::PARAM_STR);
                $stmt->bindParam(':ngay_cap_cmnd', $array_canhan['ngay_cap_cmnd'], PDO::PARAM_INT);
                $stmt->bindParam(':noi_cap_cmnd', $array_canhan['noi_cap_cmnd'], PDO::PARAM_STR);
                $stmt->bindParam(':co_so_dao_tao', $array_canhan['co_so_dao_tao'], PDO::PARAM_STR);
                $stmt->bindParam(':dia_chi', $array_canhan['dia_chi'], PDO::PARAM_STR);
                $stmt->bindParam(':he_dao_tao', $array_canhan['he_dao_tao'], PDO::PARAM_STR);
                $stmt->bindParam(':tu_khoa_ca_nhan', $array_canhan['tu_khoa_ca_nhan'], PDO::PARAM_STR);
                $stmt->bindParam(':ma_chung_chi', $array_canhan['ma_chung_chi'], PDO::PARAM_STR);
                $stmt->bindParam(':ngay_sinh', $array_canhan['ngay_sinh'], PDO::PARAM_INT);
                $stmt->bindParam(':quoc_tich', $array_canhan['quoc_tich'], PDO::PARAM_STR);
                $stmt->bindParam(':so_chung_chi', $array_canhan['so_chung_chi'], PDO::PARAM_STR);
                $stmt->bindParam(':trinh_do_chuyen_mon', $array_canhan['trinh_do_chuyen_mon'], PDO::PARAM_STR);
                $stmt->bindParam(':thoi_gian_boc', $array_canhan['thoi_gian_boc'], PDO::PARAM_INT);
                $stmt->bindParam(':so_lan_boc_tin', $array_canhan['so_lan_boc_tin'], PDO::PARAM_INT);
                $stmt->execute();
                $content_id = $db->lastInsertId();
            } catch (Exception $e) {
                print_r($e, true);
                notifySlack($channelslack_file, "```\n" . print_r($e->getMessage(), true) . "\n```", $url);
                return 0;
            }
        } else {
            try {
                $stmt = $db->prepare('UPDATE nv4_canhan SET
                    ho_va_ten = :ho_va_ten, cccd = :cccd, ngay_cap_cccd = :ngay_cap_cccd, noi_cap_cccd = :noi_cap_cccd,
                    cmnd = :cmnd, ngay_cap_cmnd = :ngay_cap_cmnd, noi_cap_cmnd= :noi_cap_cmnd, co_so_dao_tao = :co_so_dao_tao, dia_chi = :dia_chi,
                    he_dao_tao = :he_dao_tao, tu_khoa_ca_nhan = :tu_khoa_ca_nhan, ma_chung_chi = :ma_chung_chi, ngay_sinh=:ngay_sinh,
                    quoc_tich = :quoc_tich, so_chung_chi = :so_chung_chi,  trinh_do_chuyen_mon = :trinh_do_chuyen_mon, thoi_gian_boc = :thoi_gian_boc, so_lan_boc_tin = so_lan_boc_tin + 1
                WHERE id = :id');
                $stmt->bindParam(':ho_va_ten', $array_canhan['ho_va_ten'], PDO::PARAM_STR);
                $stmt->bindParam(':cccd', $array_canhan['cccd'], PDO::PARAM_STR);
                $stmt->bindParam(':ngay_cap_cccd', $array_canhan['ngay_cap_cccd'], PDO::PARAM_INT);
                $stmt->bindParam(':noi_cap_cccd', $array_canhan['noi_cap_cccd'], PDO::PARAM_STR);
                $stmt->bindParam(':cmnd', $array_canhan['cmnd'], PDO::PARAM_STR);
                $stmt->bindParam(':ngay_cap_cmnd', $array_canhan['ngay_cap_cmnd'], PDO::PARAM_INT);
                $stmt->bindParam(':noi_cap_cmnd', $array_canhan['noi_cap_cmnd'], PDO::PARAM_STR);
                $stmt->bindParam(':co_so_dao_tao', $array_canhan['co_so_dao_tao'], PDO::PARAM_STR);
                $stmt->bindParam(':dia_chi', $array_canhan['dia_chi'], PDO::PARAM_STR);
                $stmt->bindParam(':he_dao_tao', $array_canhan['he_dao_tao'], PDO::PARAM_STR);
                $stmt->bindParam(':tu_khoa_ca_nhan', $array_canhan['tu_khoa_ca_nhan'], PDO::PARAM_STR);
                $stmt->bindParam(':ma_chung_chi', $array_canhan['ma_chung_chi'], PDO::PARAM_STR);
                $stmt->bindParam(':ngay_sinh', $array_canhan['ngay_sinh'], PDO::PARAM_INT);
                $stmt->bindParam(':quoc_tich', $array_canhan['quoc_tich'], PDO::PARAM_STR);
                $stmt->bindParam(':so_chung_chi', $array_canhan['so_chung_chi'], PDO::PARAM_STR);
                $stmt->bindParam(':trinh_do_chuyen_mon', $array_canhan['trinh_do_chuyen_mon'], PDO::PARAM_STR);
                $stmt->bindParam(':thoi_gian_boc', $array_canhan['thoi_gian_boc'], PDO::PARAM_INT);
                $stmt->bindParam(':id', $data_ca_nhan_exist['id'], PDO::PARAM_INT);
                $stmt->execute();
                $content_id = $data_ca_nhan_exist['id'];
            } catch (Exception $e) {
                print_r($e, true);
                notifySlack($channelslack_file, "```\n" . print_r($e->getMessage(), true) . "\n```", $url);
                return 0;
            }
        }
    } else {
        // echo 'Thiếu thông tin bắt buộc id url, họ tên';
        notifySlack($channelslack_file, 'Thiếu thông tin bắt buộc id url, họ tên', $url);
        return 0;
    }

    if (empty($content_id)) {
        // echo 'Không rõ nguyên nhân không nhận được ID của row xử lý';
        notifySlack($channelslack_file, 'Không rõ nguyên nhân không nhận được ID của row xử lý', $url);
        return 0;
    }

    /**
     * Lưu chứng chỉ vào CSDL
     */

    // Xóa hết data chứng chỉ hiện tại để lưu mới
    $sql = "DELETE FROM nv4_canhan_chungchi WHERE ca_nhan_id = " . $content_id;
    $db->query($sql);

    foreach ($array_chungchi as $row) {
        $stmt = $db->prepare('INSERT INTO nv4_canhan_chungchi (
            so_chung_chi, ma_linh_vuc, ten_linh_vuc, so_quyet_dinh,
            file_quyet_dinh, hang, ngay_hieu_luc, ngay_het_han, ca_nhan_id
        ) VALUES (
            :so_chung_chi, :ma_linh_vuc, :ten_linh_vuc, :so_quyet_dinh, :file_quyet_dinh,
            :hang, :ngay_hieu_luc, :ngay_het_han, :ca_nhan_id
        )');
        try {
            $stmt->bindParam(':so_chung_chi', $row['so_chung_chi'], PDO::PARAM_STR);
            $stmt->bindParam(':ma_linh_vuc', $row['ma_linh_vuc'], PDO::PARAM_STR);
            $stmt->bindParam(':ten_linh_vuc', $row['ten_linh_vuc'], PDO::PARAM_STR);
            $stmt->bindParam(':so_quyet_dinh', $row['so_quyet_dinh'], PDO::PARAM_STR);
            $stmt->bindParam(':file_quyet_dinh', $row['file_quyet_dinh'], PDO::PARAM_STR);
            $stmt->bindParam(':hang', $row['hang'], PDO::PARAM_STR);
            $stmt->bindParam(':ngay_hieu_luc', $row['ngay_hieu_luc'], PDO::PARAM_INT);
            $stmt->bindParam(':ngay_het_han', $row['ngay_het_han'], PDO::PARAM_INT);
            $stmt->bindParam(':ca_nhan_id', $content_id, PDO::PARAM_INT);
            $stmt->execute();
        } catch (Exception $e) {
            print_r($e);
            notifySlack($channelslack_file, "```\n" . print_r($e->getMessage(), true) . "\n```", $url);
            return 0;
        }
    }
    return 1;
}
