#!/bin/bash

SOURCE="${BASH_SOURCE[0]}"
while [ -h "$SOURCE" ]; do
  TARGET="$(readlink "$SOURCE")"
  if [[ $TARGET == /* ]]; then
    SOURCE="$TARGET"
  else
    DIR="$( dirname "$SOURCE" )"
    SOURCE="$DIR/$TARGET"
  fi
done
DIR="$( cd -P "$( dirname "$SOURCE" )" >/dev/null 2>&1 && pwd )"
cd "$DIR/"
DIR_PATH=$PWD

echo '---------------- <PERSON><PERSON><PERSON> đầu chạy tool ----------------'

LOG_FILE="${DIR_PATH}/update_address_ycbg.log"

echo "Đang xử lý cho ngôn ngữ: Tiếng Việt (vi)"
while : ; do
    php "$DIR_PATH/update_address_ycbg.php" --site_lang=vi | tee -a "$LOG_FILE"
    code=${PIPESTATUS[0]}
    if [[ $code -eq 1 ]]; then
        echo "Đã chạy xong cho Tiếng Việt (vi)"
        break
    fi
    sleep 1
done

echo "Đang xử lý cho ngôn ngữ: Tiếng Anh (en)"
while : ; do
    php "$DIR_PATH/update_address_ycbg.php" --site_lang=en | tee -a "$LOG_FILE"
    code=${PIPESTATUS[0]}
    if [[ $code -eq 1 ]]; then
        echo "Đã chạy xong cho Tiếng Anh (en)"
        break
    fi
    sleep 1
done

echo "Đã hoàn thành xử lý cho cả Tiếng Việt và Tiếng Anh"
