<?php

// Tìm thông tin bên mời thầu do hàm get_solicitor_id sinh ra
// https://muasamcong.mpi.gov.vn/web/guest/approved-contractors-list
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));

require NV_ROOTDIR . '/mainfile.php';

// INSERT INTO `nv22_benmoithau_search` ( id, `ma_dinh_danh`, `ten_don_vi_day_du`) SELECT id, `org_code`, `title` FROM dauthau_2018.nv4_vi_bidding_solicitor WHERE `org_code` != '' AND `org_code` NOT IN (SELECT `ma_dinh_danh` FROM dauthau_crawls.nv22_benmoithau_url);

do {
    $query = $dbcr->query("SELECT * FROM `nv22_benmoithau_search` WHERE `url_run`=0 ORDER BY `id` ASC LIMIT 10"); // 158151
    $last_id = 0;
    while ($row = $query->fetch()) {
        $orgcode = $row['ma_dinh_danh'];
        $last_id = $row['id'];
        echo "Search: " . $orgcode . "\n";
        $q = $dbcr->query("SELECT effroledate FROM `nv22_benmoithau` WHERE orgcode=" . $dbcr->quote($orgcode));
        $effroledate = $q->fetchColumn();
        if (!empty($effroledate)) {
            $dbcr->query("UPDATE nv22_benmoithau_search SET url_run='1' WHERE id=" . $row['id']);
            echo $orgcode . "\t" . $effroledate . "\n";
        } else {
            $dbcr->query("UPDATE nv22_benmoithau_search SET url_run='-" . NV_CURRENTTIME . "', count_url=count_url+1 WHERE id=" . $row['id']);
            $num_run = 0;
            $data = geturlpage($orgcode, 1);
            if (isset($data['ebidOrgInfos']['totalElements'])) {
                if ($data['ebidOrgInfos']['totalElements'] > 0) {
                    $dbcr->query("UPDATE nv22_benmoithau_search SET url_run='" . NV_CURRENTTIME . "' WHERE id=" . $row['id']);
                    $data = $data['ebidOrgInfos']['content'];

                    $prepared = $dbcr->prepare("INSERT INTO `nv22_benmoithau` (`orgcode`, `startpendingdate`, `status`, `effroledate`, `detail1`, recemail) VALUES (:orgcode, :startpendingdate, :status, :effroledate, :detail1, :recemail)");
                    foreach ($data as $row) {
                        try {
                            $detail1 = $row;

                            $_effroledate = sizeof($row['effRoleDate']) > 4 ? $row['effRoleDate'][2] . '/' . $row['effRoleDate'][1] . '/' . $row['effRoleDate'][0] : '';
                            if (isset($row['startPendingDate']) and is_string($row['startPendingDate'])) {
                                $row['startPendingDate'] = trim($row['startPendingDate']);
                            } elseif (isset($row['startPendingDate']) and is_array($row['startPendingDate']) and sizeof($row['startPendingDate']) > 4) {
                                $row['startPendingDate'] = $row['startPendingDate'][2] . '/' . $row['startPendingDate'][1] . '/' . $row['startPendingDate'][0];
                            } else {
                                $row['startPendingDate'] = '';
                            }

                            $prepared->bindParam(':orgcode', $row['orgCode'], PDO::PARAM_STR);
                            $prepared->bindParam(':startpendingdate', $row['startPendingDate'], PDO::PARAM_STR);
                            $prepared->bindParam(':status', $row['status'], PDO::PARAM_STR);
                            $prepared->bindParam(':effroledate', $_effroledate, PDO::PARAM_STR);
                            $prepared->bindValue(':detail1', json_encode($detail1, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT), PDO::PARAM_STR);
                            $prepared->bindParam(':recemail', $row['recEmail'], PDO::PARAM_STR);
                            $prepared->execute();
                            echo "News: " . $row['orgCode'] . " \n";
                        } catch (PDOException $e) {
                            if (!preg_match('/Integrity constraint violation\: ([0-9]+) Duplicate entry (.*) for key \'orgcode\'/', $e->getMessage())) {
                                file_put_contents(NV_ROOTDIR . '/crawls_2023/logs/benmoithau_search.log', date('Y-m-d H:i') . " \t " . $orgcode . ": " . $row['orgCode'] . " " . $e->getMessage() . "\n", FILE_APPEND);
                                print_r($row);
                                print_r($e);
                            }
                        } catch (Exception $e) {
                            file_put_contents(NV_ROOTDIR . '/crawls_2023/logs/benmoithau_search.log', date('Y-m-d H:i') . " \t " . $orgcode . ": " . $row['orgCode'] . " " . $e->getMessage() . "\n", FILE_APPEND);
                            print_r($row);
                            print_r($e);
                        }
                    }
                } else {
                    $dbcr->query("UPDATE nv22_benmoithau_search SET url_run='-1' WHERE id=" . $row['id']);
                }
            }
        }
    }
    $query->closeCursor();
} while ($last_id > 0);

function geturlpage($orgCode, $reload = 1)
{
    global $dbcr, $num_run;
    ++$num_run;

    $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-bid-solicitor-approved/services/um/lookup-orgInfo';
    $body = '{
      "pageSize": 20,
      "pageNumber": 0,
      "queryParams": {
        "roleType": {
          "equals": "BMT"
        },
        "orgName": {
          "contains": null
        },
        "orgCode": {
          "contains": null
        },
        "orgNameOrOrgCode": {
          "contains": "' . $orgCode . '"
        },
        "agencyName": {
          "in": null
        },
        "effRoleDate": {
          "greaterThanOrEqual": null,
          "lessThanOrEqual": null
        }
      }
    }';

    $referer = 'https://muasamcong.mpi.gov.vn/web/guest/bid-solicitor-approval';

    $arr_agent = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:104.0) Gecko/20100101 Firefox/104.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/105.0.1343.42',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 9.0; SAMSUNG SM-F900U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.77 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ];
    srand((float) microtime() * 10000000);
    $rand = array_rand($arr_agent);
    $agent = $arr_agent[$rand];

    $_proxy = $dbcr->query("SELECT * FROM `nv4_proxy` WHERE `status`=2 ORDER BY `lasttime` ASC LIMIT 1")->fetch();
    $ch = curl_init();
    if (isset($_proxy['proxy'])) {
        $dbcr->query("UPDATE `nv4_proxy` SET `lasttime` = " . time() . ", number_run=number_run+1 WHERE `id` = " . $_proxy['id']);
        echo $_proxy['proxy'] . "\n";
        curl_setopt($ch, CURLOPT_PROXY, $_proxy['proxy']);
        if (isset($_proxy['proxyuserpwd']) and !empty($_proxy['proxyuserpwd'])) {
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, $_proxy['proxyuserpwd']);
        }
        curl_setopt($ch, CURLOPT_PROXYTYPE, $_proxy['proxytype']);
        curl_setopt($ch, CURLOPT_PROXYPORT, $_proxy['proxyport']);
    }
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 150);
    curl_setopt($ch, CURLOPT_TIMEOUT, 150);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
    curl_setopt($ch, CURLOPT_REFERER, $referer);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json'
    ));

    $json = curl_exec($ch);
    curl_close($ch);

    $data = json_decode($json, true);
    if (isset($data['ebidOrgInfos']['content'])) {
        return $data;
    } elseif (isset($data['ebidOrgInfos']['totalElements'])) {
        return $data;
    } elseif ($reload and $num_run < 5) {
        print_r($data);
        return geturlpage($orgCode, 1);
    } elseif ($reload) {
        return geturlpage($orgCode, 0);
    }
    return [];
}