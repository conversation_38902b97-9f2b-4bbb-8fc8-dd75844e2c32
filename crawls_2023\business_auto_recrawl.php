<?php
define('NV_SYSTEM', true);

// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME))));
require NV_ROOTDIR . '/mainfile.php';

// Tool kiểm tra bóc lại thông tin nhà thầu hàng tháng để lấy thông tin nộp phí
$business_id = 0;
$mon = 0;
$done = false;
$last_run = 0;
if (!is_dir(NV_ROOTDIR . '/crawls_2023/data/businesslistings')) {
    mkdir(NV_ROOTDIR . '/crawls_2023/data/businesslistings');
}
$run_file = NV_ROOTDIR . '/crawls_2023/data/businesslistings/business_auto_recrawl_run.txt';
$data_file = NV_ROOTDIR . '/crawls_2023/data/businesslistings/business_auto_recrawl.txt';
if (file_exists($run_file)) {
    $last_run = file_get_contents($run_file);
    $last_run = intval($last_run);
}
if ((NV_CURRENTTIME - $last_run) <= 300) {
    echo "\nĐợi timeout";
    die();
}
if (file_exists($data_file)) {
    $business_id = file_get_contents($data_file);
    $business_id = explode('.', $business_id);
    $mon = intval($business_id[0]);
    $done = intval($business_id[2]) == 0 ? false : true;
    $business_id = intval($business_id[1]);
}
if ($mon != intval(date('n', NV_CURRENTTIME))) {
    $business_id = 0;
    $done = false;
}
$module_data = $module_name = 'businesslistings';
if (!$done) {
    $today = mktime(0, 0, 0, intval(date('n', NV_CURRENTTIME)), 1, intval(date('Y', NV_CURRENTTIME))); // Kiểm tra trong tháng. Ngày tự động bóc luôn đặt là ngày 1
    $_sql = "SELECT t1.id, t2.orgcode FROM nv4_businesslistings_addinfo AS t1 INNER JOIN nv4_businesslistings_info AS t2 ON t1.id = t2.id WHERE t1.id > " . $business_id . " AND t2.active = 1 AND t2.orgcode != '' AND t1.auto_crawl_time <= " . $today . " ORDER BY id ASC LIMIT 100";
    $result = $db->query($_sql);
    $arr_row = [];
    $arr_code = [];
    while ($_row = $result->fetch()) {
        $arr_row[$dbcr->quote($_row['orgcode'])] = $_row;
        $arr_code[] = $dbcr->quote($_row['orgcode']);
        file_put_contents($data_file, date('n', NV_CURRENTTIME) . '.' . $_row['id'] . '.0', LOCK_EX);
    }

    if (!empty($arr_code)) {
        $_sql = "UPDATE nv22_nhathau_url SET url_run = 0, uniqid='', re_crawls = 9 WHERE orgcode IN (" . implode(',', $arr_code) . ")";
        $dbcr->exec($_sql);
        echo "\nCó " . count($arr_code) . " nhà thầu cần bóc lại";
    } else {
        file_put_contents($data_file, date('n', NV_CURRENTTIME) . '.0.1', LOCK_EX); // Nếu rà hết thì báo done
        echo "\nĐã rà soát hết trong tháng " . date('m/Y', NV_CURRENTTIME);
    }
    echo "\nUpdate trong: " . number_format((microtime(true) - NV_START_TIME), 3, '.', '');
} else {
    echo "\nĐã rà soát hết trong tháng " . date('m/Y', NV_CURRENTTIME);
}
file_put_contents($run_file, NV_CURRENTTIME, LOCK_EX);
