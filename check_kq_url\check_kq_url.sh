#!/bin/bash

#chown craws:craws -R /home/<USER>/private/crawls/check_kq_url

if [ -f "/home/<USER>/private/crawls/check_kq_url/check_kq_url.txt" ]; then
    if [ -n "$(find /home/<USER>/private/crawls/check_kq_url/ -type f -name check_kq_url.txt -mtime +1)" ]; then
        rm -f /home/<USER>/private/crawls/check_kq_url/check_kq_url.txt
    else
        echo "Co chuong trinh dang chay";
        exit
    fi
fi

TIME="$(date +%T)"
echo "Begin check_kqonline_url $TIME" >> "/home/<USER>/private/crawls/check_kq_url/check_kq_url.txt"
echo "Begin check_kqonline_url $TIME" >> "/home/<USER>/private/crawls/check_kq_url/check_kq_url.log"

for i in {1..5}
do
    php /home/<USER>/private/crawls/check_kq_url/check_kqonline_url.php
    TIME="$(date +%T)"
    echo "$i check_kqonline_url $TIME" >> "/home/<USER>/private/crawls/check_kq_url/check_kq_url.log"
done

TIME="$(date +%T)"
echo "END check_kqonline_url $TIME" >> "/home/<USER>/private/crawls/check_kq_url/check_kq_url.log"


TIME="$(date +%T)"
echo "Begin check_kqoffline_url $TIME" >> "/home/<USER>/private/crawls/check_kq_url/check_kq_url.txt"
echo "Begin check_kqoffline_url $TIME" >> "/home/<USER>/private/crawls/check_kq_url/check_kq_url.log"

for i in {1..6}
do
    php /home/<USER>/private/crawls/check_kq_url/check_kqoffline_url.php
    TIME="$(date +%T)"
    echo "$i check_kqoffline_url $TIME" >> "/home/<USER>/private/crawls/check_kq_url/check_kq_url.log"
done

TIME="$(date +%T)"
echo "END check_kqoffline_url $TIME" >> "/home/<USER>/private/crawls/check_kq_url/check_kqoffline_url.log"

TIME="$(date +%T)"
echo "Begin tbmt_update_month $TIME" >> "/home/<USER>/private/crawls/check_kq_url/check_kq_url.txt"
echo "Begin tbmt_update_month $TIME" >> "/home/<USER>/private/crawls/check_kq_url/check_kq_url.log"
php /home/<USER>/private/crawls/tbmt_update_month.php
TIME="$(date +%T)"
echo "END tbmt_update_month $TIME" >> "/home/<USER>/private/crawls/check_kq_url/check_kqoffline_url.log"

find /home/<USER>/private/crawls/logs -type f -name '*.log'  -mtime +60 | xargs /bin/rm -f
find /home/<USER>/private/crawls/check_kq_url -type f -name 'check*.txt'  -mtime +30 | xargs /bin/rm -f

rm -f /home/<USER>/private/crawls/check_kq_url/check_kq_url.txt
