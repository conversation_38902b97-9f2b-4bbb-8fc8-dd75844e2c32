<?php

// <PERSON><PERSON><PERSON> danh sách: <PERSON>h sách tổ chức, cá nhân vi phạm
// https://muasamcong.mpi.gov.vn/web/guest/organizations-violators
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME))));
require NV_ROOTDIR . '/mainfile.php';

$mode = $request_mode->get('mode', '');

$pageNumber = 0;
$totalPages = 10;
do {
    $new_data = 0;
    $num_data = 0;
    $num_run = 0;
    $data = geturlpage($pageNumber, 1);
    if (isset($data['content'])) {
        $_totalPages = intval($data['totalPages']);
        if ($_totalPages > $totalPages) {
            $totalPages = $_totalPages;
        }

        $data = $data['content'];

        $array_id_msc = [];
        foreach ($data as $row) {
            $array_id_msc[] = $row['id'];
        }

        if (!empty($array_id_msc)) {

            // Tìm trước khi INSERT để tránh AUTO_INCREMENT trăng quá nhanh
            $query = $dbcr->query("SELECT id_msc FROM nv22_organizations_violators_url WHERE id_msc IN ('" . implode("', '", $array_id_msc) . "')");
            $array_id_msc = [];
            while ($_row = $query->fetch()) {
                $array_id_msc[] = $_row['id_msc'];
            }
            $query->closeCursor();

            $prepared = $dbcr->prepare("INSERT INTO `nv22_organizations_violators_url`
                        (`id_msc`, `orgname`, `decisionno`, `status`, `detail1`) VALUES
                        (:id_msc, :orgname, :decisionno, :status, :detail1)");
            foreach ($data as $row) {
                ++$num_data;
                if (!in_array($row['id'], $array_id_msc)) {
                    try {
                        $prepared->bindParam(':id_msc', $row['decisionId'], PDO::PARAM_STR);
                        $prepared->bindParam(':orgname', $row['orgNameViolate'], PDO::PARAM_STR);
                        $prepared->bindParam(':decisionno', $row['decisionNo'], PDO::PARAM_STR);
                        $prepared->bindParam(':status', $row['status'], PDO::PARAM_STR);
                        $prepared->bindValue(':detail1', json_encode($row, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT), PDO::PARAM_STR);
                        $prepared->execute();
                        echo "News: " . $row['orgNameViolate'] . " \n";
                        ++$new_data;
                    } catch (PDOException $e) {
                        if (!preg_match('/Integrity constraint violation\: ([0-9]+) Duplicate entry (.*) for key \'id_msc\'/', $e->getMessage())) {
                            print_r($e);
                        }
                    }
                }
            }

            $prepared->closeCursor();
        }
        sleep(1);
    }
    echo "pageNumber: " . $pageNumber . "/" . $totalPages . "\n";
    echo "num_data:" . $num_data . "\n";
    echo "new_data:" . $new_data . "\n\n";
    ++$pageNumber;

    if ($mode == 'getall') {
        if ($num_data == 0 and $pageNumber > $totalPages) {
            break;
        }
    } elseif ($new_data == 0 and $pageNumber > 1) {
        break;
    }
} while (1);

echo "Thoi gian thuc hien = " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";

function geturlpage($pageNumber, $reload = 1)
{
    global $dbcr, $num_run;
    ++$num_run;

    $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-violation/services/get-list-violate';
    $body = '{"decisionNo":{"contains":null},"orgNameVioLate":{"contains":null},"orgNameDec":{"contains":null},"penType":{"equals":null},"pageSize":100,"pageNumber":' . $pageNumber . '}';
    $referer = 'https://muasamcong.mpi.gov.vn/web/guest/organizations-violators';

    $arr_agent = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:104.0) Gecko/20100101 Firefox/104.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/105.0.1343.42',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 9.0; SAMSUNG SM-F900U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.77 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ];

    srand((float) microtime() * 10000000);
    $rand = array_rand($arr_agent);
    $agent = $arr_agent[$rand];

    $_proxy = $dbcr->query("SELECT * FROM `nv4_proxy` WHERE `status`=2 ORDER BY `lasttime` ASC LIMIT 1")->fetch();
    $ch = curl_init();
    if (isset($_proxy['proxy'])) {
        $dbcr->query("UPDATE `nv4_proxy` SET `lasttime` = " . time() . ", number_run=number_run+1 WHERE `id` = " . $_proxy['id']);
        echo $_proxy['proxy'] . "\n";
        curl_setopt($ch, CURLOPT_PROXY, $_proxy['proxy']);
        if (isset($_proxy['proxyuserpwd']) and !empty($_proxy['proxyuserpwd'])) {
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, $_proxy['proxyuserpwd']);
        }
        curl_setopt($ch, CURLOPT_PROXYTYPE, $_proxy['proxytype']);
        curl_setopt($ch, CURLOPT_PROXYPORT, $_proxy['proxyport']);
    }
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 20);
    curl_setopt($ch, CURLOPT_TIMEOUT, 20);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
    curl_setopt($ch, CURLOPT_REFERER, $referer);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json'
    ));

    $json = curl_exec($ch);
    curl_close($ch);
    $data = json_decode($json, true);
    if (isset($data['content'])) {
        return $data;
    } elseif ($reload and $num_run < 5) {
        return geturlpage($pageNumber, 1);
    } elseif ($reload) {
        return geturlpage($pageNumber, 0);
    }
    return [];
}
