<?php

// Thông tin bổ sung của nhà thầu
// issue https://vinades.org/dauthau/dauthau.info/-/issues/2222 Bóc thêm thông tin nhà thầu theo msc mới
// Bóc thêm thông tin hoạt động của nhà thầu trên msc mới (get-org-infor-spm)
// link ví dụ nhà thầu có thông tin:
// https://muasamcong.mpi.gov.vn/web/guest/approved-contractors-list?p_p_id=egpportalcontractorsapproved_WAR_egpportalcontractorsapproved&p_p_lifecycle=0&p_p_state=normal&p_p_mode=view&_egpportalcontractorsapproved_WAR_egpportalcontractorsapproved_render=detail&orgCode=vn0107301602&effRoleDate=6/10/2020
// https://muasamcong.mpi.gov.vn/o/egp-portal-contractors-approved/services/get-org-infor-spm
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

$uniqid = uniqid('', true);

// work
$dbcr->query("UPDATE nv22_nhathau_url SET spm_time='-" . NV_CURRENTTIME . "', spm_uniqid=" . $dbcr->quote($uniqid) . "  WHERE url_run > 99 AND `spm_time`=0 AND spm_uniqid='' ORDER BY `id` ASC LIMIT 10");
$query = $dbcr->query("SELECT id, orgcode FROM `nv22_nhathau_url`  WHERE spm_uniqid = " . $dbcr->quote($uniqid));

// test
// $query = $dbcr->query("SELECT id, orgcode FROM `nv22_nhathau_url` WHERE orgcode = " . $dbcr->quote('vn0107301602'));
$i = 0;
while ($_contractor = $query->fetch()) {
    print_r($_contractor);
    $num_run = 0;
    $data = get_org_infor_spm_page($_contractor['orgcode']);
    if (isset($data[0]['orgCode'])) {
        $dbcr->query("UPDATE nv22_nhathau_url SET spm_time=" . NV_CURRENTTIME . ", request_spm=" . $dbcr->quote(json_encode($data[0], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT)) . " WHERE id=" . $_contractor['id']);
    } else {
        $dbcr->query("UPDATE nv22_nhathau_url SET spm_time=-1 WHERE id=" . $_contractor['id']);
    }
    ++$i;
}
$query->closeCursor();

if (empty($i)) {
    echo "NO: data\n";
    exit(1);
}

function get_org_infor_spm_page($contractorCode, $reload = 1)
{
    global $dbcr, $num_run;
    $num_run = $num_run + 1;
    $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-contractors-approved/services/get-org-infor-spm';

    $referer = 'https://muasamcong.mpi.gov.vn/web/guest/approved-contractors-list?p_p_id=egpportalcontractorsapproved_WAR_egpportalcontractorsapproved&p_p_lifecycle=0&p_p_state=normal&p_p_mode=view&_egpportalcontractorsapproved_WAR_egpportalcontractorsapproved_render=detail&orgCode=' . $contractorCode . '&effRoleDate=';
    $agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';

    $body = '{"body": "' . $contractorCode . '"}';

    $_proxy = $dbcr->query("SELECT * FROM `nv4_proxy` WHERE `status`=2 ORDER BY `lasttime` ASC LIMIT 1")->fetch();
    $ch = curl_init();
    if (isset($_proxy['proxy'])) {
        $dbcr->query("UPDATE `nv4_proxy` SET `lasttime` = " . time() . ", number_run=number_run+1 WHERE `id` = " . $_proxy['id']);
        echo "get_org_infor_spm_page: " . $_proxy['proxy'] . "\n";
        curl_setopt($ch, CURLOPT_PROXY, $_proxy['proxy']);
        if (isset($_proxy['proxyuserpwd']) and !empty($_proxy['proxyuserpwd'])) {
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, $_proxy['proxyuserpwd']);
        }
        curl_setopt($ch, CURLOPT_PROXYTYPE, $_proxy['proxytype']);
        curl_setopt($ch, CURLOPT_PROXYPORT, $_proxy['proxyport']);
    }
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 60);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
    curl_setopt($ch, CURLOPT_REFERER, $referer);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json'
    ));

    $json = curl_exec($ch);
    curl_close($ch);
    $data = json_decode($json, true);
    if (isset($data[0]['orgCode'])) {
        return $data;
    } elseif (trim($json) == '[]') {
        return [];
    } elseif ($reload and $num_run < 5) {
        return get_org_infor_spm_page($contractorCode, 1);
    }
    return [];
}

die('Lấy Xong request_spm' . "\n");
