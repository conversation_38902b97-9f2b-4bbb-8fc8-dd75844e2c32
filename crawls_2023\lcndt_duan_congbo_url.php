<?php
/**
 * @Project Crawls.DauThau.Info
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2022 VINADES.,JSC. All rights reserved
 * @Code Ngọc Ngân (<EMAIL>)
 * Tool bóc danh sách: Công bố danh mục dự án
 * https://vinades.org/dauthau/dauthau.info/-/issues/1498
 *
 * Chọn Lựa chọn nhà đầu tư rồi click tìm kiếm nâng cao
 * Dự án PPP - Dự án đầu tư có sử dụng đất - Dự án xã hội hóa, chuyên ngành  - Dự án ngoài phạm vi điều chỉnh
 */


define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME))));
require NV_ROOTDIR . '/mainfile.php';

$type_p = $request_mode->get('type_p', '');
$to_date = NV_CURRENTTIME;
$pageNumber = 0;
$totalPages = 0;
$all = (int) $request_mode->get('all', 0);
do {
    $new_data = 0;
    $num_data = 0;
    $num_run = 0;
    $data = geturlpage($pageNumber, 1);
    if (isset($data['page']['content'])) {
        $_totalPages = $data['page']['totalPages'];
        $data = $data['page']['content'];
        if ($_totalPages > $totalPages) {
            $totalPages = $_totalPages;
        }
        $array_id_msc = [];
        foreach ($data as $row) {
            $array_id_msc[] = $dbcr->quote($row['id']);
        }

        if (!empty($array_id_msc)) {
            $q = $dbcr->query("SELECT id_msc FROM `nv23_lcndt_cbdm_duan_url` WHERE id_msc IN (" . implode(',', $array_id_msc) . ")");
            $array_id_msc = [];
            while ($d = $q->fetch()) {
                $array_id_msc[] = $d['id_msc'];
            }
            $q->closeCursor();

            $prepared = $dbcr->prepare("INSERT INTO `nv23_lcndt_cbdm_duan_url` (`id_msc`, `pno`, `pversion`, `type_project`, `agency_name`, `detail1`) VALUES (:id_msc, :pno, :pversion, :type_project, :agency_name, :detail1)");
            foreach ($data as $row) {
                ++$num_data;
                if (!in_array($row['id'], $array_id_msc)) {
                    $row['agencyName'] = isset($row['agencyName']) ? $row['agencyName'] : '';
                    try {
                        $prepared->bindParam(':id_msc', $row['id'], PDO::PARAM_STR);
                        $prepared->bindParam(':pno', $row['projectNo'], PDO::PARAM_STR);
                        $prepared->bindParam(':pversion', $row['projectVersion'], PDO::PARAM_STR);
                        $prepared->bindParam(':type_project', $row['type'], PDO::PARAM_STR);
                        $prepared->bindParam(':agency_name', $row['agencyName'], PDO::PARAM_STR);
                        $prepared->bindValue(':detail1', json_encode($row, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT), PDO::PARAM_STR);
                        $prepared->execute();
                        ++$new_data;
                    } catch (PDOException $e) {
                        if (!preg_match('/Integrity constraint violation\: ([0-9]+) Duplicate entry (.*) for key \'id_msc\'/', $e->getMessage())) {
                            print_r($row);
                            print_r($e);
                        }
                    }
                }
            }
        }
    } else {
        echo "No Data\n";
    }
    echo "Num data: " . $num_data . " News: " . $new_data . " pageNumber: " . $pageNumber . "/" . $totalPages . "\n";
    ++$pageNumber;
} while (($all and $pageNumber <= $totalPages) || !($new_data == 0 and $pageNumber > 1));

function geturlpage($pageNumber, $reload = 1)
{
    global $dbcr, $num_run, $type_p;
    ++$num_run;
    echo 'Đang bóc page: ' . $pageNumber . "\n";
    $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-investor-selection-v2/services/smart-search';
    $body = '[
        {
            "pageSize":10,
            "pageNumber": ' . $pageNumber . ',
            "query": [
                {
                    "index":"es-investor-selection",
                    "keyWord":"",
                    "matchType":"all",
                    "matchFields":[],
                    "filters": [
                        {
                            "fieldName":"type",
                            "searchType":"in",
                            "fieldValues": [
                                "es-bidp-project-invest-ppp-p",
                                "es-bidp-project-invest-sdd-p",
                                "es-project-social-p",
                                "es-bidp-bidding-law-p"
                            ]
                        }
                    ]
                }
            ],
            "sortBy":"publicDate",
            "sortType":"DESC"
        }
    ]';
    if ($type_p == 'law') {
        $body = '[
            {
                "pageSize":10,
                "pageNumber": ' . $pageNumber . ',
                "query": [
                    {
                        "index":"es-investor-selection",
                        "keyWord":"",
                        "matchType":"all",
                        "matchFields":[],
                        "filters": [
                            {
                                "fieldName":"type",
                                "searchType":"in",
                                "fieldValues": [
                                    "es-bidp-bidding-law-p"
                                ]
                            }
                        ]
                    }
                ],
                "sortBy":"publicDate",
                "sortType":"DESC"
            }
        ]';
    }
    $referer = 'https://muasamcong.mpi.gov.vn/web/guest/investor-selection';

    $arr_agent = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:104.0) Gecko/20100101 Firefox/104.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/105.0.1343.42',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 9.0; SAMSUNG SM-F900U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.77 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ];

    srand((float) microtime() * 10000000);
    $rand = array_rand($arr_agent);
    $agent = $arr_agent[$rand];

    $_proxy = $dbcr->query("SELECT * FROM `nv4_proxy` WHERE `status`=2 ORDER BY `lasttime` ASC LIMIT 1")->fetch();
    $ch = curl_init();
    if (isset($_proxy['proxy'])) {
        $dbcr->query("UPDATE `nv4_proxy` SET `lasttime` = " . time() . ", number_run=number_run+1 WHERE `id` = " . $_proxy['id']);
        echo $_proxy['proxy'] . "\n";
        curl_setopt($ch, CURLOPT_PROXY, $_proxy['proxy']);
        if (isset($_proxy['proxyuserpwd']) and !empty($_proxy['proxyuserpwd'])) {
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, $_proxy['proxyuserpwd']);
        }
        curl_setopt($ch, CURLOPT_PROXYTYPE, $_proxy['proxytype']);
        curl_setopt($ch, CURLOPT_PROXYPORT, $_proxy['proxyport']);
    }
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
    curl_setopt($ch, CURLOPT_REFERER, $referer);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json'
    ));

    $json = curl_exec($ch);
    curl_close($ch);

    $data = json_decode($json, true);
    if (isset($data['page']['content'])) {
        return $data;
    } elseif ($reload and $num_run < 5) {
        return geturlpage($pageNumber, 1);
    } elseif ($reload) {
        return geturlpage($pageNumber, 0);
    }
    return [];
}
