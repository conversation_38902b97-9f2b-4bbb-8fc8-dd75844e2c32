<?php
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME))));
require NV_ROOTDIR . '/mainfile.php';

$id = file_get_contents(NV_CONSOLE_DIR . '/businesslistings_old.txt');
$id = intval($id);

do {
    $q = $db->query("SELECT `id`, `code`,`orgcode` FROM `".BUSINESS_PREFIX_GLOBAL."_info` WHERE `id` > " . $id . " LIMIT 10");
    $id = 0;
    while ($row = $q->fetch()) {
        $id = $row['id'];
        $url = 'http://muasamcong.mpi.gov.vn:8070/RA/UM_RAJ_ConuA050y.jsp?bizRegNo=' . $row['code'];
        $html = geturlhtml($url);
        if (strpos($html, $row['code'])) {
            echo $row['code'] . "\n";
            file_put_contents(NV_CONSOLE_DIR . '/business/' . $row['code'] . '.html.gz', gzcompress($html, 9));
        } else {
            echo $row['code'] . " Error\n";
        }
    }
    $q->closeCursor();
    if ($id) {
        file_put_contents(NV_CONSOLE_DIR . '/businesslistings_old.txt', $id);
    }
} while ($id > 0);

//đang chạy ở tiến trình: 1550876.pts-4.craws
