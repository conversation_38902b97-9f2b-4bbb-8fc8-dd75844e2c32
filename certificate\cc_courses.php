<?php
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
/*
cho file NV_ROOTDIR . '/certificate/date.txt' với giá trị 1451606400 (1/1/2016)
$run_bash = 1 để chạy toàn bộ dữ liệu bằng cách chạy qua certificate/cc_courses.sh
+ NV_ROOTDIR . '/certificate/date.txt'  mặc định là ngày 01/01/2016
+ Tăng lên 1 ngày với mỗi vòng chạy
+ Sau khi chạy đến ngày hiện tại thì rename file date_ok.txt để k chạy lại

$run_bash = 2 để chạy dữ liệu hằng ngày nếu msc có dữ liệu mới thì cập nhật

Chạy các dữ liệu lỗi:
$run_bash = số ngày muốn chạy
Trong trường kiểm tra thấy thiếu dữ liệu ngày nào đó thì ta chỉ cần nhập giá trị ngày muốn chạy lại vào đây.
 */

$totalPages = 1;
$filepage = NV_ROOTDIR . '/certificate/pagenumber.txt';
if (file_exists($filepage)) {
    $pageNumber = file_get_contents($filepage);
    $pageNumber = intval($pageNumber);
} else {
    $pageNumber = 1;
}

try {
    global $db, $dbcr;
    $new_data = 0;
    $num_data = 0;
    $num_run = 0;
    $run_bash = (int) $request_mode->get('run_bash', '');
    if ($run_bash > 2) {
        echo ("====================Bắt đầu chạy test theo ngày ========== \n");
        $date = intval($run_bash);
        $data = geturlpage($pageNumber, $date, 1);
    } elseif ($run_bash == 1) {
        if (file_exists(NV_ROOTDIR . '/certificate/date.txt')) {
            echo ("====================Bắt đầu chạy tool quét toàn bộ========== <br/>");
            $date = file_get_contents(NV_ROOTDIR . '/certificate/date.txt');
            $date = intval($date);
            $data = geturlpage($pageNumber, $date, 1);
            if ($data) {
                file_put_contents(NV_ROOTDIR . '/certificate/date.txt', $date);
                if ($date > NV_CURRENTTIME) {
                    echo ("====================Đã chạy xong tool========== <br/>");
                    rename(NV_ROOTDIR . '/certificate/date.txt', NV_ROOTDIR . '/certificate/date_ok.txt');
                    exit(1);
                }
            }
        }
    } else {
        echo ("====================Bắt đầu chạy crontab hàng ngày ========== \n");
        $current_date = mktime(0, 0, 0, date('n'), date('j'), date('Y'));
        $data = geturlpage($pageNumber, $current_date, 1);
    }

    if (isset($data['content'])) {
        $_totalPages = intval($data['totalPages']);
        if ($_totalPages > $totalPages) {
            $totalPages = $_totalPages;
        }

        $id_msc = [];
        foreach ($data['content'] as $row) {
            $id_msc[] = $db->quote($row['id']);
        }

        if (!empty($id_msc)) {
            $q = $db->query("SELECT id, id_msc, url_runtraning FROM `nv4_certificate_courses` WHERE id_msc IN (" . implode(',', $id_msc) . ")");
            $id_msc = [];
            while ($_r = $q->fetch()) {
                $id_msc[$_r['id_msc']] = $_r;
            }
            $q->closeCursor();

            foreach ($data['content'] as $row) {
                ++$num_data;
                $systemTrainerId = isset($row['systemTrainerId']) ? (int)$row['systemTrainerId'] : 0;
                
                $fixed_issuer = fix_vietnamese_encoding($row['issuer']);             
                $fixed_issuerPosition = fixIssuerPosition($row['issuerPosition']);
                // Hoán đổi tên người ký chứng chỉ và chức vụ
                if ($fixed_issuer == 'Giám đốc') {
                    $fixed_issuerPosition = 'Giám đốc';
                    $fixed_issuer = $row['issuerPosition'];
                }
                if (!isset($id_msc[$row['id']])) {
                    // Chèn mới dữ liệu vào nv4_certificate_courses
                    $stmt = $db->prepare('INSERT INTO nv4_certificate_courses (id_msc, name, name_courses, type_course, area, number_student, certificate_date, certificate_singer, start_date, end_date, teacher, role, status, url_runtraning, systemTrainerId)
                    VALUES (:id_msc, :name, :name_courses, :type_course, :area, :number_student, :certificate_date, :certificate_singer, :start_date, :end_date, :teacher, :role, :status, 0, :systemTrainerId)');
                    $stmt->bindParam(':id_msc', $row['id'], PDO::PARAM_INT);
                    $stmt->bindParam(':name', $row['systemTrainerName'], PDO::PARAM_STR);
                    $stmt->bindParam(':name_courses', $row['trainerName'], PDO::PARAM_STR);
                    $stmt->bindParam(':type_course', $row['courseType'], PDO::PARAM_STR);
                    $stmt->bindParam(':area', $row['trainerArea'], PDO::PARAM_STR);
                    $stmt->bindParam(':number_student', $row['studentNumber'], PDO::PARAM_INT);
                    $stmt->bindParam(':certificate_date', strtotime($row['issueDate']), PDO::PARAM_INT);
                    $stmt->bindParam(':certificate_singer', $fixed_issuer, PDO::PARAM_STR);
                    $stmt->bindParam(':start_date', strtotime($row['startDate']), PDO::PARAM_STR);
                    $stmt->bindParam(':end_date', strtotime($row['endDate']), PDO::PARAM_STR);
                    $stmt->bindParam(':teacher', $row['lecturerName'], PDO::PARAM_STR);
                    $stmt->bindParam(':role', $fixed_issuerPosition, PDO::PARAM_STR);
                    $stmt->bindParam(':status', $row['status'], PDO::PARAM_STR);
                    $stmt->bindParam(':systemTrainerId', $systemTrainerId, PDO::PARAM_INT);
                    $stmt->execute();
                    echo "\nĐã thêm mới id_msc = " . $row['id'] . " <br/>";
                    ++$new_data;

                } else {
                    // Cập nhật nv4_certificate_courses nếu cần
                    $stmt = $db->prepare('UPDATE nv4_certificate_courses SET name=:name, name_courses=:name_courses, type_course=:type_course, area=:area, number_student=:number_student, certificate_date=:certificate_date, certificate_singer=:certificate_singer, start_date=:start_date, end_date=:end_date, teacher=:teacher, role=:role, status=:status, systemTrainerId=:systemTrainerId WHERE id_msc = ' . $db->quote($row['id']));
                    $stmt->bindParam(':name', $row['systemTrainerName'], PDO::PARAM_STR);
                    $stmt->bindParam(':name_courses', $row['trainerName'], PDO::PARAM_STR);
                    $stmt->bindParam(':type_course', $row['courseType'], PDO::PARAM_STR);
                    $stmt->bindParam(':area', $row['trainerArea'], PDO::PARAM_STR);
                    $stmt->bindParam(':number_student', $row['studentNumber'], PDO::PARAM_INT);
                    $stmt->bindParam(':certificate_date', strtotime($row['issueDate']), PDO::PARAM_INT);
                    $stmt->bindParam(':certificate_singer', $fixed_issuer, PDO::PARAM_STR);
                    $stmt->bindParam(':start_date', strtotime($row['startDate']), PDO::PARAM_STR);
                    $stmt->bindParam(':end_date', strtotime($row['endDate']), PDO::PARAM_STR);
                    $stmt->bindParam(':teacher', $row['lecturerName'], PDO::PARAM_STR);
                    $stmt->bindParam(':role', $fixed_issuerPosition, PDO::PARAM_STR);
                    $stmt->bindParam(':status', $row['status'], PDO::PARAM_STR);
                    $stmt->bindParam(':systemTrainerId', $systemTrainerId, PDO::PARAM_INT);
                    $stmt->execute();
                    echo "Update thành công id_msc = " . $row['id'] . " <br/>";
                }

                // Chỉ xử lý bảng nv4_certificate_training nếu url_runtraning = 0
                if ($id_msc[$row['id']]['url_runtraning'] == 0) {
                    $check_query = $db->query("SELECT id, course_number FROM nv4_certificate_training WHERE name LIKE " . $db->quote('%' . $row['systemTrainerName'] . '%'));
                    $existing_training = $check_query->fetch();

                    if (!$existing_training) {
                        // Thêm mới vào nv4_certificate_training
                        $stmt = $db->prepare("INSERT INTO `nv4_certificate_training` (`name`, `course_number`) VALUES (:name, :course_number)");
                        $stmt->bindParam(':name', $row['systemTrainerName'], PDO::PARAM_STR);
                        $stmt->bindValue(':course_number', 1, PDO::PARAM_INT);
                        $stmt->execute();
                        $id_training = $db->lastInsertId();
                    } else {
                        // Cập nhật course_number
                        $new_course_number = $existing_training['course_number'] + 1;
                        $stmt = $db->prepare("UPDATE `nv4_certificate_training` SET `course_number` = :course_number WHERE `id` = :id");
                        $stmt->bindParam(':id', $existing_training['id'], PDO::PARAM_INT);
                        $stmt->bindParam(':course_number', $new_course_number, PDO::PARAM_INT);
                        $stmt->execute();
                        $id_training = $existing_training['id'];
                    }
                    // Cập nhật url_runtraning và id_training trong nv4_certificate_courses
                    $id_msc_quoted = $db->quote($row['id']);
                    $sql = "UPDATE `nv4_certificate_courses` SET `id_training` = :id_training, `url_runtraning` = 1 WHERE `id_msc` = " . $id_msc_quoted;
                    $update_course = $db->prepare($sql);
                    $update_course->bindParam(':id_training', $id_training, PDO::PARAM_INT);
                    $update_course->execute();
                    echo "Đã xử lý url_runtraning cho id_msc: " . $row['id'] . " <br/>";

                }
            }
        }

        echo "\nSố tin mới: " . $new_data . " pageNumber: " . $pageNumber . "/" . $totalPages . "<br/>";
        echo 'Date: ' . date('d/m/Y', $date) . "\n";

        // Tăng số trang và lưu lại
        ++$pageNumber;
        file_put_contents($filepage, $pageNumber);

        // Nếu đã xử lý hết tất cả các trang của ngày hiện tại, chuyển sang ngày tiếp theo
        if ($pageNumber > $totalPages) {
            unlink($filepage); // Xóa file lưu số trang
            if ($run_bash == 1) {
                $date += 86400; // Chuyển sang ngày tiếp theo
                file_put_contents(NV_ROOTDIR . '/certificate/date.txt', $date);
                echo "Chuyển sang ngày mới: " . date('d/m/Y', $date) . "\n";
            }
        }
    }

} catch (Exception $e) {
    echo '<pre>';
    print_r($e);
    echo '</pre>';
    trigger_error(print_r($e, true), 256);
    die(1);
}

echo "Thời gian thực hiện = " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";

function geturlpage($pageNumber, $date, $reload = 1)
{
    global $dbcr, $num_run;
    ++$num_run;
    $datec = date('d', $date);
    $month = date('m', $date);
    $year = date('Y', $date);
    $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-tender-certificate/services/get-list';
    $body = '{
        "pageSize": 5,
        "pageNumber": ' . $pageNumber . ',
        "trainingClass": {
            "lectureName": "",
            "issueDateFrom": "' . $year . '-' . $month . '-' . $datec . 'T00:00:00.000Z",
            "issueDateTo": "' . $year . '-' . $month . '-' . $datec . 'T00:00:00.000Z",
            "startDate": "",
            "endDate": "",
            "systemTrainerName": "",
            "issuer": "",
            "courseType": "",
            "approveDateFrom": "",
            "approveDateTo": ""
        }
    }';

    $referer = 'https://muasamcong.mpi.gov.vn/web/guest/student-list';

    $arr_agent = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:104.0) Gecko/20100101 Firefox/104.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/105.0.1343.42',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 9.0; SAMSUNG SM-F900U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.77 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ];
    srand((float) microtime() * 10000000);
    $rand = array_rand($arr_agent);
    $agent = $arr_agent[$rand];
    $ch = curl_init();

    if (defined('USE_PROXY')) {
        $_proxy = $dbcr->query("SELECT * FROM `nv4_proxy` WHERE `status`=2 ORDER BY `lasttime` ASC LIMIT 1")->fetch();
        if (isset($_proxy['proxy'])) {
            $dbcr->query("UPDATE `nv4_proxy` SET `lasttime` = " . time() . ", number_run=number_run+1 WHERE `id` = " . $_proxy['id']);
            echo $_proxy['proxy'] . "\n";
            curl_setopt($ch, CURLOPT_PROXY, $_proxy['proxy']);
            if (isset($_proxy['proxyuserpwd']) and !empty($_proxy['proxyuserpwd'])) {
                curl_setopt($ch, CURLOPT_PROXYUSERPWD, $_proxy['proxyuserpwd']);
            }
            curl_setopt($ch, CURLOPT_PROXYTYPE, $_proxy['proxytype']);
            curl_setopt($ch, CURLOPT_PROXYPORT, $_proxy['proxyport']);
        }
    }
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 20);
    curl_setopt($ch, CURLOPT_TIMEOUT, 20);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
    curl_setopt($ch, CURLOPT_REFERER, $referer);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json'
    ));

    $json = curl_exec($ch);
    curl_close($ch);

    $data = json_decode($json, true);

    if (isset($data['content'])) {
        return $data;
    } elseif ($reload and $num_run < 5) {
        return geturlpage($pageNumber, $date, 1);
    } elseif ($reload) {
        echo 'Date: ' . date('d/m/Y', $date) . "\n";
        $date = $date + 86400;
        file_put_contents(NV_ROOTDIR . '/certificate/date.txt', $date);
        return geturlpage($pageNumber, $date, 0);
    }
    return [];
}
function fix_vietnamese_encoding($str) {
    $search = [
        'ThS. Nguyá»…n ÄÃ¬nh HÆ°ng', 'ThS Nguyá»…n ÄÃ¬nh HÆ°ng', 'Nguyá»…n Kim Phong', 'NguyÃ¡Â»â€¦n Kim Phong', 'nguyá»…n kim phong',
        'nguyÃƒÆ’Ã‚Â¡Ãƒâ€šÃ‚', 'Â»ÃƒÂ¢Ã¢â€šÂ¬Ã‚Â¦n', 'NguyÃƒÂ¡Ã‚Â»Ã¢â‚¬Â¦n Kim Phong', 'nguyÃƒÆ’Ã†â€™Ãƒâ€šÃ‚Âªn kim phong', 'Nguyá»…n', 'NguyÃƒÆ’Ã‚Â¡Ãƒâ€šÃ‚', 'nguyÃƒÂ¡Ã‚Â»Ã¢â‚¬Â¦n', 'Nguyá»… ',
        'NGUYÃ¡Â»â€žN KIM PHONG', 'NGUYÃŠN KIM PHONG', 'NGuyá»…n KIm Phong', 'TÃ´ Minh Thu', 'Mai VÄƒn KhÃ¡nh', 'Mai VÃ„Æ’n KhÃƒÂ¡nh',
        'VÃƒÆ’Ã†â€™ÃƒÂ¢Ã¢â€šÂ¬Ã…Â¾ÃƒÆ’Ã¢â‚¬Â', 'ÃƒÂ¢Ã¢â€šÂ¬Ã¢â€žÂ¢n', 'KhÃƒÆ’Ã†â€™Ãƒâ€', 'Ã¢â‚¬â„¢ÃƒÆ’Ã¢â‚¬Å¡Ãƒâ€šÃ‚Â¡nh', 'Mai VÃƒâ€žÃ†â€™n KhÃƒÆ’Ã‚Â¡nh',
        'Nguyá»…n VÄƒn KhiÃªm', 'NguyÃ¡Â»â€¦n VÃ„Æ’n KhiÃƒÂªm', 'Äá»– QUANG HÆ¯NG', 'Nguyá»…n Thá»‹ Hiá»n', 'Triá»‡u Thá»‹ Tháº¯m',
        'Ã„ÂOAÃŒâ‚¬N DUY NAM', 'LÃª Tráº§n Tháº¯ng', 'Chu VÄƒn Tuáº¥n', 'Chu VĂ„Æ’n TuĂ¡ÂºÂ¥n', 'CHU VÄ‚N TUáº¤N', 'Tráº§n Há»¯u HÃ',
        'TS Nguyá»…n Há»¯u HÃ', 'TS . Tráº§n Há»¯u HÃ  Â', 'GiÃ¡m Ä‘á»‘c', 'TrÃƒÆ’Ã‚Â n HÃƒÂ¡Ã‚Â»Ã‚Â¯u HÃƒÆ’Ã‚Â', 'TrÃ¡ÂºÂ§n HÃ¡Â»Â¯u HÃƒÂ', 'TS. TrÃ¡ÂºÂ§n HÃ¡Â»Â¯u HÃƒÂ',
        'NguyÃ¡Â»â€¦n TÃ¡ÂºÂ¥n BÃƒÂ¬nh', 'Nguyá»…n Táº¥n BÃ¬nh', 'NguyÃƒÂ¡Ã‚Â»Ã¢â‚¬Â¦n TÃƒÂ¡Ã‚ÂºÃ‚Â¥n BÃƒÆ’Ã‚Â¬nh', 'NGUYá»„N Táº¤N BÃŒNH', 'LÃƒÂª Thanh HÃ¡ÂºÂ£i', 'LÃª Thanh Háº£',
        'LÃª Thanh Háº£i', 'LÃƒÆ’Ã‚Âª Thanh HÃƒÂ¡Ã‚ÂºÃ‚Â£i', 'VÅ© VÄƒn Thá»‹nh', 'Pháº¡m Kim Long', 'BÃ¹i VÄƒn ThiÃªn', 'LÃª VÄƒn CÆ°',
        'NGUYá»„N KIM PHONG', 'LÃ¢m VÄƒn QuaÌ‰n', 'Tráº§n Tháº©m', 'Nguyá»…n Thá»‹ Tháº¯ng', 'Pháº¡m Thá»‹ Thá»§y', 'Chu Quang Ngá»c', 'Nguyễn VÄƒn KhiÃªm', 'TrÃƒÆ’Ã‚Â n HÃƒÂ¡Ã‚Â»Ã‚Â¯u HÃƒÆ’Ã‚Â  '
    ];

    $replace = [
        'ThS. Nguyễn Đình Hưng', 'ThS. Nguyễn Đình Hưng', 'Nguyễn Kim Phong', 'Nguyễn Kim Phong', 'Nguyễn Kim Phong',
        'Nguyễn', '', 'Nguyễn Kim Phong', 'Nguyễn Kim Phong', 'Nguyễn', 'Nguyễn', 'Nguyễn', 'Nguyễn ',
        'Nguyễn Kim Phong', 'Nguyễn Kim Phong', 'Nguyễn Kim Phong', 'Tô Minh Thu', 'Mai Văn Khánh', 'Mai Văn Khánh',
        'Văn', '', '', 'Khánh', 'Mai Văn Khánh',
        'Nguyễn Văn Khiêm', 'Nguyễn Văn Khiêm', 'Đỗ Quang Hưng', 'Nguyễn Thị Hiền', 'Triệu Thị Thắm',
        'ĐOÀN DUY NAM', 'Lê Trần Thắng', 'Chu Văn Tuấn', 'Chu Văn Tuấn', 'Chu Văn Tuấn', 'Trần Hữu Hã',
        'TS. Nguyễn Hữu Hã', 'TS. Nguyễn Hữu Hã', 'Giám đốc', 'Trần Hữu Hã', 'Trần Hữu Hã', 'TS. Trần Hữu Hã',
        'Nguyễn Tấn Bình', 'Nguyễn Tấn Bình', 'Nguyễn Tấn Bình', 'Nguyễn Tấn Bình', 'Lê Thanh Hải', 'Lê Thanh Hải',
        'Lê Thanh Hải', 'Lê Thanh Hải', 'Vũ Văn Thịnh', 'Phạm Kim Long', 'Bùi Văn Thiên', 'Lê Văn Cư',
        'Nguyễn Kim Phong', 'Lâm Văn Quản', 'Trần Thẩm', 'Nguyễn Thị Thắng', 'Phạm Thị Thủy', 'Chu Quang Ngọc', 'Nguyễn Văn Khiêm', 'Trần Hữu Hà'
    ];

    $str = str_replace($search, $replace, $str);

    return $str;
}
function fixIssuerPosition($issuerPosition) {
    $replacements = [
        'GiÃ¡m Ä‘á»‘c' => 'Giám đốc',
        'GiÃƒÂ¡m Ã„â€˜Ã¡Â»â€˜c' => 'Giám đốc',
        'GiÃ¡m Äá»‘c' => 'Giám đốc',
        'giĂ¡m Ä‘á»‘c' => 'Giám đốc',
        'GiĂ¡m Ä‘á»‘c' => 'Giám đốc',
        'GiÃƒÂ¡m Ã„â€˜Ã¡Â»â€˜c' => 'Giám đốc',
        'GIAÃŒÂM Ã„ÂÃƒâ€ÃŒÂC ' => 'Giám đốc',
        'GiÃ¡m dá»‘c ' => 'Giám đốc',
        'GiÃƒÆ’Ã‚Â¡m Ãƒâ€žÃ‚ÂÃƒÂ¡Ã‚Â»Ã¢â‚¬Ëœc' => 'Giám đốc',
        'GiÃƒÆ’Ã‚Â¡m Ãƒâ€žÃ¢â‚¬ËœÃƒÂ¡Ã‚Â»Ã¢â‚¬Ëœc ' => 'Giám đốc',
        'GiÃƒÆ’Ã†â€™Ãƒâ€ Ã¢â‚¬â„¢ÃƒÆ’Ã¢â‚¬Å¡Ãƒâ€šÃ‚Â¡m ÃƒÆ’Ã†â€™ÃƒÂ¢Ã¢â€šÂ¬Ã…Â¾ÃƒÆ’Ã‚Â¢ÃƒÂ¢Ã¢â‚¬Å¡Ã‚Â¬Ãƒâ€¹Ã…â€œÃƒÆ’Ã†â€™Ãƒâ€šÃ‚Â¡ÃƒÆ’Ã¢â‚¬Å¡Ãƒâ€šÃ‚Â»ÃƒÆ’Ã‚Â¢ÃƒÂ¢Ã¢â‚¬Å¡Ã‚Â¬Ãƒâ€¹Ã…â€œc' => 'Giám đốc',
        'giÃƒÆ’Ã†â€™Ãƒâ€šÃ‚Â¡m ÃƒÆ’Ã¢â‚¬Å¾ÃƒÂ¢Ã¢â€šÂ¬Ã‹Å“ÃƒÆ’Ã‚Â¡Ãƒâ€šÃ‚Â»ÃƒÂ¢Ã¢â€šÂ¬Ã‹Å“c' => 'Giám đốc',
        'GiÃƒÆ’Ã†â€™Ãƒâ€šÃ‚Â¡m ÃƒÆ’Ã¢â‚¬Å¾Ãƒâ€šÃ‚ÂÃƒÆ’Ã‚Â¡Ãƒâ€šÃ‚Â»ÃƒÂ¢Ã¢â€šÂ¬Ã‹Å“c' => 'Giám đốc',
        'giam Ä‘Ã´c' => 'Giám đốc',
        'giam Ã„â€˜Ã¡Â»â€˜c' => 'Giám đốc',
        'Giam Ã„ÂÃ¡Â»â€˜c' => 'Giám đốc',
        'GIAM ÄÃ”C' => 'Giám đốc',
        'GIĂM Äá»C' => 'Giám đốc',
        'GIÃM Äá»C' => 'Giám đốc',
        'giam ÃƒÆ’Ã¢â‚¬Å¾ÃƒÂ¢Ã¢â€šÂ¬Ã‹Å“oc' => 'Giám đốc'
    ];
    
    return str_replace(array_keys($replacements), array_values($replacements), $issuerPosition);
}

?>
