#!/bin/bash

SOURCE="${BASH_SOURCE[0]}"
while [ -h "$SOURCE" ]; do
  TARGET="$(readlink "$SOURCE")"
  if [[ $TARGET == /* ]]; then
    #echo "SOURCE '$SOURCE' is an absolute symlink to '$TARGET'"
    SOURCE="$TARGET"
  else
    DIR="$( dirname "$SOURCE" )"
    #echo "SOURCE '$SOURCE' is a relative symlink to '$TARGET' (relative to '$DIR')"
    SOURCE="$DIR/$TARGET"
  fi
done
#echo "SOURCE is '$SOURCE'"
RDIR="$( dirname "$SOURCE" )"
DIR="$( cd -P "$( dirname "$SOURCE" )" >/dev/null 2>&1 && pwd )"
#if [ "$DIR" != "$RDIR" ]; then
  #echo "DIR '$RDIR' resolves to '$DIR'"
#fi
#echo "DIR is '$DIR'"

cd "$DIR/../"
DIR_PATH=$PWD

START=$(date +%s)
RUN=1
while [ $RUN -gt 0 ]; do
    php "$DIR_PATH/crawls_2023/filter_email_kqlcnt_foreign.php"
    if [ -f "$DIR_PATH/crawls_2023/filter_email_kqlcnt_foreign_run.txt" ]; then
        RUN=$(cat "$DIR_PATH/crawls_2023/filter_email_kqlcnt_foreign_run.txt")
    else
        RUN=0
    fi
    sleep 1
done

echo ""
echo "Kết thúc"
echo "Tổng cộng: $(($(date +%s) - $START))"
