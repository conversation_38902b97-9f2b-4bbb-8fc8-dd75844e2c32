<?php

// <PERSON>h sách nhà thầu được phê duyệt screen -r 3294408
// https://muasamcong.mpi.gov.vn/web/guest/approved-contractors-list
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

file_put_contents(NV_ROOTDIR . '/crawls_2023/logs/nhathau_url.log', "\nBegin: " . date('Y-m-d H:i') . "\n", FILE_APPEND);

$dbcr->query("UPDATE `nv22_nhathau_url` SET `url_run`=0, uniqid='' WHERE `url_run`<0 AND `count_url`<6");
$mode = $request_mode->get('mode', ''); // php tbmt_url.php --mode=getall

$pageNumber = 0;
$totalPages = 0;
$pageEmpty = 0;
do {
    $new_data = 0;
    $num_data = 0;
    $num_run = 0;
    file_put_contents(NV_ROOTDIR . '/crawls_2023/logs/nhathau_url.page', date('Y-m-d H:i') . ": " . $pageNumber);
    $data = geturlpage($pageNumber, 1);
    if (isset($data['content'])) {
        $arr_orgcode = [];
        foreach ($data['content'] as $row) {
            $arr_orgcode[] = $row['orgCode'];
        }
        if (!empty($arr_orgcode)) {
            $_query = $dbcr->query("SELECT orgcode, detail1 FROM `nv22_nhathau_url` WHERE orgcode IN ('" . implode("', '", $arr_orgcode) . "')");
            $arr_orgcode = [];
            while ($row = $_query->fetch()) {
                $arr_orgcode[$row['orgcode']] = $row['detail1'];
            }
            $_query->closeCursor();

            $prepared = $dbcr->prepare("INSERT INTO `nv22_nhathau_url` (`orgcode`, `detail1`) VALUES (:orgcode, :detail1)");
            foreach ($data['content'] as $row) {
                ++$num_data;
                $detail1 = json_encode($row, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);

                $check_detail1 = false;
                if (isset($arr_orgcode[$row['orgCode']])) {
                    if (strcmp($arr_orgcode[$row['orgCode']], $detail1) !== 0) {
                        // Kiểm tra và Update thông tin
                        $check_detail1 = true;
                    }
                } else {
                    try {
                        $prepared->bindParam(':orgcode', $row['orgCode'], PDO::PARAM_STR);
                        $prepared->bindValue(':detail1', $detail1, PDO::PARAM_STR);
                        $prepared->execute();
                        echo "News: " . $row['orgCode'] . " \n";
                        ++$new_data;
                    } catch (PDOException $e) {
                        if (preg_match('/Integrity constraint violation\: ([0-9]+) Duplicate entry (.*) for key \'orgcode\'/', $e->getMessage())) {
                            $check_detail1 = true;
                        } else {
                            print_r($row);
                            print_r($e);
                        }
                    }
                }
                if ($check_detail1) {
                    $dbcr->query("UPDATE `nv22_nhathau_url` SET `detail1` = " . $dbcr->quote($detail1) . ", `url_run`=0, uniqid='', check_detail1=" . time() . " WHERE `orgcode` = " . $dbcr->quote($row['orgCode']));
                }
            }
            unset($arr_orgcode);
        }
        if ($data['totalPages'] > $totalPages) {
            $totalPages = $data['totalPages'];
        }
    }
    echo "pageNumber: " . $pageNumber . "/" . $totalPages . "\n";
    echo "new_data:" . $new_data . "/" . $num_data . "\n\n";
    if ($new_data > 0) {
        $pageEmpty = 0;
        file_put_contents(NV_ROOTDIR . '/crawls_2023/logs/nhathau_url.log', $pageNumber . ": " . $new_data . "\n", FILE_APPEND);
    } else {
        ++$pageEmpty;
    }
    if ($num_data > 0) {
        ++$pageNumber;
    } else {
        sleep(60);
    }

    if ($mode == 'getall') {
        if ($pageNumber < $totalPages) {
            break;
        }
    } else {
        if ($new_data == 0 and $pageEmpty > 2) {
            break;
        }
    }
} while (1);
file_put_contents(NV_ROOTDIR . '/crawls_2023/logs/nhathau_url.log', "\nEND, pageNumber: " . $pageNumber . ":  " . date('Y-m-d H:i') . "\n", FILE_APPEND);

$dbcr->query("UPDATE `nv22_nhathau_url` SET `url_run`=0, uniqid='' WHERE `url_run`<0 AND `count_url`<6");

function geturlpage($pageNumber, $reload = 1)
{
    global $dbcr, $num_run, $totalPages;
    $num_run = $num_run + 1;
    $url = 'https://muasamcong.mpi.gov.vn/o/egp-portal-contractors-approved/services/get-list';

    $referer = 'https://muasamcong.mpi.gov.vn/web/guest/approved-contractors-list';
    $arr_agent = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:104.0) Gecko/20100101 Firefox/104.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/105.0.1343.42',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 9.0; SAMSUNG SM-F900U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        'Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.77 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ];
    srand((float) microtime() * 10000000);
    $rand = array_rand($arr_agent);
    $agent = $arr_agent[$rand];

    $body = '{
        "pageSize": 7,
        "pageNumber": ' . $pageNumber . ',
        "queryParams": {
          "roleType": {
            "equals": "NT"
          },
          "orgName": {
            "contains": null
          },
          "taxCode": {
            "contains": null
          },
          "orgNameOrOrgCode": {
            "contains": ""
          }
        }
    }';
    $_proxy = $dbcr->query("SELECT * FROM `nv4_proxy` WHERE `status`=2 ORDER BY `lasttime` ASC LIMIT 1")->fetch();
    $ch = curl_init();
    if (isset($_proxy['proxy'])) {
        $dbcr->query("UPDATE `nv4_proxy` SET `lasttime` = " . time() . ", number_run=number_run+1 WHERE `id` = " . $_proxy['id']);
        echo $_proxy['proxy'] . "\n";
        curl_setopt($ch, CURLOPT_PROXY, $_proxy['proxy']);
        if (isset($_proxy['proxyuserpwd']) and !empty($_proxy['proxyuserpwd'])) {
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, $_proxy['proxyuserpwd']);
        }
        curl_setopt($ch, CURLOPT_PROXYTYPE, $_proxy['proxytype']);
        curl_setopt($ch, CURLOPT_PROXYPORT, $_proxy['proxyport']);
    }
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 60);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
    curl_setopt($ch, CURLOPT_REFERER, $referer);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json'
    ));

    $json = curl_exec($ch);
    curl_close($ch);
    $data = json_decode($json, true);
    if (isset($data['content'])) {
        return $data;
    } elseif ($reload and $num_run < 5) {
        return geturlpage($pageNumber, 1);
    }
    return [];
}

die('Lấy Xong Contractor url');
