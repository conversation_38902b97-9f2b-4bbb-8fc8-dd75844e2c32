<?php
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME))));
require NV_ROOTDIR . '/mainfile.php';

$folder_business = NV_CONSOLE_DIR . '/business/';

// Lấy các file name
$limit = 100;
$query_file = $db->query('SELECT * FROM ' . BUSINESS_PREFIX_GLOBAL . '_old_file WHERE url_run = 0 ORDER BY id ASC LIMIT ' . $limit);

while ($file_row = $query_file->fetch()) {
    $file_path = $folder_business . $file_row['file_name'];
    $id = $file_row['id'];
    if(file_exists($file_path)) {
        $file_name = basename($file_path);
        echo $file_name."\n";
        $html = file_get_contents($folder_business . basename($file_path));
        $html = gzuncompress($html, 0);

        if (!empty($html)) {
            // Chạy để insert, update
            $db->query("UPDATE " . BUSINESS_PREFIX_GLOBAL . "_old_file SET url_run='-1', time_run=" . NV_CURRENTTIME . " WHERE id=" . $id);
            getdata_old($html, $id);
        } else {
            $db->query("UPDATE " . BUSINESS_PREFIX_GLOBAL . "_old_file SET url_run='-8', crawls_info='html rỗng' WHERE id=" . $id);
        }
    } else {
        $db->query("UPDATE " . BUSINESS_PREFIX_GLOBAL . "_old_file SET url_run='-7', crawls_info='Không tìm thấy file' WHERE id=" . $id);
    }
}
$query_file->closeCursor();

function getdata_old($html, $id)
{
    global $db, $dbcr, $array_name_staff;

    $array_lvkd = [
        1 => 'Hàng hóa',
        2 => 'Xây lắp',
        3 => 'Tư vấn',
        4 => 'Phi tư vấn',
        5 => 'Hỗn hợp'
    ];

    $dom = new DOMDocument();
    libxml_use_internal_errors(true);
    $dom->loadHTML($html);
    $xpath = new DOMXPath($dom);

    if ($xpath->query('//td[@class="HEADLINE"]')->length > 0) {
        $error_i = $xpath->query('//td[@class="HEADLINE"]');
        if (trim(DOMinnerHTML($error_i->item(0)) == 'Thông báo lỗi')) {
            $db->query("UPDATE " . BUSINESS_PREFIX_GLOBAL . "_old_file SET url_run='-5', crawls_info='Lỗi dữ liệu gốc' WHERE id=" . $id);
            return false;
        }
    }

    $nodeListLabel = $xpath->query('//td[@class="tdar"]'); // Tên các trường dữ liệu
    $nodeListControl = $xpath->query('//td[@class="tdb"]'); // Giá trị các trường dữ liệu tương ứng
    $nodListNghe = $xpath->query('//table/tr/td[@colspan=5]'); // List các ngành nghề
    $nodListThoihan = $xpath->query('//table/tr/th[@align="right"]'); // thời hạn

    if ($xpath->query("//*[@id='bid_estimated']")->length > 0) {
        $sotienchu_goithau = $xpath->query("//*[@id='bid_estimated']")->item(0)->nodeValue;
    }
    $nodeListSubTitle = $xpath->query('//td[@class="subtitle"]');

    $row = array();
    if ($nodeListLabel->length > 0) {
        // Loop và lấy các trường dữ liệu

        $i = 0;
        foreach ($nodeListLabel as $node) {
            $key = preg_replace('/\s+/', '_', DOMinnerHTML($node));
            $key = preg_replace('/&nbsp;+/', '', trim($key));
            $key = vn_to_str(html_entity_decode($key));
            if (!empty($nodeListControl->item($i)->nodeValue)) {
                $value = DOMinnerHTML($nodeListControl->item($i));
                $value = preg_replace('/(\s+)|(&nbsp;+)/', ' ', $value);
            } else {
                $value = '';
            }
            $row[strtolower($key)] = strip_tags($value);
            $i++;
        }

        // Loop và lấy ngành nghề
        $arr_nghe = array();
        foreach ($nodListNghe as $node) {
            $arr_nghe[] = html_entity_decode(DOMinnerHTML($node), ENT_QUOTES, 'UTF-8');
        }
        array_shift($arr_nghe);
        $str_nghe = [];
        $array_industry = [
            'data_insert5' => [],
            'data_insert4' => [],
            'data_insert3' => [],
            'data_insert2' => [],
            'data_insert1' => []
        ];
        foreach ($arr_nghe as $nghe) {
            preg_match('/([\w\s].*)/', $nghe, $m);
            $value = trim($db->quote($m[0]), "'");
            $value = addcslashes($value, '_%');

            $sql_industry = "SELECT level, code FROM nv4_vi_industry WHERE title LIKE '%" . $value . "%' OR title_vsic_2007 LIKE '%" . $value . "%'
            ORDER BY CASE
                WHEN level=4 THEN 6
                ELSE level
            END DESC LIMIT 1";
            $result = $db->query($sql_industry);
            if ($industry = $result->fetch()) {
                $industry['level'] >= 5 && $array_industry['data_insert5'][] = $industry['code'];
                $industry['level'] >= 4 && $array_industry['data_insert4'][] = substr($industry['code'], 0, 5);
                $industry['level'] >= 3 && $array_industry['data_insert3'][] = substr($industry['code'], 0, 4);
                $industry['level'] >= 2 && $array_industry['data_insert2'][] = substr($industry['code'], 0, 3);
                $industry['level'] >= 1 && $array_industry['data_insert1'][] = substr($industry['code'], 0, 1);
            }
            $str_nghe[] = $nghe;
        }
        $row['thong_tin_nganh_nghe'] = implode('[]', $str_nghe);

        foreach ($array_industry as $key => $value) {
            $value = array_unique($value);
            $value = empty($value) ? '' : implode(',', $value);
            $array_industry[$key] = $value;
        }

        $row['ma_so_doanh_nghiep'] = !empty(trim($row['ma_so_doanh_nghiep'])) ? preg_replace('/\s+/', '', $row['ma_so_doanh_nghiep']) : '';
        $row['so_dkkd'] = (isset($row['so_dkkd']) and !empty(trim($row['so_dkkd']))) ? preg_replace('/\s+/', '', $row['so_dkkd']) : $row['ma_so_doanh_nghiep'];

        $row['ngay_dkkd'] = !empty($row['ngay_dkkd']) ? $row['ngay_dkkd'] : $row['ngay_thanh_lap'];
        if (!empty($row['ngay_dkkd'])) {
            if (preg_match('/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/', $row['ngay_dkkd'], $m)) {
                $row['ngay_dkkd'] = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
            }
        } else {
            $row['ngay_dkkd'] = 0;
        }

        $row['ten_nha_thau'] = !empty($row['ten_nha_thau']) ? html_entity_decode($row['ten_nha_thau'], ENT_QUOTES, 'UTF-8') : (!empty($row['ten_tieng_viet']) ? html_entity_decode($row['ten_tieng_viet'], ENT_QUOTES, 'UTF-8') : '');
        $row['ten_tieng_anh'] = !empty($row['ten_tieng_anh']) ? html_entity_decode($row['ten_tieng_anh'], ENT_QUOTES, 'UTF-8') : '';

        $row['linh_vuc_kinh_doanh'] = !empty($row['linh_vuc_kinh_doanh']) ? html_entity_decode($row['linh_vuc_kinh_doanh'], ENT_QUOTES, 'UTF-8') : '';
        $arr_linh_vuc_kinh_doanh = explode(', ', $row['linh_vuc_kinh_doanh']);
        $arr_linh_vuc = array();
        foreach ($arr_linh_vuc_kinh_doanh as $value) {
            $value = trim($value);
            if ($key = array_search($value, $array_lvkd)) {
                $arr_linh_vuc[$key] = $key;
            }
        }
        $row['linh_vuc_kinh_doanh'] = implode(',', $arr_linh_vuc);
        $row['linh_vuc_kinh_doanh'] = !empty($row['linh_vuc_kinh_doanh']) ? html_entity_decode($row['linh_vuc_kinh_doanh'], ENT_QUOTES, 'UTF-8') : '';
        if (empty($row['so_nhan_vien'])) {
            $row['so_nhan_vien'] = 0;
            if (preg_match_all('/setNumFormat\(\'(.*?)\'/', $html, $matches)) {
                $row['so_nhan_vien'] = $matches[1][0];
            }
        }
        $row['so_nhan_vien'] = intval($row['so_nhan_vien']);

        $row['von_dieu_le'] = !empty($row['von_dieu_le']) ? html_entity_decode($row['von_dieu_le'], ENT_QUOTES, 'UTF-8') : '';
        $row['so_dien_thoai'] = !empty($row['so_dien_thoai']) ? html_entity_decode($row['so_dien_thoai'], ENT_QUOTES, 'UTF-8') : '';

        $row['so_fax'] = !empty($row['so_fax']) ? html_entity_decode($row['so_fax'], ENT_QUOTES, 'UTF-8') : '';

        $row['dia_chi'] = isset($row['dia_chi']) ? preg_replace('/&nbsp;+/u', '', trim($row['dia_chi'])) : '';
        $row['dia_chi_tru_so'] = !empty($row['dia_chi_tru_so']) ? html_entity_decode($row['dia_chi_tru_so'], ENT_QUOTES, 'UTF-8') : '';
        $row['dia_chi_giao_dich'] = !empty($row['dia_chi_giao_dich']) ? html_entity_decode($row['dia_chi_giao_dich'], ENT_QUOTES, 'UTF-8') : '';
        $row['dia_chi'] = (isset($row['dia_chi']) and !empty($row['dia_chi'])) ? html_entity_decode($row['dia_chi'], ENT_QUOTES, 'UTF-8') : (!empty($row['dia_chi_tru_so']) ? $row['dia_chi_tru_so'] : $row['dia_chi_giao_dich']);
        $row['dia_chi'] = mb_ereg_replace('[\s]+', ' ', $row['dia_chi']);
        $row['dia_chi'] = str_replace('Đakpơ', 'Đắk Pơ', $row['dia_chi']);
        $row['dia_chi'] = str_replace('Đắk Lắk', 'Đắc Lắc', $row['dia_chi']);
        $row['dia_chi'] = str_replace('-', ',', $row['dia_chi']);

        $row['tinh/_thanh_pho'] = !empty($row['tinh/_thanh_pho']) ? html_entity_decode($row['tinh/_thanh_pho'], ENT_QUOTES, 'UTF-8') : '';
        $row['tinh_/_thanh_pho'] = !empty($row['tinh_/_thanh_pho']) ? html_entity_decode($row['tinh_/_thanh_pho'], ENT_QUOTES, 'UTF-8') : $row['tinh/_thanh_pho'];
        $row['tinh_/_thanh_pho'] = preg_replace('/Đăk Lăk/', 'Đắc Lắc', $row['tinh_/_thanh_pho']);
        $row['tinh_/_thanh_pho'] = preg_replace('/Kạn/', 'Cạn', $row['tinh_/_thanh_pho']);
        $row['tinh_/_thanh_pho'] = preg_replace('/Thừa Thiên Huế/', 'Thừa Thiên - Huế', $row['tinh_/_thanh_pho']);
        $row['tinh_/_thanh_pho'] = preg_replace('/Bà Rịa Vũng Tàu/', 'Bà Rịa - Vũng Tàu', $row['tinh_/_thanh_pho']);
        $row['tinh_/_thanh_pho'] = str_replace('Bắc Kạn', 'Bắc Cạn', $row['tinh_/_thanh_pho']);

        $arr_diachi = get_address($row['dia_chi'], $row['tinh_/_thanh_pho'], $id);
        $diachi_phuong = $arr_diachi['diachi_phuong'];
        $diachi_quan = $arr_diachi['diachi_quan'];
        $diachi_tinh = $arr_diachi['diachi_tinh'];
        $diachi_sonha = $arr_diachi['diachi_sonha'] != '' ? $arr_diachi['diachi_sonha'] : '';

        // Lấy thông tin địa chỉ
        $_arr_phuong = explode('-', $diachi_phuong);
        $diachi_phuong = sizeof($_arr_phuong) > 1 ? $_arr_phuong[1] : $diachi_phuong;

        $diachi_quan = change_alias($diachi_quan);
        $diachi_phuong = change_alias($diachi_phuong);

        // Tác tất cả các chữ
        $string = $diachi_phuong;
        $arr_p = explode('-', $string);
        // Thêm % ở đầu mỗi chữ
        foreach ($arr_p as $key => $value) {
            $string = substr($string, strpos($string, $value), strlen($string));
            $search[] = str_replace($value, '%' . $value, $string);
        }

        $where = '';
        $result_diachi = [];
        $result = [];
        // Lọc nếu có kết quả thì dừng luôn,
        foreach ($search as $key => $value) {
            if (!empty($diachi_quan)) {
                $result = $db->query('SELECT tb1.*, tb2.title as name_tinh, tb3.title as name_district FROM nv4_vi_location_ward tb1 ' . ' INNER JOIN nv4_vi_location_province tb2 ON tb2.id = tb1.idprovince  INNER JOIN nv4_vi_location_district tb3 ON tb3.id = tb1.iddistrict ' . ' WHERE tb1.alias LIKE ' . $db->quote($value) . ' AND tb3.alias LIKE ' . $db->quote('%' . $diachi_quan . '%'))
                    ->fetchAll();
                if (!empty($result)) {
                    // Nếu trùng cả tên huyện và tên phường thì where like tiếp cái tỉnh
                    if (count($result) > 1) {
                        $result = $db->query('SELECT tb1.*, tb2.title as name_tinh, tb3.title as name_district FROM nv4_vi_location_ward tb1 ' . ' INNER JOIN nv4_vi_location_province tb2 ON tb2.id = tb1.idprovince  INNER JOIN nv4_vi_location_district tb3 ON tb3.id = tb1.iddistrict ' . ' WHERE tb1.alias LIKE ' . $db->quote($value) . ' AND tb3.alias LIKE ' . $db->quote('%' . $diachi_quan . '%') . ' AND tb2.title LIKE ' . $db->quote('%' . $diachi_tinh . '%'))
                            ->fetchAll();
                    }
                    break;
                }
            }
        }

        if (!empty($result)) {
            $result_diachi = $result[0];
        }

        // $sql_diachi = $db->query('SELECT * FROM nv4_vi_location_ward WHERE alias LIKE ' . $db->quote('%' . $diachi_phuong) . ' AND idprovince IN (SELECT id FROM nv4_vi_location_province WHERE title LIKE ' . $db->quote('%' . $diachi_tinh . '%') . ') AND iddistrict IN (SELECT id FROM nv4_vi_location_district WHERE alias LIKE ' . $db->quote('%' . $diachi_quan . '%') . ' )');
        // $result_diachi = $sql_diachi->fetch();

        if (!empty($result_diachi)) {
            $row['province'] = !empty($result_diachi['idprovince']) ? $result_diachi['idprovince'] : 0;
            $row['district'] = !empty($result_diachi['iddistrict']) ? $result_diachi['iddistrict'] : 0;
            $row['ward'] = !empty($result_diachi['id']) ? $result_diachi['id'] : 0;
        } else {
            $sql_diachi = $db->query('SELECT id as idprovince FROM nv4_vi_location_province WHERE title=' . $db->quote($diachi_tinh) . 'OR alias =' . $db->quote(change_alias($diachi_tinh)));
            $result_diachi = $sql_diachi->fetch();
            $row['province'] = !empty($result_diachi['idprovince']) ? $result_diachi['idprovince'] : 0;
            if (!empty($diachi_quan)) {
                $sql_diachi = $db->query('SELECT id as iddistrict FROM nv4_vi_location_district WHERE alias LIKE ' . $db->quote('%' . $diachi_quan . '%') . (!empty($row['province']) ? ' AND idprovince=' . intval($row['province']) : ''));
                $result_diachi = $sql_diachi->fetch();
            }
            $row['district'] = !empty($result_diachi['iddistrict']) ? $result_diachi['iddistrict'] : 0;
            if (!empty($diachi_phuong)) {
                $sql_diachi = $db->query('SELECT id as idward, iddistrict, idprovince FROM nv4_vi_location_ward WHERE alias LIKE ' . $db->quote('%' . $diachi_phuong . '%') . (!empty($row['district']) ? ' AND iddistrict=' . intval($row['district']) : '') . (!empty($row['province']) ? ' AND idprovince=' . intval($row['province']) : ''));
                $result_diachi = $sql_diachi->fetch();
            }
            $row['ward'] = !empty($result_diachi['idward']) ? $result_diachi['idward'] : 0;
            if (!empty($row['ward']) && (empty($row['district']) || empty($row['province']))) {
                $row['district'] = $result_diachi['iddistrict'];
                $row['province'] = $result_diachi['idprovince'];
            }
            if (empty($row['ward']) && !empty($arr_diachi['diachi_phuong']) && strpos(mb_strtolower(nv_compound_unicode($diachi_sonha)), trim($arr_diachi['diachi_phuong'])) === false && strpos(trim($arr_diachi['diachi_quan']), trim($arr_diachi['diachi_phuong'])) === false) {
                $diachi_sonha .= (!empty($diachi_sonha) ? ', ' : '') . trim($arr_diachi['diachi_phuong']);
            }
        }
        $row['loai_hinh_doanh_nghiep'] = !empty($row['loai_hinh_doanh_nghiep']) ? html_entity_decode($row['loai_hinh_doanh_nghiep'], ENT_QUOTES, 'UTF-8') : '';
        $row['phan_loai_doanh_nghiep'] = !empty($row['phan_loai_doanh_nghiep']) ? html_entity_decode($row['phan_loai_doanh_nghiep'], ENT_QUOTES, 'UTF-8') : $row['loai_hinh_doanh_nghiep'];
        $row['hop_dong'] = !empty($row['hop_dong']) ? html_entity_decode($row['hop_dong'], ENT_QUOTES, 'UTF-8') : '';
        $row['bao_cao_tai_chinh'] = !empty($row['bao_cao_tai_chinh']) ? html_entity_decode($row['bao_cao_tai_chinh'], ENT_QUOTES, 'UTF-8') : '';
        $result_loaidn = $db->query('SELECT id,title FROM ' . NV_PREFIXLANG . '_businesslistings_businesstype');
        $businesstype = 0;
        if ($row['phan_loai_doanh_nghiep'] == 'Công ty 100% vốn nước') {
            $row['phan_loai_doanh_nghiep'] = 'Công ty 100% vốn nước ngoài';
        }
        $row['phan_loai_doanh_nghiep'] = preg_replace('/100%/', '100&#x25;', $row['phan_loai_doanh_nghiep']);
        while ($row_loaidn = $result_loaidn->fetch()) {
            if (change_alias(mb_strtolower($row['phan_loai_doanh_nghiep'])) == change_alias(mb_strtolower($row_loaidn['title']))) {
                $businesstype = $row_loaidn['id'];
                break;
            }
        }

        if (empty($row['phan_loai_doanh_nghiep'])) {
            $row['businesstype'] = 0;
        } elseif ($businesstype == 0) {
            print_r("đã xảy ra lỗi:\n");
            print_r($row);
            print_r('<br/>-----id old lỗi:' . $id . '-------<br/>');
            $db->query("UPDATE " . BUSINESS_PREFIX_GLOBAL . "_old_file SET url_run='-3', crawls_info=" . $dbcr->quote($crawls_info . "; Error businesstype=0") . " WHERE id=" . $id);
            return false;
            // die('dừng');
        } else {
            $row['businesstype'] = $businesstype;
        }

        // Lấy code
        if (empty($row['so_dkkd'])) {
            $result = $db->query("SHOW TABLE STATUS WHERE Name='" . BUSINESS_PREFIX_GLOBAL . "_old'");
            $item = $result->fetch();
            $row['code'] = vsprintf("C%06s", $item['auto_increment']);
        } else {
            $row['code'] = $row['so_dkkd'];
        }

        $row['trang_web'] = !empty($row['trang_web']) ? html_entity_decode($row['trang_web'], ENT_QUOTES, 'UTF-8') : '';
        $row['quoc_gia'] = !empty($row['quoc_gia']) ? html_entity_decode($row['quoc_gia'], ENT_QUOTES, 'UTF-8') : '';
        $row['email'] = !empty($row['email']) ? html_entity_decode($row['email'], ENT_QUOTES, 'UTF-8') : '';

        $_string = $row['ten_nha_thau'];
        $_string = change_alias($_string);
        $_string = str_replace("-", " ", $_string);
        $_string = mb_strtolower($_string);

        $_string = str_replace("tong cong ty", "COMNAME", $_string);
        $_string = str_replace("cn cong ty", "COMNAME", $_string);
        $_string = str_replace("cong ty cp", "COMNAME", $_string);
        $_string = str_replace("cong ty co phan", "COMNAME", $_string);
        $_string = str_replace("cong ty tnhh", "COMNAME", $_string);
        $_string = str_replace("cong ty trach nhiem huu han", "COMNAME", $_string);
        $_string = str_replace("cong ty mtv", "COMNAME", $_string);
        $_string = str_replace("cong ty mot thanh vien", "COMNAME", $_string);
        $_string = str_replace("cong ty", "COMNAME", $_string);
        $_string = str_replace("cong rty", "COMNAME", $_string);
        $_string = str_replace("c.ty", "COMNAME", $_string);
        $_string = str_replace("vien", "COMNAME", $_string);
        $_string = str_replace("doanh nghiep tu nhan", "COMNAME", $_string);
        $_string = str_replace("doanh nghiep", "COMNAME", $_string);
        $_string = str_replace("xi nghiep tu nhan", "COMNAME", $_string);
        $_string = str_replace("xi nghiep", "COMNAME", $_string);

        $_string = nv_compound_unicode($_string);
        $diachi_sonha = nv_compound_unicode($diachi_sonha);
        $row = nv_compound_unicode_recursion($row);
        $array_industry = nv_compound_unicode_recursion($array_industry);

        try {
            $count = 0;
            $count = $db->query('SELECT id FROM '. BUSINESS_PREFIX_GLOBAL . '_old WHERE code = ' . $db->quote($row['code']))
                ->fetchcolumn();
            $update_data = $count > 0 ? 1 : 0;
            if ($update_data == 0) {
                $stmt = $db->prepare("INSERT INTO " . BUSINESS_PREFIX_GLOBAL . "_old (code, userid, industry1, industry2, industry3, industry4, industry5, province, district, ward, address, trading_address, businesstype, companyname, name_search, officialname, phone, fax, email,website, dateestablished, currentstatus, active, about, elasticsearch, linh_vuc_kinh_doanh, so_nhan_vien, von_dieu_le, thong_tin_nganh_nghe, hop_dong, bao_cao_tai_chinh, time_crawler, name_staff, nation, so_dkkd) VALUES (:code, :userid, :industry1, :industry2, :industry3, :industry4, :industry5, :province, :district, :ward, :address, :trading_address, :businesstype, :companyname, :name_search, :officialname, :phone, :fax, :email, :website, :dateestablished, 1, 1, '', 0, :linh_vuc_kinh_doanh, :so_nhan_vien, :von_dieu_le, :thong_tin_nganh_nghe, :hop_dong, :bao_cao_tai_chinh, :time_crawler, :name_staff, :nation, :so_dkkd)");
                $stmt->bindParam(':code', $row['code'], PDO::PARAM_STR);
                $stmt->bindValue(':userid', 1, PDO::PARAM_INT);
                $stmt->bindParam(':so_dkkd', $row['so_dkkd'], PDO::PARAM_STR);
            } else {
                $row_old = $db->query('SELECT * FROM ' . BUSINESS_PREFIX_GLOBAL . '_old WHERE id=' . $count)->fetch();
                $stmt = $db->prepare("UPDATE " . BUSINESS_PREFIX_GLOBAL . "_old SET industry1=:industry1, industry2=:industry2, industry3=:industry3, industry4=:industry4, industry5=:industry5, province=:province, district=:district, ward=:ward, address=:address, trading_address=:trading_address, businesstype=:businesstype, companyname=:companyname, name_search=:name_search, officialname=:officialname, phone=:phone, fax=:fax, email=:email,website=:website, dateestablished=:dateestablished, elasticsearch=0, update_data = 0, linh_vuc_kinh_doanh = :linh_vuc_kinh_doanh, so_nhan_vien = :so_nhan_vien, von_dieu_le = :von_dieu_le, thong_tin_nganh_nghe = :thong_tin_nganh_nghe, hop_dong = :hop_dong, bao_cao_tai_chinh = :bao_cao_tai_chinh, time_crawler = :time_crawler, name_staff = :name_staff, nation = :nation WHERE id=" . $count);
            }

            $stmt->bindParam(':industry1', $array_industry['data_insert1'], PDO::PARAM_STR);
            $stmt->bindParam(':industry2', $array_industry['data_insert2'], PDO::PARAM_STR);
            $stmt->bindParam(':industry3', $array_industry['data_insert3'], PDO::PARAM_STR);
            $stmt->bindParam(':industry4', $array_industry['data_insert4'], PDO::PARAM_STR);
            $stmt->bindParam(':industry5', $array_industry['data_insert5'], PDO::PARAM_STR);
            $stmt->bindParam(':province', $row['province'], PDO::PARAM_STR);
            $stmt->bindParam(':district', $row['district'], PDO::PARAM_STR);
            $stmt->bindParam(':ward', $row['ward'], PDO::PARAM_STR);
            $stmt->bindParam(':address', $diachi_sonha, PDO::PARAM_STR);
            $stmt->bindParam(':trading_address', $row['dia_chi_giao_dich'], PDO::PARAM_STR);
            $stmt->bindParam(':businesstype', $row['businesstype'], PDO::PARAM_STR);
            $stmt->bindParam(':companyname', $row['ten_nha_thau'], PDO::PARAM_STR);
            $stmt->bindParam(':name_search', $_string, PDO::PARAM_STR);
            $stmt->bindParam(':officialname', $row['ten_tieng_anh'], PDO::PARAM_STR);
            $stmt->bindParam(':phone', $row['so_dien_thoai'], PDO::PARAM_STR);
            $stmt->bindParam(':fax', $row['so_fax'], PDO::PARAM_STR);
            $stmt->bindParam(':email', $row['email'], PDO::PARAM_STR);
            $stmt->bindParam(':website', $row['trang_web'], PDO::PARAM_STR);
            $stmt->bindParam(':dateestablished', $row['ngay_dkkd'], PDO::PARAM_STR);
            $stmt->bindParam(':linh_vuc_kinh_doanh', $row['linh_vuc_kinh_doanh'], PDO::PARAM_STR);
            $stmt->bindParam(':so_nhan_vien', $row['so_nhan_vien'], PDO::PARAM_STR);
            $stmt->bindParam(':von_dieu_le', $row['von_dieu_le'], PDO::PARAM_STR);
            $stmt->bindParam(':thong_tin_nganh_nghe', $row['thong_tin_nganh_nghe'], PDO::PARAM_STR);
            $stmt->bindParam(':hop_dong', $row['hop_dong'], PDO::PARAM_STR);
            $stmt->bindParam(':bao_cao_tai_chinh', $row['bao_cao_tai_chinh'], PDO::PARAM_STR);
            $stmt->bindValue(':time_crawler', NV_CURRENTTIME, PDO::PARAM_INT);
            $name_staff = $array_name_staff[array_rand($array_name_staff)];
            $stmt->bindParam(':name_staff', $name_staff, PDO::PARAM_STR);
            $stmt->bindParam(':nation', $row['quoc_gia'], PDO::PARAM_STR);

            $exc = $stmt->execute();
            if ($update_data == 0) {
                $count = $db->lastInsertId();
            }

            // Nếu không có xã|phường trong CSDL thì update trạng thái bằng -6
            if ($row['ward'] == 0 || $row['district'] == 0 || $row['province'] == 0) {
                $note = 'Lỗi: ';
                if ($row['ward'] == 0) {
                    print_r("\nKhông tìm thấy Xã|Phường(" . $arr_diachi['diachi_phuong'] . ") trong CSDL id = " . $id . "\n" . "ID old = " . $id);
                    $note .= 'Xã|Phường: ' . $arr_diachi['diachi_phuong'];
                }

                if ($row['district'] == 0) {
                    print_r("\nKhông tìm thấy Quận|Huyện(" . $arr_diachi['diachi_quan'] . ") trong CSDL id = " . $id . "\n" . "ID old = " . $id);
                    $note .= 'Quận|Huyện: ' . $arr_diachi['diachi_quan'];
                }

                if ($row['province'] == 0) {
                    print_r("\nKhông tìm thấy Tỉnh|TP(" . $arr_diachi['diachi_tinh'] . ") trong CSDL id = " . $id . "\n" . "id = " . $id);
                    $note .= 'Tỉnh|TP: ' . $arr_diachi['diachi_tinh'];
                }
                $db->query("UPDATE " . BUSINESS_PREFIX_GLOBAL . "_old_file SET url_run = -6, crawls_info = " . $dbcr->quote($note) . " WHERE id = " . $id);
            } else {
                $db->query('UPDATE ' . BUSINESS_PREFIX_GLOBAL . '_old_file SET url_run=1 WHERE id=' . $id);
            }
        } catch (Exception $e) {
            print_r($e);
            $db->query("UPDATE " . BUSINESS_PREFIX_GLOBAL . "_old_file SET url_run='-4', crawls_info=" . $dbcr->quote($crawls_info . "; " . print_r($e, true)) . " WHERE id=" . $id);
            return false;
        }

        print_r("\nfile da lay xong:" . $row['code'] . "\n\n");
    } else {
        $db->query("UPDATE " . BUSINESS_PREFIX_GLOBAL . "_old_file SET url_run='-5', crawls_info='Lỗi dữ liệu gốc' WHERE id=" . $id);
        return false;
    }
}

function get_address($dia_chi, $_tinh_thanh_pho, $id = 0)
{
    global $db, $dbcr;
    $arr_diachi_2 = explode('.', $dia_chi);
    $arr_diachi = [];

    if (sizeof($arr_diachi_2) > 3) {
        // $dia_chi = str_replace('.', ',', $dia_chi);
        $dia_chi = preg_replace('/TP\,|Tp\,/', '', $dia_chi);
        $dia_chi = str_replace('Tp,HCM', 'TP.Hồ Chí Minh', $dia_chi);
        $dia_chi = str_replace('Tx,', 'Thị xã', $dia_chi);
        $dia_chi = str_replace('Q,', 'Quận', $dia_chi);

        $dia_chi = str_replace('-p,', ',Phường ', $dia_chi);
        $dia_chi = str_replace('-tx', ',Thị xã ', $dia_chi);
        $dia_chi = str_replace('-t,', ',tỉnh ', $dia_chi);
        $arr_diachi = explode(',', $dia_chi);
    }

    if (preg_match('/tỉnh|Tỉnh|thành phố|Thành phố/', trim($_tinh_thanh_pho), $s)) {
        $_tinh_thanh_pho = trim(str_replace($s[0], '', $_tinh_thanh_pho));
        $_tinh_thanh_pho = ($_tinh_thanh_pho == 'Hồ Chí Minh') ? 'TP.Hồ Chí Minh' : $_tinh_thanh_pho;
    }
    $diachi_tinh = trim($_tinh_thanh_pho);
    $dia_chi = nv_compound_unicode($dia_chi);
    if ($dia_chi == 'Số 10, tổ dân phố Ninh Hải 4') {
        $dia_chi = 'số 10, tổ dân phố ninh hải 4, phường anh dũng, quận dương kinh';
    }
    $dia_chi = preg_replace("/[\s]*việt[\s]*nam[\s]*$/iu", "", mb_strtolower($dia_chi));
    $dia_chi = str_replace('việt nam', '', mb_strtolower($dia_chi));
    $dia_chi = str_replace('hcm', 'hồ chí minh', mb_strtolower($dia_chi));
    $dia_chi = str_replace('bắc kạn', 'bắc cạn', mb_strtolower($dia_chi));
    $dia_chi = str_replace('tx. thủ dầu một', 'thành phố thủ dầu một', mb_strtolower($dia_chi));

    $dia_chi = trim(preg_replace('/hn/', 'hà nội', mb_strtolower($dia_chi)));
    $dia_chi = trim(preg_replace('/đắk rlấp/', 'đắk R\'Lấp', mb_strtolower($dia_chi)));
    $dia_chi = trim(preg_replace('/eahleo|ea hleo/', 'ea h\'leo', mb_strtolower($dia_chi)));
    $dia_chi = trim(preg_replace('/earal/', 'ea ral', mb_strtolower($dia_chi)));
    $dia_chi = trim(preg_replace('/eadrăng/', 'ea drăng', mb_strtolower($dia_chi)));
    $dia_chi = trim(preg_replace('/sadec/', 'sa đéc', mb_strtolower($dia_chi)));
    $dia_chi = trim(preg_replace('/đăk lăk/', ' đắc lắc ', mb_strtolower($dia_chi)));
    $dia_chi = trim(preg_replace('/bắc kạn/', 'bắc cạn', mb_strtolower($dia_chi)));
    $dia_chi = trim(preg_replace('/bmt/', ' buôn ma thuột ', mb_strtolower($dia_chi)));
    $dia_chi = trim(preg_replace('/thành phố tam điệp/', 'thị xã tam điệp', mb_strtolower($dia_chi)));
    $dia_chi = trim(preg_replace('/thị xã đông triều/', 'huyện đông triều', mb_strtolower($dia_chi)));
    $dia_chi = trim(preg_replace('/t.t.huế/', ' thừa thiên huế ', mb_strtolower($dia_chi)));
    $dia_chi = preg_replace('/kp\./', ' khu phố ', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/[\s]+tt\.[\s]+|thi tran/', ' thị trấn ', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/[\s]+h\.[\s]+|huỵen/', ' huyện ', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/[\s]+tx[\s]+/', ' thị xã ', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/t\.p/', 'thành phố', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/[\s]+t\.|[\s]+t[\s]+|tinihr/', ' tỉnh ', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/tp\,hcm|tp\.hồ chí minh|tp\. hồ chí minh.|tphồ chí minh/', 'thành phố hồ chí minh', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/[\s]+tp\,[\s]+|[\s]+tp\./', ' thành phố ', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/[\s]+q[\s]+|[\s]+q\.|q\.|[\s]+q\,|quân/', ' quận ', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/lạc long  quận/', 'lạc long quân', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/[\s]+p\.|[\s]+p\.|\-p\,/', ' phường ', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/bà rịa, vũng tàu|brvt/', ' bà rịa - vũng tàu ', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/mdrak/', 'm\'drắk', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/bút sơn/', 'hoằng hóa', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/plei kần/', 'plei cần', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/nông trường mộc châu/', 'nt mộc châu', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/thị trấn xuân hòa/', 'xã xuân hòa', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/quận bình thạch/', 'quận bình thạnh', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/tĩnh quảng nam/', 'tỉnh quảng nam', mb_strtolower($dia_chi));
    for ($i = 1 ;$i <= 12; $i++) {
        $dia_chi = preg_replace('/q' . $i . '|q\.\s*' . $i . '|quận 0' . $i . '/iu', 'quận ' . $i, mb_strtolower($dia_chi));
    }
    for ($i = 1 ;$i <= 27; $i++) {
        $dia_chi = preg_replace('/p' . $i . '|p\.\s*' . $i . '|phường 0' . $i . '/iu', 'phường ' . $i, mb_strtolower($dia_chi));
    }

    if (strpos(change_alias($dia_chi), change_alias(mb_strtolower('hồ chí minh'))) === false) {
        $dia_chi = trim(preg_replace('/tp/', 'thành phố', $dia_chi));
    } else {
        $dia_chi = trim(preg_replace('/tphồ chí minh/', 'tp.hồ chí minh', $dia_chi));
    }

    $_tmp_dia_chi = $dia_chi;
    $dia_chi = str_replace(',', ' ', $dia_chi);
    $dia_chi = mb_ereg_replace('[\s]+', ' ', $dia_chi);
    if ($dia_chi == 'số 10  tổ dân phố ninh hải 4') {
        $diachi_quan = 'Dương Kinh ';
        $diachi_phuong = 'Anh Dũng';
        $diachi_sonha = $dia_chi;
    } elseif ($dia_chi == 'lầu 20 tòa nhà a') {
        $diachi_quan = 'Quận 5';
        $diachi_phuong = 'Phường 1';
        $diachi_sonha = $dia_chi;
    } elseif (preg_match('/(.*)(thị trấn|[\s]+phường|\sp\.|xã)(.+)(\sq\.|\sq\.1|tx|thị xã|quận|huyên|huyện|\sh\.|thành phố|\stp\.|\stp\s)(.+)(\st\.|thành phố\.|thành phố|tinh|tỉnh|\stp\.|\stp\s)(.+)/iu', mb_strtolower($dia_chi), $m)) {
        $m[4] = str_replace('tx', 'thị xã', $m[4]);
        $m[3] = str_replace('khối quán hành', 'quán hành', $m[3]);
        $_ckeckzero = explode('0', $m[5]);
        if (is_int($_ckeckzero[sizeof($_ckeckzero) - 1])) {
            $db->query("UPDATE " . BUSINESS_PREFIX_GLOBAL . "_old_file SET url_run='-2', crawls_info+=" . $dbcr->quote("; Error 205") . " WHERE id=" . $id);
            echo "Error 205";
            return false;
            // die('dừng đây');
            $m[5] = str_replace('0', '', $m[5]);
        }

        $m[5] = str_replace('nho quang ', 'nho quan', $m[5]);
        if ($m[2] == 'quận' || trim($m[4]) == 'quận') {
            $diachi_quan = str_replace('q.', 'quận', $m[4]) . ' ' . $m[5];
        } else {
            $diachi_quan = $m[5];
        }

        $diachi_quan = mb_ereg_replace('[\s]+', ' ', $diachi_quan);
        $diachi_quan = str_replace('thị xã thủ dầu một', 'thành phố thủ dầu một', $diachi_quan);

        $diachi_phuong = str_replace('p.', 'phường', $m[2]) . ' ' . $m[3];
        $diachi_sonha = trim($m[1]);
        $_check = $db->query('SELECT COUNT(*) FROM nv4_vi_location_district WHERE idprovince IN (SELECT id FROM nv4_vi_location_province WHERE title = ' . $db->quote($diachi_tinh) . ') AND alias LIKE ' . $db->quote('%' . change_alias(trim($diachi_quan))))
            ->fetchcolumn();
        if ($_check == 0) {
            $diachi_tinh = trim($m[7]);
            if (strpos(mb_strtolower($diachi_tinh), 'hồ chí minh') !== false) {
                $diachi_tinh = 'tp.hồ chí minh';
            }
        }

        if (!empty($arr_diachi)) {
            if ((trim($arr_diachi[sizeof($arr_diachi) - 2]) == 'Thành phố Hồ Chí Minh' || strpos(mb_strtolower(trim($m[7])), 'hcm') !== false) && $diachi_tinh == 'Hà Nội') {
                $diachi_tinh = 'TP.Hồ Chí Minh';
            }
        }
    } else if (preg_match('/(.*)(\stx\.|\stx\s|thị xã|thị trấn|quận|huyện|\sh\.|\sq\.|thành phố|\stp\.|\stp\s)(.+)(thành phố|tinh|tỉnh|\stp\.|\stp\s)(.+)/iu', mb_strtolower($dia_chi), $m)) {
        if ($m[2] == 'quận') {
            $diachi_quan = $m[2] . ' ' . $m[3];
        } else {
            $diachi_quan = $m[3];
        }
        $_ckeckzero = explode('0', $diachi_quan);
        if ((int) $_ckeckzero[sizeof($_ckeckzero) - 1]) {
            $diachi_quan = str_replace('0', '', $diachi_quan);
        }
        $_ckeckzero = explode('phường', $m[1]);
        if (sizeof($_ckeckzero) > 1) {
            $diachi_phuong = 'phường ' . $_ckeckzero[1];
        } else {
            $diachi_phuong = $m[1];
        }

        $diachi_sonha = trim($m[1]);
        $_check = $db->query('SELECT COUNT(*) FROM nv4_vi_location_district WHERE idprovince IN (SELECT id FROM nv4_vi_location_province WHERE title = ' . $db->quote($diachi_tinh) . ') AND alias LIKE ' . $db->quote('%' . change_alias(trim($m[3]))))
            ->fetchcolumn();
        if ($_check == 0) {
            $diachi_tinh = trim($m[5]);
            if (strpos(mb_strtolower($diachi_tinh), 'hồ chí minh') !== false) {
                $diachi_tinh = 'tp.hồ chí minh';
            }
        }
    } else if (preg_match('/(.*)(\sp\.|phuong|phường|xã|thị trấn)(.+)(\stp\.*|thanh pho|\sq\.|\stx\.|\stx|thị xã|quận|huyện|\sh\.|\stp\.|thành phố)(.+)/iu', mb_strtolower($dia_chi), $m)) {
        // lầu 5 tòa nhà tân hoàng long 25/68 nguyễn bỉnh khiêm p.bến nghé q.1 thành phố hồ chí minh
        $m[5] = trim(preg_replace('/thành phố/', '', $m[5]));
        $m[5] = trim(preg_replace('/tỉnh bà rịa vũng tàu/', 'bà rịa - vũng tàu', $m[5]));
        if (trim($m[5]) == 'vĩnh yên vĩnh phúc') {
            $m[5] = 'vĩnh yên';
            $diachi_tinh = 'vĩnh phúc';
        }
        $m[4] = preg_replace('/\stx\.|\stx/', 'thị xã', $m[4]);

        $diachi_quan = '';
        if (strpos(mb_strtolower(trim($m[5])), 'hồ chí minh') === false and strpos(mb_strtolower(trim($m[5])), 'hà nội') === false) {
            if (strpos(change_alias($m[5]), change_alias('tỉnh')) !== false) {
                $diachi_quan = str_replace('q.', 'quận', $m[4]) . ' ' . str_replace('tỉnh ' . mb_strtolower($diachi_tinh), '', trim($m[5]));
                $diachi_quan = str_replace('q.', 'quận', $m[4]) . ' ' . str_replace('tỉnh', '', $m[5]);
            } else {
                $diachi_quan = str_replace('q.', 'quận', $m[4]) . ' ' . str_replace('-' . mb_strtolower(change_alias($diachi_tinh)), '', change_alias($m[5]));
            }
        } elseif (trim($m[3]) == '11 phú nhuận') {
            $diachi_quan = 'phú nhuận';
            $diachi_phuong = 'phường 11';
        } elseif (trim($m[3]) == 'trung mỹ tây') {
            $diachi_quan = 'quận 12';
            $diachi_phuong = 'trung mỹ tây';
        } elseif (trim($m[3]) == 'bình trị đông b bình tân') {
            $diachi_quan = 'bình tân';
            $diachi_phuong = 'bình trị đông b';
        } elseif (trim($m[3]) == 'linh xuân thủ đức') {
            $diachi_quan = 'thủ đức';
            $diachi_phuong = 'linh xuân';
        } elseif (trim($m[3]) == 'bình hưng bình chánh') {
            $diachi_quan = 'bình chánh';
            $diachi_phuong = 'bình hưng';
        } else {
            $m[5] = trim(preg_replace('/thành phố hồ chí minh|hồ chí minh|tp.hồ chí minh|thành phố hà nội/', '', $m[5]));
            $diachi_quan = str_replace('q.', 'quận', $m[4]) . ' ' . str_replace('-' . mb_strtolower(change_alias($diachi_tinh)), '', change_alias($m[5]));
            $diachi_quan = trim(preg_replace('/thành phố hồ chí minh|hồ chí minh|tp.hồ chí minh|thành phố hà nội|thành phố/', '', $diachi_quan));
        }
        $diachi_quan = str_replace('thị xã thủ dầu một', 'thành phố thủ dầu một', $diachi_quan);
        $diachi_quan = str_replace('thành phố bac-can', 'thị xã bac-can', $diachi_quan);

        $diachi_phuong = str_replace('p.', 'phường', $m[2]) . ' ' . (!empty($diachi_phuong) ? $diachi_phuong : trim($m[3]));
        $diachi_sonha = trim($m[1]);
    } else {
        $_tmp_dia_chi = str_replace('–', ',', $_tmp_dia_chi);
        $_tmp_dia_chi = preg_replace('/(thành phố[\s]+hà nội|hà nội)$/', '', $_tmp_dia_chi);
        $_tmp_dia_chi = trim($_tmp_dia_chi);
        $_tmp_dia_chi = trim($_tmp_dia_chi, ',');
        $_arr_diachi_tmp = explode(',', $_tmp_dia_chi);
        if ($_arr_diachi_tmp[sizeof($_arr_diachi_tmp) - 1] == '') {
            unset($_arr_diachi_tmp[sizeof($_arr_diachi_tmp) - 1]);
            $_arr_diachi_tmp = array_values($_arr_diachi_tmp);
        }

        $_tmp_quan = $_arr_diachi_tmp[sizeof($_arr_diachi_tmp) - 1];

        if (preg_match('/tỉnh|thành phố/', mb_strtolower($_tinh_thanh_pho), $s) and sizeof($_arr_diachi_tmp) > 1) {
            if (strpos($_arr_diachi_tmp[sizeof($_arr_diachi_tmp) - 1], $s[0]) === false) {
                $_arr_diachi_tmp[sizeof($_arr_diachi_tmp) - 1] = $s[0] . ' ' . trim($_arr_diachi_tmp[sizeof($_arr_diachi_tmp) - 1]);
            }
        }
        if (strpos(change_alias($_arr_diachi_tmp[sizeof($_arr_diachi_tmp) - 1]), change_alias('hồ chí minh')) !== false) {
            $_arr_diachi_tmp[sizeof($_arr_diachi_tmp) - 1] = 'tp.hồ chí minh';
        }
        if (strpos(mb_strtolower($_tinh_thanh_pho), 'hồ chí minh') !== false) {
            $_tinh_thanh_pho = 'tp.hồ chí minh';
        }

        if (change_alias($_tmp_dia_chi) == change_alias('lô 36/23 khu đô thị hoà vượng, lộc hoà, nam định')) {
            $diachi_quan = 'nam định';
            $diachi_phuong = 'xã lộc hoà';
            $diachi_sonha = 'lô 36/23 khu đô thị hoà vượng';
        } elseif (change_alias($_arr_diachi_tmp[0]) == change_alias('xã cổ đông sơn tây')) {
            $diachi_quan = 'sơn tây';
            $diachi_phuong = 'xã cổ đông';
            $diachi_sonha = '';
        } elseif (strpos(change_alias($_arr_diachi_tmp[sizeof($_arr_diachi_tmp) - 1]), change_alias(mb_strtolower($_tinh_thanh_pho))) !== false || change_alias($_arr_diachi_tmp[sizeof($_arr_diachi_tmp) - 1]) == 'ha-noi') {
            $diachi_quan = $_arr_diachi_tmp[sizeof($_arr_diachi_tmp) - 2];
            if (change_alias($diachi_quan) == 'q3') {
                $diachi_quan = 'quận 3';
            }

            $diachi_quan = str_replace('q.', 'quận ', $diachi_quan);
            // $diachi_quan = trim(preg_replace('/[\s]+q/', 'quan-', $diachi_quan));
            $diachi_quan = trim(preg_replace('/q-/', 'quan-', change_alias($diachi_quan)));

            $diachi_phuong = !empty($_arr_diachi_tmp[sizeof($_arr_diachi_tmp) - 3]) ? $_arr_diachi_tmp[sizeof($_arr_diachi_tmp) - 3] : '';
            if (change_alias($diachi_phuong) == 'p6') {
                $diachi_phuong = 'phường 6';
            } else if (change_alias($diachi_phuong) == 'p4') {
                $diachi_phuong = 'phường 4';
            }

            if (strpos(change_alias($diachi_phuong), '-p-') !== false) {
                $diachi_phuong = trim(preg_replace('/p-|[\s]+p/', 'phuong-', change_alias($diachi_phuong)));
            }

            array_splice($_arr_diachi_tmp, sizeof($arr_diachi) - 3);
            $diachi_sonha = sizeof($_arr_diachi_tmp) > 1 ? implode(',', $_arr_diachi_tmp) : (!empty($_arr_diachi_tmp[0]) ? $_arr_diachi_tmp[0] : '');
        } else {
            $diachi_quan = trim($_tmp_quan);
            $diachi_phuong = trim($_arr_diachi_tmp[sizeof($_arr_diachi_tmp) - 2]);
            array_splice($_arr_diachi_tmp, sizeof($arr_diachi) - 2);
            $diachi_sonha = sizeof($_arr_diachi_tmp) > 1 ? implode(',', $_arr_diachi_tmp) : $_arr_diachi_tmp[0];
        }
        $_ckeckzero = explode('p', $diachi_phuong);
        if ((int) $_ckeckzero[sizeof($_ckeckzero) - 1]) {
            $diachi_sonha .= (!empty($diachi_sonha) ? ', ' : '') . $diachi_phuong;
            $diachi_phuong = str_replace('p', 'phường', $diachi_quan);
        }
        $_ckeckzero = explode('p', $diachi_phuong);
        if ((int) $_ckeckzero[sizeof($_ckeckzero) - 1]) {
            $diachi_phuong = str_replace('p', 'phường', $diachi_phuong);
        }
    }
    $arr_diachi = array();
    $arr_diachi['diachi_phuong'] = $diachi_phuong;
    $arr_diachi['diachi_quan'] = $diachi_quan;
    $arr_diachi['diachi_sonha'] = $diachi_sonha;
    $arr_diachi['diachi_tinh'] = $diachi_tinh;
    return $arr_diachi;
}
