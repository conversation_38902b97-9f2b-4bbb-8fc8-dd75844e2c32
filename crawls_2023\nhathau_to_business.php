<?php

// <PERSON><PERSON><PERSON><PERSON> nhà thầu được phê duyệt vào danh sách nhà thầu của dauthau.info
// https://muasamcong.mpi.gov.vn/web/guest/approved-contractors-list

// Fix lại logic bỏ phần sử dụng dữ liệu detail1:
// "status": 1,
// "effRoleDate": "2022-04-14",
// "startPendingDate": null
// "orgFullname": null -> đánh dấu url_run = -2

// 21/04/2023 Fix lại logic thêm dữ liệu
// "effRoleDate": [
//     2023,
//     4,
//     10,
//     9,
//     59,
//     10
// ],
// "startPendingDate": "2023-04-01"
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
require NV_ROOTDIR . '/functions/call_api.php';

define('CONTRACTOR_TABLE', 'nv22_nhathau_url');
define('BUSINESS_TABLE', BUSINESS_PREFIX_GLOBAL . '_info');
define('BUSINESS_TYPE',  NV_PREFIXLANG . '_businesslistings_businesstype');

// $business_type_json = '[{"code":"LLC2","name":"Công ty trách nhiệm hữu hạn hai thành viên trở lên","categoryTypeCode":"BUSINESS_TYPE"},{"code":"LLC1","name":"Công ty trách nhiệm hữu hạn một thành viên","categoryTypeCode":"BUSINESS_TYPE"},{"code":"NON_BUSINESS_UNIT","name":"Đơn vị hành chính, đơn vị sự nghiệp","categoryTypeCode":"BUSINESS_TYPE"},{"code":"LEGAL_TYPE_COOP","name":"Hợp tác xã","categoryTypeCode":"BUSINESS_TYPE"},{"code":"ECONOMY_ORG","name":"Tổ chức kinh tế của tổ chức chính trị, xã hội","categoryTypeCode":"BUSINESS_TYPE"},{"code":"LEGAL_TYPE_OTHER","name":"Loại hình khác","categoryTypeCode":"BUSINESS_TYPE"},{"code":"BR","name":"Chi nhánh","categoryTypeCode":"BUSINESS_TYPE"},{"code":"PS","name":"Công ty hợp danh","categoryTypeCode":"BUSINESS_TYPE"},{"code":"PE","name":"Doanh nghiệp tư nhân","categoryTypeCode":"BUSINESS_TYPE"},{"code":"RO","name":"Văn phòng đại diện","categoryTypeCode":"BUSINESS_TYPE"},{"code":"BL","name":"Địa điểm kinh doanh","categoryTypeCode":"BUSINESS_TYPE"},{"code":"SC","name":"Công ty cổ phần","categoryTypeCode":"BUSINESS_TYPE"}]';
// $business_type = json_decode($business_type_json, true);

// lấy thông tin business_type
$business_query = $db->query('SELECT * FROM ' . BUSINESS_TYPE);
$business_type = [];
while ($temp = $business_query->fetch()) {
    $business_type[$temp['code']] = $temp;
}

// lấy thông tin contractor
$contractor_info = [];
$contractor_list = [];
$uniqid = uniqid('', true);
$exec = $dbcr->exec("UPDATE nv22_nhathau_url SET business_id = -1, url_run=" . NV_CURRENTTIME . ", uniqid = " . $dbcr->quote($uniqid) . "  WHERE uniqid = '' and business_id = 0 AND url_run > 100 ORDER BY id ASC LIMIT 100");
echo "exec: " . $exec . "\n";
$contractor_list = $dbcr->query('SELECT * FROM ' . CONTRACTOR_TABLE . ' WHERE  url_run=' . NV_CURRENTTIME . ' AND uniqid = ' . $dbcr->quote($uniqid));

if (!empty($contractor_list)) {
    while ($contractor_info = $contractor_list->fetch()) {
        // xử lý dữ liệu đầu vào
        $error = [];
        $err_code = [];
        if (!empty($contractor_info)) {
            print_r("\n\n -------- Bat dau convert contractor id: " . $contractor_info['id'] . "\n");
            // detail
            $detail2 = [];

            $detail1 = !empty($contractor_info['detail1']) ? json_decode($contractor_info['detail1'], true) : [];
            $detail2 = json_decode($contractor_info['detail2'], true);

            // request_fee
            $request_fee = json_decode($contractor_info['request_fee'], true);

            // request_spm
            $request_spm = json_decode($contractor_info['request_spm'], true);

            // orgCode -> orgcode
            $orgcode = !empty($contractor_info['orgcode']) ? $contractor_info['orgcode'] : (!empty($detail2['orgCode']) ? $detail2['orgCode'] : '');
            if (empty($orgcode)) {
                $err_code[] = 2001;
                $error[] = 'orgCode is required';
            }

            // taxcode -> code
            $taxCode = !empty($detail2['taxCode']) ? $detail2['taxCode'] : '';
            if (empty($taxCode)) {
                if (preg_match('/^vn([0-9]+)$/', $orgcode, $m)) {
                    $taxCode = $m[1];
                } elseif (preg_match('/^vnz([0-9]+)$/', $orgcode, $m)) {
                    $taxCode = $m[1];
                } elseif (preg_match('/^([a-z]+)([0-9]+)$/', $orgcode, $m)) {
                    $taxCode = $m[2];
                }
            }

            // orgFullname -> companyname
            $companyname = '';
            if (!empty($detail2['orgFullName'])) {
                $companyname = $detail2['orgFullName'];
            }

            // name_search
            $name_search = '';
            if (!empty($companyname)) {
                $_string = $companyname;
                $_string = change_alias($_string);
                $_string = str_replace("-", " ", $_string);
                $_string = mb_strtolower($_string);

                $_string = str_replace("tong cong ty", "COMNAME", $_string);
                $_string = str_replace("cn cong ty", "COMNAME", $_string);
                $_string = str_replace("cong ty cp", "COMNAME", $_string);
                $_string = str_replace("cong ty co phan", "COMNAME", $_string);
                $_string = str_replace("cong ty tnhh", "COMNAME", $_string);
                $_string = str_replace("cong ty trach nhiem huu han", "COMNAME", $_string);
                $_string = str_replace("cong ty mtv", "COMNAME", $_string);
                $_string = str_replace("cong ty mot thanh vien", "COMNAME", $_string);
                $_string = str_replace("cong ty", "COMNAME", $_string);
                $_string = str_replace("cong rty", "COMNAME", $_string);
                $_string = str_replace("c.ty", "COMNAME", $_string);
                $_string = str_replace("vien", "COMNAME", $_string);
                $_string = str_replace("doanh nghiep tu nhan", "COMNAME", $_string);
                $_string = str_replace("doanh nghiep", "COMNAME", $_string);
                $_string = str_replace("xi nghiep tu nhan", "COMNAME", $_string);
                $_string = str_replace("xi nghiep", "COMNAME", $_string);

                $name_search = $_string;
            } else {
                $err_code[] = 2003;
                $error[] = 'companyname is required';
                $dbcr->query("UPDATE " . CONTRACTOR_TABLE . " SET url_run='-2' WHERE id=" . $contractor_info['id']);
            }

            // officeAdd -> address
            $address = ((!empty($detail2['officeAdd']) ? $detail2['officeAdd'] : ''));

            // effRoleDate -> ngay_phe_duyet
            $ngay_phe_duyet = 0;
            if (!empty($detail1['effRoleDate']) && is_array($detail1['effRoleDate'])) {
                $approve_year = !empty($detail1['effRoleDate'][0]) && (int)$detail1['effRoleDate'][0] > 0 ? (int)$detail1['effRoleDate'][0] : 0;
                $approve_month = !empty($detail1['effRoleDate'][1]) && (int)$detail1['effRoleDate'][1] > 0 ? (int)$detail1['effRoleDate'][1] : 0;
                $approve_date = !empty($detail1['effRoleDate'][2]) && (int)$detail1['effRoleDate'][2] > 0 ? (int)$detail1['effRoleDate'][2] : 0;
                $approve_hour = !empty($detail1['effRoleDate'][3]) && (int)$detail1['effRoleDate'][3] >= 0 ? (int)$detail1['effRoleDate'][3] : 0;
                $approve_minute = !empty($detail1['effRoleDate'][4]) && (int)$detail1['effRoleDate'][4] >= 0 ? (int)$detail1['effRoleDate'][4] : 0;
                $approve_sec = !empty($detail1['effRoleDate'][5]) && (int)$detail1['effRoleDate'][5] >= 0 ? (int)$detail1['effRoleDate'][5] : 0;

                $ngay_phe_duyet = ((!empty($approve_year) && !empty($approve_month) && !empty($approve_date)) ? mktime($approve_hour, $approve_minute, $approve_sec, $approve_month, $approve_date, $approve_year) : 0);
            }

            // startPendingDate -> ngay_tam_dung
            $ngay_tam_dung = ((!empty($detail1['startPendingDate']) && (preg_match('/^([0-9]{4})\-([0-9]{1,2})\-([0-9]{1,2})$/', $detail1['startPendingDate'], $m))) ? mktime(0, 0, 0, $m[2], $m[3], $m[1]) : 0);

            // orgEnName -> officialname
            // trim vì 1 số trường hợp có khoảng trắng
            $officialname = !empty($detail2['orgEnName']) ? trim($detail2['orgEnName']) : '';
            // taxDate -> taxdate
            $taxdate = (!empty($detail2['taxDate']) && (preg_match('/^([0-9]{4})\-([0-9]{1,2})\-([0-9]{1,2})$/', $detail2['taxDate'], $m))) ? mktime(0, 0, 0, $m[2], $m[3], $m[1]) : 0;

            // repName -> rep_name
            $rep_name = !empty($detail2['repName']) ? $detail2['repName'] : '';
            // repPosition -> rep_position
            $rep_position = !empty($detail2['repPosition']) ? trim($detail2['repPosition']) : '';

            /* Xử lý sau */
            // officePro -> province
            $province = !empty($detail2['officePro']) ? $detail2['officePro'] : 0;
            // officeDis -> district
            $district = !empty($detail2['officeDis']) ? $detail2['officeDis'] : 0;
            // officeWar -> ward
            $ward = !empty($detail2['officeWar']) ? $detail2['officeWar'] : 0;
            // businessType -> businesstype
            $business_code = !empty($detail2['businessType']) ? $detail2['businessType'] : '';
            // taxNation -> tax_nation
            $tax_nation = !empty($detail2['taxNation']) ? $detail2['taxNation'] : '';

            // Bổ sung các trường mới
            // officePhone -> phone
            // $phone = !empty($detail2['officePhone']) ? $detail2['officePhone'] : ''; -> bỏ
            // officeWeb -> website
            $website = !empty($detail2['officeWeb']) ? $detail2['officeWeb'] : '';
            // businesses -> industry1, industry2, industry3, industry4, industry5, thong_tin_nganh_nghe
            $businesses = !empty($detail2['businesses']) ? $detail2['businesses'] : [];

            // xử lý ngành nghề
            $str_nghe = $arr_nghe = [];
            $industry_main = $thong_tin_nganh_nghe = '';
            $array_industry = [
                'data_insert5' => '',
                'data_insert4' => '',
                'data_insert3' => '',
                'data_insert2' => '',
                'data_insert1' => ''
            ];
            if (!empty($businesses)) {
                $arr_nghe = $businesses;

                $array_industry = [
                    'data_insert5' => [],
                    'data_insert4' => [],
                    'data_insert3' => [],
                    'data_insert2' => [],
                    'data_insert1' => []
                ];
                foreach ($arr_nghe as $key_arr => $nghe) {
                    unset($arr_nghe[$key_arr]['id']);

                    // Xác định code industry chính xác
                    $arr_nghe[$key_arr]['code_industry'] = '';
                    if (!empty($nghe['code'])) {
                        $sql_industry = "SELECT * FROM `" . NV_PREFIXLANG . "_industry` WHERE code REGEXP '^[A-Z]" . $nghe['code'] . "$' LIMIT 1";
                        $result = $db->query($sql_industry);
                        if ($industry = $result->fetch()) {
                            $arr_nghe[$key_arr]['code_industry'] = !empty($industry['code']) ? $industry['code'] : '';
                            $industry['level'] >= 5 && $array_industry['data_insert5'][] = $industry['code'];
                            $industry['level'] >= 4 && $array_industry['data_insert4'][] = substr($industry['code'], 0, 5);
                            $industry['level'] >= 3 && $array_industry['data_insert3'][] = substr($industry['code'], 0, 4);
                            $industry['level'] >= 2 && $array_industry['data_insert2'][] = substr($industry['code'], 0, 3);
                            $industry['level'] >= 1 && $array_industry['data_insert1'][] = substr($industry['code'], 0, 1);
                        }
                    }

                    $str_nghe[] = $nghe['name'];

                    // ngành chính
                    if ($nghe['main'] == 1) {
                        $main_industry = $industry['code'];
                    }
                }
                $thong_tin_nganh_nghe = implode('[]', $str_nghe);

                foreach ($array_industry as $key => $value) {
                    $value = array_unique($value);
                    $value = empty($value) ? '' : implode(',', $value);
                    $array_industry[$key] = $value;
                }

                $array_industry = nv_compound_unicode_recursion($array_industry);
            }

            // Tình trạng nộp phí
            $fee_content = [];
            $invoiceEmail = $issueInvoiceAddr = $paymentExpDate = $nop_phi = '';
            $status = $email_type = $nop_phi_type = 0;

            // ngày hết hạn
            $ngay_het_han = 0;
            $invoiceName = '';
            $invoicePhone = '';
            $current_year = intval(date('Y', NV_CURRENTTIME));
            $current_month = intval(date('m', NV_CURRENTTIME));

            // Thông tin thanh toán
            $short_fee_info = '';
            $arr_short_fee_info = [];

            if (!empty($request_fee)) {
                if ($request_fee['responseCode'] == 200) {
                    // email nhận hoá đơn điện tử
                    $invoiceEmail = !empty($request_fee['body']['pmContractorViewDTO']['invoiceEmail']) ? $request_fee['body']['pmContractorViewDTO']['invoiceEmail'] : '';
                    // kiểm tra email
                    if (!empty($invoiceEmail)) {
                        $invoiceEmail = trim($invoiceEmail);
                        $invoiceEmail = preg_replace('/\\t|\\n|\\r|\s/', '', $invoiceEmail);

                        $email_validation_regex = "/^[_a-z0-9-]+(\.[_a-z0-9-]+)*@[a-z-]+(\.[a-z-]+)*(\.[a-z]{2,3})$/i";
                        if (!filter_var($invoiceEmail, FILTER_VALIDATE_EMAIL) || !preg_match($email_validation_regex, $invoiceEmail)) {
                            $invoiceEmail = '';
                        }
                    }
                    // địa chỉ nhận hoá đơn điện tử
                    $issueInvoiceAddr = !empty($request_fee['body']['pmContractorViewDTO']['issueInvoiceAddr']) ? $request_fee['body']['pmContractorViewDTO']['issueInvoiceAddr'] : '';
                    //trạng thái
                    $status = !empty($request_fee['body']['pmContractorViewDTO']['status']) ? $request_fee['body']['pmContractorViewDTO']['status'] : 0;
                    if (!empty($request_fee['body']['pageFeeInfo']['content'])) {
                        $fee_content = $request_fee['body']['pageFeeInfo']['content'];
                    }

                    if (!empty($fee_content)) {
                        //ngày hết hạn -> tính lại ngày hết hạn nếu chưa nộp phí
                        $paymentExpDate = !empty($fee_content[0]['paymentExpDate']) ? $fee_content[0]['paymentExpDate'] : '';

                        if (!empty($fee_content[0]['recName'])) {
                            $invoiceName = $fee_content[0]['recName'];
                        }
                        if (!empty($fee_content[0]['recPhone'])) {
                            $invoicePhone = $fee_content[0]['recPhone'];
                        }
                        if (!empty($paymentExpDate)) {
                            if (preg_match('/^([0-9]{4})\-([0-9]{1,2})\-([0-9]{1,2})T([0-9]{2})\:([0-9]{2})\:([0-9]{2})$/', $paymentExpDate, $m)) {
                                $ngay_het_han = mktime($m[4], $m[5], $m[6], $m[2], $m[3], $m[1]);
                            }
                        }
                        $nop_phi = 'Chưa nộp chi phí';
                        // Tình trạng nộp phí
                        $email_type = 0;
                        if (!empty($fee_content[0]['feeTypeCode']) && $fee_content[0]['feeTypeCode'] == 'DK') {
                            if (!empty($ngay_het_han) && $ngay_het_han < NV_CURRENTTIME) {
                                if (count($fee_content) == 1) {
                                    // Chưa nộp phi phí mà mới hết hạn năm đầu
                                    $email_type = 1;
                                } elseif (count($fee_content) == 2 || count($fee_content) == 3) {
                                    // Chưa nộp phi phí mà đã hết hạn 2 năm đến dưới 3 năm
                                    $email_type = 4;
                                } elseif (count($fee_content) > 3) {
                                    // Chưa nộp phi phí mà đã hết hạn 3 năm trở lên.
                                    $email_type = 5;
                                }
                            }

                            $nop_phi_type = 1;
                        } else {
                            if (!empty($ngay_het_han) && $ngay_het_han < NV_CURRENTTIME) {
                                // Đã nộp chi phí nhưng hiện tại hết hạn
                                $email_type = 2;
                                $nop_phi_type = 2;
                                $nop_phi = 'Đã nộp phí, hiện tại hết hạn';
                            } else {
                                // Đã nộp chi phí
                                $nop_phi = 'Đã nộp chi phí';
                                $email_type = 3;
                                $nop_phi_type = 3;
                            }
                        }

                        // Lưu thông tin thanh toán ngắn gọn
                        foreach ($fee_content as $key_fee_content => $value_fee_content) {
                            $arr_short_fee_info[] = [
                                'feeCharge' => $value_fee_content['feeCharge'],
                                'feeVat' => $value_fee_content['feeVat'],
                                'feeTotal' => $value_fee_content['feeTotal'],
                                'paymentExpDate' => $value_fee_content['paymentExpDate'],
                                'feeTypeCode' => $value_fee_content['feeTypeCode'],
                                'maintainYear' => $value_fee_content['maintainYear'],
                                'contractorRole' => $value_fee_content['contractorRole']
                            ];
                        }

                        if (!empty($arr_short_fee_info)) {
                            $short_fee_info = json_encode($arr_short_fee_info, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                        }
                    } else {
                        $nop_phi = 'Đã nộp chi phí';
                        $email_type = 3;
                        $nop_phi_type = 3;
                    }
                }
            }

            $re_crawl_time = 0;
            // thời gian bóc lại
            if ($email_type == 3) { // Đã nộp chi phí và vẫn còn hạn
                // Update Bóc lại sau ngày hết hạn 1 ngày (ngày 01/04 hàng năm)
                $current_year_expiry_date = mktime(0, 0, 0, 31, 3, $current_year);
                $next_expiry_date = $current_year_expiry_date;
                if (NV_CURRENTTIME > $current_year_expiry_date) {
                    $next_expiry_date = mktime(0, 0, 0, 31, 3, ($current_year + 1));
                }
                $re_crawl_time = $next_expiry_date + 24 * 60 * 60;
            } elseif ($email_type == 2) { // Đã nộp chi phí nhưng hiện tại hết hạn
                // Update tuần tự 10 ngày/ lần
                $re_crawl_time = NV_CURRENTTIME + 10 * 24 * 60 * 60;
            } elseif ($email_type == 1) { // Chưa nộp phi phí mà mới hết hạn năm đầu
                // Update mỗi tháng 1 lần.
                if ($current_month == 12) { // Đặt ngày tự động vào ngày 1 tháng sau
                    $re_crawl_time = mktime(0, 0, 0, 1, 1, $current_year + 1);
                } else {
                    $re_crawl_time = mktime(0, 0, 0, $current_month + 1, 1, $current_year);
                }
            } elseif ($email_type == 4) { // Chưa nộp phi phí mà đã hết hạn 2 năm đến dưới 3 năm
                // Update mỗi 2 tháng 1 lần.
                if ($current_month >= 11) { // Đặt ngày tự động vào ngày 1 hai tháng sau
                    $re_crawl_time = mktime(0, 0, 0, ($current_month + 2) % 12, 1, $current_year + 1);
                } else {
                    $re_crawl_time = mktime(0, 0, 0, $current_month + 2, 1, $current_year);
                }
            } elseif ($email_type == 5) { // Chưa nộp phi phí mà đã hết hạn 3 năm trở lên.
                if ($current_month >= 10) { // Update mỗi 3 tháng 1 lần (tránh thời gian cao điểm update là tháng 3,4)
                    $set_month = ($current_month + 3) % 12;
                    $set_year = $current_year + 1;
                } else {
                    $set_month = ($current_month + 3) % 12;
                    $set_year = $current_year;
                }
                $set_month == 3 && $set_month == 5; // Tránh tháng 3
                $set_month == 4 && $set_month == 5; // Tránh tháng 4
                $re_crawl_time = mktime(0, 0, 0, $set_month, 1, $set_year);
            }

            if (!empty($re_crawl_time)) {
                echo '- Ngày bóc lại tiếp theo: ' . date('d-m-Y', $re_crawl_time) . "\n";
            }

            // xử lý request_spm
            $representative = $spm = $org_detail = [];
            $array_lvkd = [
                1 => 'HH', // Hàng hóa
                2 => 'XL', // Xây lắp
                3 => 'TV', // Tư vấn
                4 => 'PTV', // Phi tư vấn
            ];
            $get_ho_so = 0;
            if (!empty($request_spm)) {
                // lĩnh vực kinh doanh
                $spm['linh_vuc_kinh_doanh'] = !empty($request_spm['bidFields']['lcnt']) ? $request_spm['bidFields']['lcnt'] : '';
                if (!empty($spm['linh_vuc_kinh_doanh'])) {
                    $arr_linh_vuc_kinh_doanh = explode(',', $spm['linh_vuc_kinh_doanh']);
                    $arr_linh_vuc = [];
                    foreach ($arr_linh_vuc_kinh_doanh as $value) {
                        $value = trim($value);
                        if ($key = array_search($value, $array_lvkd)) {
                            $arr_linh_vuc[$key] = $key;
                        }
                    }
                    $spm['linh_vuc_kinh_doanh'] = implode(',', $arr_linh_vuc);
                }

                // quy mô doanh nghiệp
                $spm['org_scale'] = !empty($request_spm['orgScale']) ? $request_spm['orgScale'] : '';

                // số lượng nhân viên
                $spm['so_nhan_vien'] = !empty($request_spm['empCount']) ? $request_spm['empCount'] : 0;

                if (!empty($request_spm['orgAdd'])) {
                    // sdt doanh nghiệp
                    $spm['phone'] = !empty($request_spm['orgAdd']['phone']) ? $request_spm['orgAdd']['phone'] : '';

                    // website doanh nghiệp
                    $spm['website'] = !empty($request_spm['orgAdd']['website']) ? $request_spm['orgAdd']['website'] : '';
                }

                // thông tin người đại diện
                if (!empty($request_spm['legalRepresentative'])) {
                    $representative['rep_name'] = !empty($request_spm['legalRepresentative']['fullName']) ? $request_spm['legalRepresentative']['fullName'] : '';
                    $representative['represent_phone'] = !empty($request_spm['legalRepresentative']['phone']) ? $request_spm['legalRepresentative']['phone'] : '';
                    $representative['represent_email'] = !empty($request_spm['legalRepresentative']['email']) ? $request_spm['legalRepresentative']['email'] : '';
                }

                // Thông tin doanh nghiệp bổ sung
                if (!empty($request_spm['orgDetail'])) {
                    // điều lệ công ty
                    $org_detail['comp_charter_file_id'] = !empty($request_spm['orgDetail']['compCharterFileId']) ? $request_spm['orgDetail']['compCharterFileId'] : '';
                    $org_detail['comp_charter_file_name'] = !empty($request_spm['orgDetail']['compCharterFileName']) ? $request_spm['orgDetail']['compCharterFileName'] : '';

                    // sơ đồ tổ chức
                    $org_detail['org_chart_file_id'] = !empty($request_spm['orgDetail']['orgChartFileId']) ? $request_spm['orgDetail']['orgChartFileId'] : '';
                    $org_detail['org_chart_file_name'] = !empty($request_spm['orgDetail']['orgChartFileName']) ? $request_spm['orgDetail']['orgChartFileName'] : '';

                    // Đkkd công ty
                    $org_detail['business_file_id'] = !empty($request_spm['orgDetail']['businessFileId']) ? $request_spm['orgDetail']['businessFileId'] : '';
                    $org_detail['business_file_name'] = !empty($request_spm['orgDetail']['businessFileName']) ? $request_spm['orgDetail']['businessFileName'] : '';
                    $get_ho_so = 1;
                }
            }


            /* END xử lý */

            /* Custom handle address */
            if (empty($ward)) {
                $handle_address = handle_address($address, $province);
                if (!empty($handle_address)) {
                    $address = $handle_address['address'];
                    $ward = $handle_address['ward'];
                    $district = $handle_address['district'];
                    $province = $handle_address['province'];
                }
            }
            /* End custom handle address */

            // arr_business_info
            $arr_business_info = [
                'orgcode' => $orgcode,
                // 'currentstatus' => $detail1['status'], -> bỏ
                'companyname' => $companyname,
                'province' => (int) $province,
                'district' => (int) $district,
                'ward' => (int) $ward,
                'name_search' => $name_search,
                'address' => trim($address, "\\"), // Xóa cái này ở cuối địa chỉ để tránh ảnh hưởng seo
                'code' => $taxCode,
                'tax_nation' => $detail2['taxNation'],
                'officialname' => $officialname,
                'businesstype' => (!empty($business_code) && !empty($business_type[$business_code]['id'])) ? $business_type[$business_code]['id'] : 0,
                'taxdate' => (int) $taxdate,
                'main_industry' => !empty($main_industry) ? $main_industry : '',
                // 'phone' => $phone, -> bỏ
                'website' => !empty($website) ? $website : (!empty($spm['website']) ? $spm['website'] : ''),
                'phone' => !empty($spm['phone']) ? $spm['phone'] : '',
                'represent_phone' => !empty($representative['represent_phone']) ? $representative['represent_phone'] : '',
                'represent_email' => !empty($representative['represent_email']) ? $representative['represent_email'] : '',
            ];

            $arr_business_info = nv_compound_unicode_recursion($arr_business_info);
            print_r($arr_business_info);

            // arr_business_addinfo
            $arr_business_addinfo = [
                'so_dkkd' => $taxCode,
                'ngay_phe_duyet' => $ngay_phe_duyet,
                'ngay_tam_dung' => $ngay_tam_dung,
                'rep_name' => (!empty($rep_name) ? $rep_name : (!empty($representative['rep_name']) ? $representative['rep_name'] : '')),
                'rep_position' => $rep_position,
                'tax_nation' => $tax_nation,
                'thong_tin_nganh_nghe' => $thong_tin_nganh_nghe,
                'nganh_nghe_kinh_doanh' => !empty($arr_nghe) ? json_encode($arr_nghe, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) : '',
                'invoice_email' => !empty($invoiceEmail) ? $invoiceEmail : '',
                'invoice_name' => !empty($invoiceName) ? $invoiceName : '',
                'invoice_phone' => !empty($invoicePhone) ? $invoicePhone : '',
                'expiry_time' => !empty($ngay_het_han) ? $ngay_het_han : 0,
                'nop_phi' => !empty($nop_phi) ? $nop_phi : '',
                'status' => !empty($status) ? $status : 0,
                'issue_invoice_addr' => !empty($issueInvoiceAddr) ? $issueInvoiceAddr : '',
                'nop_phi_type' => !empty($nop_phi_type) ? $nop_phi_type : 0,
                'email_type' => !empty($email_type) ? $email_type : 0,
                'auto_crawl_time' => !empty($re_crawl_time) ? (int)$re_crawl_time : 0,
                'short_fee_info' => !empty($short_fee_info) ? $short_fee_info : '',
                'linh_vuc_kinh_doanh' => !empty($spm['linh_vuc_kinh_doanh']) ? $spm['linh_vuc_kinh_doanh'] : '',
                'org_scale' => !empty($spm['org_scale']) ? $spm['org_scale'] : '',
                'so_nhan_vien' => !empty($spm['so_nhan_vien']) ? $spm['so_nhan_vien'] : 0,
                'comp_charter_file_id' => !empty($org_detail['comp_charter_file_id']) ? $org_detail['comp_charter_file_id'] : '',
                'comp_charter_file_name' => !empty($org_detail['comp_charter_file_name']) ? $org_detail['comp_charter_file_name'] : '',
                'org_chart_file_id' => !empty($org_detail['org_chart_file_id']) ? $org_detail['org_chart_file_id'] : '',
                'org_chart_file_name' => !empty($org_detail['org_chart_file_name']) ? $org_detail['org_chart_file_name'] : '',
                'business_file_id' => !empty($org_detail['business_file_id']) ? $org_detail['business_file_id'] : '',
                'business_file_name' => !empty($org_detail['business_file_name']) ? $org_detail['business_file_name'] : '',
                'get_ho_so' => $get_ho_so
            ];

            $arr_business_addinfo = nv_compound_unicode_recursion($arr_business_addinfo);
            print_r($arr_business_addinfo);

            $turn_send_mail_renewal = 0;
            $next_time_send_mail_renewal = 0;
            if (empty($err_code)) {
                try {
                    $business_id = 0;
                    $my_business = [];
                    if (!empty($orgcode)) {
                        $my_business = $db->query('SELECT * FROM ' . BUSINESS_TABLE . ' WHERE orgcode = ' . $db->quote($orgcode) . ' ORDER BY id DESC LIMIT 1')
                            ->fetch();
                    }

                    if (empty($my_business) and !empty($taxCode)) {
                        $my_business = $db->query('SELECT * FROM ' . BUSINESS_TABLE . ' WHERE code = ' . $db->quote($taxCode) . ' ORDER BY id DESC LIMIT 1')
                            ->fetch();
                    }

                    if (!empty($my_business)) {
                        $business_id = $my_business['id'];
                    }

                    $update_data = $business_id > 0 ? 1 : 0;

                    // xác định có gửi mail không
                    $send_mail_renewal = true;
                    $turn_send_mail_renewal = 1;
                    if ($email_type == 3 || $email_type == 0) { // Đã nộp chi phí và vẫn còn hạn hoặc chưa có thông tin nộp phí
                        $send_mail_renewal = false;
                        $turn_send_mail_renewal = 0;
                    }

                    if ($update_data == 1 && !empty($my_business)) {
                        // Kiểm tra lại nếu Giá trị mới không có thì gán lại giá trị cũ

                        if (empty($arr_business_info['address'])) {
                            $arr_business_info['address'] = $my_business['address'];
                        }
                        // if (empty($arr_business_info['officialname'])) {
                        //     $arr_business_info['officialname'] = $my_business['officialname'];
                        // }
                        if (empty($arr_business_info['businesstype'])) {
                            $arr_business_info['businesstype'] = $my_business['businesstype'];
                        }
                        if (empty($arr_business_info['taxdate'])) {
                            $arr_business_info['taxdate'] = $my_business['taxdate'];
                        }
                        if (empty($arr_business_info['phone'])) {
                            $arr_business_info['phone'] = $my_business['phone'];
                        }
                        if (empty($arr_business_info['website'])) {
                            $arr_business_info['website'] = $my_business['website'];
                        }
                        if (empty($arr_business_info['represent_phone'])) {
                            $arr_business_info['represent_phone'] = $my_business['represent_phone'];
                        }
                        if (empty($arr_business_info['represent_email'])) {
                            $arr_business_info['represent_email'] = $my_business['represent_email'];
                        }
                    }

                    if ($update_data == 0) {
                        echo "...Bat dau insert nv4_businesslistings_info... \n";
                        $stmt = $db->prepare("INSERT INTO " . BUSINESS_PREFIX_GLOBAL . "_info (code, userid, industry1, industry2, industry3, industry4, industry5, address, companyname, name_search, officialname, province, district, ward, totalview, active, allowmail, about, elasticsearch, businesstype, orgcode, taxdate, main_industry, website, phone, represent_phone, represent_email) VALUES (:code, :userid, :industry1, :industry2, :industry3, :industry4, :industry5, :address, :companyname, :name_search, :officialname, :province, :district, :ward, 1, 1, 0, '', 0, :businesstype, :orgcode, :taxdate, :main_industry, :website, :phone, :represent_phone, :represent_email)");
                        $stmt->bindParam(':code', $arr_business_info['code'], PDO::PARAM_STR);
                        $stmt->bindValue(':userid', 1, PDO::PARAM_INT);
                    } else {
                        echo '...Bat dau update nv4_businesslistings_info... id:  ' . $my_business['id'] . "\n";
                        $row_old = $my_business;
                        $stmt = $db->prepare("UPDATE " . BUSINESS_PREFIX_GLOBAL . "_info SET industry1 = :industry1, industry2 = :industry2, industry3 = :industry3, industry4 = :industry4, industry5 = :industry5, address=:address, companyname=:companyname, name_search=:name_search, officialname=:officialname, province=:province, district=:district, ward=:ward, elasticsearch=0, update_data = 0, businesstype = :businesstype, orgcode = :orgcode, taxdate = :taxdate, main_industry = :main_industry, website = :website, phone = :phone, represent_phone = :represent_phone, represent_email = :represent_email WHERE id=" . $my_business['id']);
                    }

                    $stmt->bindParam(':industry1', $array_industry['data_insert1'], PDO::PARAM_STR);
                    $stmt->bindParam(':industry2', $array_industry['data_insert2'], PDO::PARAM_STR);
                    $stmt->bindParam(':industry3', $array_industry['data_insert3'], PDO::PARAM_STR);
                    $stmt->bindParam(':industry4', $array_industry['data_insert4'], PDO::PARAM_STR);
                    $stmt->bindParam(':industry5', $array_industry['data_insert5'], PDO::PARAM_STR);
                    $stmt->bindParam(':address', $arr_business_info['address'], PDO::PARAM_STR);
                    $stmt->bindParam(':companyname', $arr_business_info['companyname'], PDO::PARAM_STR);
                    $stmt->bindParam(':name_search', $arr_business_info['name_search'], PDO::PARAM_STR);
                    $stmt->bindParam(':officialname', $arr_business_info['officialname'], PDO::PARAM_STR);
                    // $stmt->bindParam(':currentstatus', $arr_business_info['currentstatus'], PDO::PARAM_STR); -> bỏ
                    $stmt->bindParam(':province', $arr_business_info['province'], PDO::PARAM_INT);
                    $stmt->bindParam(':district', $arr_business_info['district'], PDO::PARAM_INT);
                    $stmt->bindParam(':ward', $arr_business_info['ward'], PDO::PARAM_INT);
                    $stmt->bindParam(':businesstype', $arr_business_info['businesstype'], PDO::PARAM_INT);
                    $stmt->bindParam(':orgcode', $arr_business_info['orgcode'], PDO::PARAM_STR);
                    $stmt->bindParam(':taxdate', $arr_business_info['taxdate'], PDO::PARAM_INT);
                    $stmt->bindParam(':main_industry', $arr_business_info['main_industry'], PDO::PARAM_STR);
                    $stmt->bindParam(':phone', $arr_business_info['phone'], PDO::PARAM_STR);
                    $stmt->bindParam(':website', $arr_business_info['website'], PDO::PARAM_STR);
                    $stmt->bindParam(':represent_phone', $arr_business_info['represent_phone'], PDO::PARAM_STR);
                    $stmt->bindParam(':represent_email', $arr_business_info['represent_email'], PDO::PARAM_STR);

                    $exc = $stmt->execute();

                    if ($update_data == 0) {
                        $business_id = $db->lastInsertId();
                    }
                    if ($update_data == 1) {
                        $row_new = $db->query('SELECT * FROM ' . BUSINESS_PREFIX_GLOBAL . '_info WHERE id = ' . $business_id)->fetch();
                        $status_insert = insert_log_crawls($business_id, 'BUSINESS', $row_old, $row_new);
                        if ($status_insert > 0) {
                            echo ("<br/>\nLOG: BUSINESS - ID: " . $business_id . "- OK<br/>\n");
                        }
                    }

                    if ($exc) {
                        // Cập nhật dữ liệu ngành nghề theo đkkd, dữ liệu lấy từ dauthau.net
                        // thời gian cập nhật lại: 6 tháng
                        $time_update_industry_again = 6 * 30 * 24 * 60 * 60;
                        if (defined('API_DAUTHAUNET_KEY') && !empty($taxCode) && ($update_data == 0 || ($update_data == 1 && (empty($my_business['update_industry_dtnet']) || $my_business['time_update_industry'] < NV_CURRENTTIME - $time_update_industry_again)))) {
                            $timestamp = time();
                            $business = [];
                            $business[0]['id'] = $business_id;
                            $business[0]['code'] = $taxCode;
                            $request = [
                                // Tham số bắt buộc
                                'apikey' => API_DAUTHAUNET_KEY,
                                'timestamp' => $timestamp,
                                'hashsecret' => password_hash(API_DAUTHAUNET_SECRET . '_' . $timestamp, PASSWORD_DEFAULT),
                                'language' => 'vi',
                                'action' => 'ListIndustry',
                                'module' => '',
                                'busines_info' => $business
                            ];

                            $agent = 'NukeViet Remote API Lib';
                            $ch = curl_init();
                            curl_setopt($ch, CURLOPT_URL, API_DAUTHAUNET_URL);
                            curl_setopt($ch, CURLOPT_HEADER, 0);

                            $safe_mode = (ini_get('safe_mode') == '1' || strtolower(ini_get('safe_mode')) == 'on') ? 1 : 0;
                            $open_basedir = ini_get('open_basedir') ? true : false;
                            if (!$safe_mode and !$open_basedir) {
                                curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
                                curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
                            }

                            curl_setopt($ch, CURLOPT_TIMEOUT, 20);
                            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                            curl_setopt($ch, CURLOPT_USERAGENT, $agent);
                            curl_setopt($ch, CURLOPT_POST, true);
                            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($request));
                            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
                            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
                            $res = curl_exec($ch);
                            curl_close($ch);

                            $respon = json_decode($res, true);

                            if ($respon['status'] == 'success') {
                                $value_result = current($respon['arr_profile']);
                                if (!empty($value_result['industry_dkkd'])) {
                                    $db->query('UPDATE ' . BUSINESS_PREFIX_GLOBAL . '_info SET update_industry_dtnet = 1, industry_dkkd = ' . $db->quote($value_result['industry_dkkd']) . ', time_update_industry = ' . NV_CURRENTTIME . ', elasticsearch = 0 WHERE code = ' . $db->quote($value_result['code']));
                                } else {
                                    $db->query('UPDATE ' . BUSINESS_PREFIX_GLOBAL . '_info SET update_industry_dtnet = -1, time_update_industry = ' . NV_CURRENTTIME . ', elasticsearch = 0 WHERE code = ' . $db->quote($value_result['code']));
                                }
                            }
                        }
                        // Cập nhật dữ liệu array_profile (thông tin bên dauthau.net), dữ liệu lấy từ dauthau.net
                        if (defined('API_DAUTHAUNET_KEY') and ($update_data == 0 || ($update_data == 1 && empty($my_business['array_profile']) && !empty($taxCode)))) {
                            $business = [];
                            $business[0]['id'] = $business_id;
                            $business[0]['code'] = $taxCode;
                            // Check exist on site ID
                            $connect_api = [
                                'api_url' => API_DAUTHAUNET_URL,
                                'api_key' => API_DAUTHAUNET_KEY,
                                'api_secret' => API_DAUTHAUNET_SECRET
                            ];
                            $params = [
                                'busines_info' => $business
                            ];
                            $respon = call_api($connect_api, '', 'UpdateBusinesInfo', $params);

                            if ($respon['status'] == 'success') {
                                $value_result = current($respon['result_profile']);
                                if (!empty($value_result['profile_id'])) {
                                    $db->query("UPDATE " . BUSINESS_PREFIX_GLOBAL . "_info SET profile_id = " . intval($value_result['profile_id']) . ", array_profile = " . $db->quote(json_encode($value_result['array_profile'], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT)) . ", elasticsearch = 0 WHERE id = " . $business_id . "");
                                } else {
                                    $db->query('UPDATE ' . BUSINESS_PREFIX_GLOBAL . '_info SET profile_id = -' . NV_CURRENTTIME . ', elasticsearch = 0 WHERE id = ' . $business_id);
                                }
                            }
                        }
                        /* insert thông tin vào bảng nv4_businesslistings_addinfo */
                        // check lại bảng nv4_businesslistings_addinfo do 1 số nhà thầu k có row trong bảng này
                        $count_addinfo = $db->query('SELECT * FROM ' . BUSINESS_PREFIX_GLOBAL . '_addinfo WHERE id = ' . $business_id)->fetch();
                        $update_data_addinfo = !empty($count_addinfo) ? 1 : 0;
                        if ($update_data_addinfo == 1) {
                            //Kiểm tra xem có first_time_crawl chưa? nếu chưa thì gán luôn = NV_CURRENTTIME
                            if ($count_addinfo['first_time_crawl'] == 0) {
                                $db->exec('UPDATE ' . BUSINESS_PREFIX_GLOBAL . '_addinfo SET first_time_crawl = ' . NV_CURRENTTIME . ' WHERE id = ' . $count_addinfo['id']);
                            }

                            // Kiểm tra lại nếu Giá trị mới không có thì gán lại giá trị cũ
                            if (empty($arr_business_addinfo['rep_name']) and isset($count_addinfo['rep_name'])) {
                                $arr_business_addinfo['rep_name'] = $count_addinfo['rep_name'];
                            }
                            if (empty($arr_business_addinfo['rep_position']) and isset($count_addinfo['rep_position'])) {
                                $arr_business_addinfo['rep_position'] = $count_addinfo['rep_position'];
                            }
                            if (empty($arr_business_addinfo['tax_nation']) and isset($count_addinfo['tax_nation'])) {
                                $arr_business_addinfo['tax_nation'] = $count_addinfo['tax_nation'];
                            }
                            if (empty($arr_business_addinfo['thong_tin_nganh_nghe']) and isset($count_addinfo['thong_tin_nganh_nghe'])) {
                                $arr_business_addinfo['thong_tin_nganh_nghe'] = $count_addinfo['thong_tin_nganh_nghe'];
                            }
                            if (empty($arr_business_addinfo['nganh_nghe_kinh_doanh']) and isset($count_addinfo['nganh_nghe_kinh_doanh'])) {
                                $arr_business_addinfo['nganh_nghe_kinh_doanh'] = $count_addinfo['nganh_nghe_kinh_doanh'];
                            }
                            if (empty($arr_business_addinfo['invoice_email']) and isset($count_addinfo['invoice_email'])) {
                                $arr_business_addinfo['invoice_email'] = $count_addinfo['invoice_email'];
                            }
                            if (empty($arr_business_addinfo['invoice_name']) and isset($count_addinfo['invoice_name'])) {
                                $arr_business_addinfo['invoice_name'] = $count_addinfo['invoice_name'];
                            }
                            if (empty($arr_business_addinfo['invoice_phone']) and isset($count_addinfo['invoice_phone'])) {
                                $arr_business_addinfo['invoice_phone'] = $count_addinfo['invoice_phone'];
                            }
                            if (empty($arr_business_addinfo['expiry_time']) and isset($count_addinfo['expiry_time'])) {
                                $arr_business_addinfo['expiry_time'] = $count_addinfo['expiry_time'];
                            }
                            if (empty($arr_business_addinfo['status']) and isset($count_addinfo['status'])) {
                                $arr_business_addinfo['status'] = $count_addinfo['status'];
                            }
                            if (empty($arr_business_addinfo['issue_invoice_addr']) and isset($count_addinfo['issue_invoice_addr'])) {
                                $arr_business_addinfo['issue_invoice_addr'] = $count_addinfo['issue_invoice_addr'];
                            }
                            if (empty($arr_business_addinfo['ngay_phe_duyet']) and isset($count_addinfo['ngay_phe_duyet'])) {
                                $arr_business_addinfo['ngay_phe_duyet'] = $count_addinfo['ngay_phe_duyet'];
                            }
                            if (empty($arr_business_addinfo['linh_vuc_kinh_doanh']) and isset($count_addinfo['linh_vuc_kinh_doanh'])) {
                                $arr_business_addinfo['linh_vuc_kinh_doanh'] = $count_addinfo['linh_vuc_kinh_doanh'];
                            }
                            if (empty($arr_business_addinfo['org_scale']) and isset($count_addinfo['org_scale'])) {
                                $arr_business_addinfo['org_scale'] = $count_addinfo['org_scale'];
                            }
                            if (empty($arr_business_addinfo['so_nhan_vien']) and isset($count_addinfo['so_nhan_vien'])) {
                                $arr_business_addinfo['so_nhan_vien'] = $count_addinfo['so_nhan_vien'];
                            }
                            if (empty($arr_business_addinfo['comp_charter_file_id']) and isset($count_addinfo['comp_charter_file_id'])) {
                                $arr_business_addinfo['comp_charter_file_id'] = $count_addinfo['comp_charter_file_id'];
                                $arr_business_addinfo['get_ho_so'] = 1;
                            }
                            if (empty($arr_business_addinfo['comp_charter_file_name']) and isset($count_addinfo['comp_charter_file_name'])) {
                                $arr_business_addinfo['comp_charter_file_name'] = $count_addinfo['comp_charter_file_name'];
                            }
                            if (empty($arr_business_addinfo['org_chart_file_id']) and isset($count_addinfo['org_chart_file_id'])) {
                                $arr_business_addinfo['org_chart_file_id'] = $count_addinfo['org_chart_file_id'];
                                $arr_business_addinfo['get_ho_so'] = 1;
                            }
                            if (empty($arr_business_addinfo['org_chart_file_name']) and isset($count_addinfo['org_chart_file_name'])) {
                                $arr_business_addinfo['org_chart_file_name'] = $count_addinfo['org_chart_file_name'];
                            }
                            if (empty($arr_business_addinfo['business_file_id']) and isset($count_addinfo['business_file_id'])) {
                                $arr_business_addinfo['business_file_id'] = $count_addinfo['business_file_id'];
                                $arr_business_addinfo['get_ho_so'] = 1;
                            }
                            if (empty($arr_business_addinfo['business_file_name']) and isset($count_addinfo['business_file_name'])) {
                                $arr_business_addinfo['business_file_name'] = $count_addinfo['business_file_name'];
                            }

                            // Không Gửi email nếu tình trạng nộp phí vẫn như cũ và đã có turn_send_mail_renewal (đã gửi mail rồi)
                            if ($count_addinfo['email_type'] == $email_type && !empty($count_addinfo['turn_send_mail_renewal'])) {
                                $send_mail_renewal = false;
                                $turn_send_mail_renewal = $count_addinfo['turn_send_mail_renewal'];
                            }

                            echo '...Bat dau update nv4_businesslistings_addinfo... id:  ' . $business_id . "\n";
                            $stmt1 = $db->prepare('UPDATE ' . BUSINESS_PREFIX_GLOBAL . '_addinfo SET time_crawler=:time_crawler, count_crawl=count_crawl+1, rep_name = :rep_name, rep_position = :rep_position, tax_nation = :tax_nation, thong_tin_nganh_nghe = :thong_tin_nganh_nghe, nganh_nghe_kinh_doanh = :nganh_nghe_kinh_doanh, invoice_email = :invoice_email, invoice_name=:invoice_name, invoice_phone=:invoice_phone,expiry_time = :expiry_time, nop_phi = :nop_phi, status = :status, issue_invoice_addr = :issue_invoice_addr, email_type = :email_type, auto_crawl_time = :auto_crawl_time, short_fee_info = :short_fee_info, nop_phi_type = :nop_phi_type, ngay_phe_duyet = :ngay_phe_duyet, ngay_tam_dung = :ngay_tam_dung, linh_vuc_kinh_doanh = :linh_vuc_kinh_doanh, org_scale = :org_scale, so_nhan_vien = :so_nhan_vien, comp_charter_file_id = :comp_charter_file_id, comp_charter_file_name = :comp_charter_file_name, org_chart_file_id = :org_chart_file_id, org_chart_file_name = :org_chart_file_name, business_file_id = :business_file_id, business_file_name = :business_file_name, get_ho_so = :get_ho_so WHERE id=' . $business_id);
                        } else {
                            echo "...Bat dau insert nv4_businesslistings_addinfo... \n";
                            $stmt1 = $db->prepare('INSERT INTO ' . BUSINESS_PREFIX_GLOBAL . '_addinfo (id, so_dkkd, time_crawler, count_crawl, rep_name, rep_position, linh_vuc_kinh_doanh, von_dieu_le, thong_tin_nganh_nghe, hop_dong, bao_cao_tai_chinh, tax_nation, nganh_nghe_kinh_doanh, invoice_email, invoice_name, invoice_phone, expiry_time, nop_phi, status, issue_invoice_addr, email_type, auto_crawl_time, short_fee_info, nop_phi_type, ngay_phe_duyet, ngay_tam_dung, org_scale, so_nhan_vien, comp_charter_file_id, comp_charter_file_name, org_chart_file_id, org_chart_file_name, business_file_id, business_file_name, first_time_crawl, get_ho_so) VALUES (:id, :so_dkkd, :time_crawler, 1, :rep_name, :rep_position, :linh_vuc_kinh_doanh, :von_dieu_le, :thong_tin_nganh_nghe, :hop_dong, :bao_cao_tai_chinh, :tax_nation, :nganh_nghe_kinh_doanh, :invoice_email, :invoice_name, :invoice_phone, :expiry_time, :nop_phi, :status, :issue_invoice_addr, :email_type, :auto_crawl_time, :short_fee_info, :nop_phi_type, :ngay_phe_duyet, :ngay_tam_dung, :org_scale, :so_nhan_vien, :comp_charter_file_id, :comp_charter_file_name, :org_chart_file_id, :org_chart_file_name, :business_file_id, :business_file_name, :first_time_crawl, :get_ho_so)');

                            $stmt1->bindParam(':id', $business_id, PDO::PARAM_STR);
                            $stmt1->bindParam(':so_dkkd', $arr_business_addinfo['so_dkkd'], PDO::PARAM_STR);
                            // các biến ko dc để null
                            $arr_business_addinfo['linh_vuc_kinh_doanh'] = $arr_business_addinfo['von_dieu_le'] = $arr_business_addinfo['hop_dong'] = $arr_business_addinfo['bao_cao_tai_chinh'] = '';
                            $stmt1->bindParam(':von_dieu_le', $arr_business_addinfo['von_dieu_le'], PDO::PARAM_STR);
                            $stmt1->bindParam(':hop_dong', $arr_business_addinfo['hop_dong'], PDO::PARAM_STR);
                            $stmt1->bindParam(':bao_cao_tai_chinh', $arr_business_addinfo['bao_cao_tai_chinh'], PDO::PARAM_STR);
                            $stmt1->bindValue(':first_time_crawl', NV_CURRENTTIME, PDO::PARAM_INT);
                        }

                        $stmt1->bindParam(':ngay_phe_duyet', $arr_business_addinfo['ngay_phe_duyet'], PDO::PARAM_INT);
                        $stmt1->bindParam(':ngay_tam_dung', $arr_business_addinfo['ngay_tam_dung'], PDO::PARAM_INT);
                        $arr_business_addinfo['thong_tin_nganh_nghe'] = isset($arr_business_addinfo['thong_tin_nganh_nghe']) ? trim($arr_business_addinfo['thong_tin_nganh_nghe']) : '';
                        $arr_business_addinfo['nganh_nghe_kinh_doanh'] = isset($arr_business_addinfo['nganh_nghe_kinh_doanh']) ? trim($arr_business_addinfo['nganh_nghe_kinh_doanh']) : '';

                        $stmt1->bindValue(':time_crawler', NV_CURRENTTIME, PDO::PARAM_INT);
                        $stmt1->bindParam(':rep_name', $arr_business_addinfo['rep_name'], PDO::PARAM_STR);
                        $stmt1->bindParam(':rep_position', $arr_business_addinfo['rep_position'], PDO::PARAM_STR);
                        $stmt1->bindParam(':tax_nation', $arr_business_addinfo['tax_nation'], PDO::PARAM_STR);
                        $stmt1->bindParam(':thong_tin_nganh_nghe', $arr_business_addinfo['thong_tin_nganh_nghe'], PDO::PARAM_STR);
                        $stmt1->bindParam(':nganh_nghe_kinh_doanh', $arr_business_addinfo['nganh_nghe_kinh_doanh'], PDO::PARAM_STR);
                        $stmt1->bindParam(':invoice_email', $arr_business_addinfo['invoice_email'], PDO::PARAM_STR);
                        $stmt1->bindParam(':invoice_name', $arr_business_addinfo['invoice_name'], PDO::PARAM_STR);
                        $stmt1->bindParam(':invoice_phone', $arr_business_addinfo['invoice_phone'], PDO::PARAM_STR);
                        $stmt1->bindParam(':expiry_time', $arr_business_addinfo['expiry_time'], PDO::PARAM_STR);
                        $stmt1->bindParam(':nop_phi', $arr_business_addinfo['nop_phi'], PDO::PARAM_STR);
                        $stmt1->bindParam(':status', $arr_business_addinfo['status'], PDO::PARAM_INT);
                        $stmt1->bindParam(':issue_invoice_addr', $arr_business_addinfo['issue_invoice_addr'], PDO::PARAM_STR);
                        $stmt1->bindParam(':email_type', $arr_business_addinfo['email_type'], PDO::PARAM_INT);
                        $stmt1->bindParam(':nop_phi_type', $arr_business_addinfo['nop_phi_type'], PDO::PARAM_INT);
                        $stmt1->bindParam(':auto_crawl_time', $arr_business_addinfo['auto_crawl_time'], PDO::PARAM_INT);
                        $stmt1->bindParam(':short_fee_info', $arr_business_addinfo['short_fee_info'], PDO::PARAM_STR);
                        $stmt1->bindParam(':linh_vuc_kinh_doanh', $arr_business_addinfo['linh_vuc_kinh_doanh'], PDO::PARAM_STR);
                        $stmt1->bindParam(':org_scale', $arr_business_addinfo['org_scale'], PDO::PARAM_STR);
                        $stmt1->bindParam(':so_nhan_vien', $arr_business_addinfo['so_nhan_vien'], PDO::PARAM_INT);
                        $stmt1->bindParam(':comp_charter_file_id', $arr_business_addinfo['comp_charter_file_id'], PDO::PARAM_STR);
                        $stmt1->bindParam(':comp_charter_file_name', $arr_business_addinfo['comp_charter_file_name'], PDO::PARAM_STR);
                        $stmt1->bindParam(':org_chart_file_id', $arr_business_addinfo['org_chart_file_id'], PDO::PARAM_STR);
                        $stmt1->bindParam(':org_chart_file_name', $arr_business_addinfo['org_chart_file_name'], PDO::PARAM_STR);
                        $stmt1->bindParam(':business_file_id', $arr_business_addinfo['business_file_id'], PDO::PARAM_STR);
                        $stmt1->bindParam(':business_file_name', $arr_business_addinfo['business_file_name'], PDO::PARAM_STR);
                        $stmt1->bindParam(':get_ho_so', $arr_business_addinfo['get_ho_so'], PDO::PARAM_INT);
                        $stmt1->execute();

                        $dbcr->query("UPDATE " . CONTRACTOR_TABLE . " SET business_id=" . $business_id . ", convert_err_code = '', tax_code = " . $dbcr->quote($taxCode) . " WHERE id=" . $contractor_info['id']);
                        print_r("- contractor id da convert xong: " . $contractor_info['id'] . "\n");
                        print_r("- business id: " . $business_id . "\n");
                        // Gửi mail
                        $id_mail_renewal = 0;
                        if ($send_mail_renewal) {
                            if (!empty($business_id) && !empty($arr_business_addinfo['expiry_time'])) {
                                // insert vào bảng nv4_business_renewal_mail
                                $id_mail_renewal = send_mail_renewal($business_id);
                                if ($id_mail_renewal) {
                                    // update id_mail_renewal
                                    $db->query("UPDATE " . BUSINESS_PREFIX_GLOBAL . "_addinfo SET id_mail_renewal = " . $id_mail_renewal . ", turn_send_mail_renewal = " . $turn_send_mail_renewal . " WHERE id = " . $business_id);
                                    echo '- Đã insert mail nhắc gia hạn tên trên msc vào bảng nv4_business_renewal_mail, id: ' . $id_mail_renewal . "\n";
                                }
                            }
                        }
                    }
                } catch (PDOException $e) {
                    $err_code[] = 3000;
                    print_r("- 241 Da xay ra loi trong qua trinh convert, contractor id: " . $contractor_info['id'] . "\n");
                    print_r($e);
                    $dbcr->query("UPDATE " . CONTRACTOR_TABLE . " SET business_id='-2', convert_err_code = '" . implode(",", $err_code) . "' WHERE id=" . $contractor_info['id']);
                    return false;
                }
            } else {
                print_r("- 247 Da xay ra loi trong qua trinh convert, contractor id: " . $contractor_info['id'] . "\n");
                if (empty($err_code)) {
                    $dbcr->query("UPDATE " . CONTRACTOR_TABLE . " SET business_id='-3' WHERE id=" . $contractor_info['id']);
                } else {
                    print_r($error);
                    $dbcr->query("UPDATE " . CONTRACTOR_TABLE . " SET business_id='-4', convert_err_code = '" . implode(",", $err_code) . "' WHERE id=" . $contractor_info['id']);
                }
            }
        }
    }
    echo "Thoi gian thuc hien = " . number_format((microtime(true) - NV_START_TIME), 3, '.', '');
} else {
    echo "No Data";
}

function handle_address($origin_address, $origin_province)
{
    global $db;
    $dstinhthanh = $db->query('SELECT id,title FROM ' . NV_PREFIXLANG . '_location_province')->fetchAll(\PDO::FETCH_UNIQUE | \PDO::FETCH_ASSOC);

    $dia_chi = $_dia_chi = $origin_address;
    $tinh_thanh_pho = !empty($origin_province) ? $dstinhthanh[$origin_province]['title'] : '';

    $row = [];
    $row['dia_chi'] = isset($dia_chi) ? preg_replace('/&nbsp;+/u', '', trim($dia_chi)) : '';
    $row['dia_chi_tru_so'] = !empty($row['dia_chi_tru_so']) ? html_entity_decode($row['dia_chi_tru_so'], ENT_QUOTES, 'UTF-8') : '';
    $row['dia_chi_giao_dich'] = !empty($row['dia_chi_giao_dich']) ? html_entity_decode($row['dia_chi_giao_dich'], ENT_QUOTES, 'UTF-8') : '';
    $row['dia_chi'] = (isset($row['dia_chi']) and !empty($row['dia_chi'])) ? html_entity_decode($row['dia_chi'], ENT_QUOTES, 'UTF-8') : (!empty($row['dia_chi_tru_so']) ? $row['dia_chi_tru_so'] : $row['dia_chi_giao_dich']);
    $row['dia_chi'] = mb_ereg_replace('[\s]+', ' ', $row['dia_chi']);
    $row['dia_chi'] = str_replace('Đakpơ', 'Đắk Pơ', $row['dia_chi']);
    $row['dia_chi'] = str_replace('Đắk Lắk', 'Đắc Lắc', $row['dia_chi']);
    $row['dia_chi'] = str_replace('-', ',', $row['dia_chi']);

    $row['tinh/_thanh_pho'] = !empty($tinh_thanh_pho) ? html_entity_decode($tinh_thanh_pho, ENT_QUOTES, 'UTF-8') : '';
    $row['tinh_/_thanh_pho'] = !empty($row['tinh_/_thanh_pho']) ? html_entity_decode($row['tinh_/_thanh_pho'], ENT_QUOTES, 'UTF-8') : $row['tinh/_thanh_pho'];
    $row['tinh_/_thanh_pho'] = preg_replace('/Đăk Lăk/', 'Đắc Lắc', $row['tinh_/_thanh_pho']);
    $row['tinh_/_thanh_pho'] = preg_replace('/Kạn/', 'Cạn', $row['tinh_/_thanh_pho']);
    $row['tinh_/_thanh_pho'] = preg_replace('/Thừa Thiên Huế/', 'Thừa Thiên - Huế', $row['tinh_/_thanh_pho']);
    $row['tinh_/_thanh_pho'] = preg_replace('/Bà Rịa Vũng Tàu/', 'Bà Rịa - Vũng Tàu', $row['tinh_/_thanh_pho']);
    $row['tinh_/_thanh_pho'] = str_replace('Bắc Kạn', 'Bắc Cạn', $row['tinh_/_thanh_pho']);

    $arr_diachi = get_address($row['dia_chi'], $row['tinh_/_thanh_pho']);
    $diachi_phuong = $arr_diachi['diachi_phuong'];
    $diachi_quan = $arr_diachi['diachi_quan'];
    $diachi_tinh = $arr_diachi['diachi_tinh'];
    $diachi_sonha = $arr_diachi['diachi_sonha'] != '' ? $arr_diachi['diachi_sonha'] : $_dia_chi;

    // Lấy thông tin địa chỉ
    $_arr_phuong = explode('-', $diachi_phuong);
    $diachi_phuong = sizeof($_arr_phuong) > 1 ? $_arr_phuong[1] : $diachi_phuong;

    $diachi_quan = change_alias($diachi_quan);
    $diachi_phuong = change_alias($diachi_phuong);

    // Tác tất cả các chữ
    $string = $diachi_phuong;
    $arr_p = explode('-', $string);
    // Thêm % ở đầu mỗi chữ
    foreach ($arr_p as $key => $value) {
        $string = substr($string, strpos($string, $value), strlen($string));
        $search[] = str_replace($value, '%' . $value, $string);
    }

    $where = '';
    $result_diachi = [];
    $result = [];
    // Lọc nếu có kết quả thì dừng luôn,
    foreach ($search as $key => $value) {
        if (!empty($diachi_quan)) {
            $result = $db->query('SELECT tb1.*, tb2.title as name_tinh, tb3.title as name_district FROM ' . NV_PREFIXLANG . '_location_ward tb1 ' . ' INNER JOIN ' . NV_PREFIXLANG . '_location_province tb2 ON tb2.id = tb1.idprovince  INNER JOIN ' . NV_PREFIXLANG . '_location_district tb3 ON tb3.id = tb1.iddistrict ' . ' WHERE tb1.alias LIKE ' . $db->quote($value) . ' AND tb3.alias LIKE ' . $db->quote('%' . $diachi_quan . '%'))
                ->fetchAll();
            if (!empty($result)) {
                // Nếu trùng cả tên huyện và tên phường thì where like tiếp cái tỉnh
                if (count($result) > 1) {
                    $result = $db->query('SELECT tb1.*, tb2.title as name_tinh, tb3.title as name_district FROM ' . NV_PREFIXLANG . '_location_ward tb1 ' . ' INNER JOIN ' . NV_PREFIXLANG . '_location_province tb2 ON tb2.id = tb1.idprovince  INNER JOIN ' . NV_PREFIXLANG . '_location_district tb3 ON tb3.id = tb1.iddistrict ' . ' WHERE tb1.alias LIKE ' . $db->quote($value) . ' AND tb3.alias LIKE ' . $db->quote('%' . $diachi_quan . '%') . ' AND tb2.title LIKE ' . $db->quote('%' . $diachi_tinh . '%'))
                        ->fetchAll();
                }
                break;
            }
        }
    }

    if (!empty($result)) {
        $result_diachi = $result[0];
    }

    // $sql_diachi = $db->query('SELECT * FROM nv4_vi_location_ward WHERE alias LIKE ' . $db->quote('%' . $diachi_phuong) . ' AND idprovince IN (SELECT id FROM nv4_vi_location_province WHERE title LIKE ' . $db->quote('%' . $diachi_tinh . '%') . ') AND iddistrict IN (SELECT id FROM nv4_vi_location_district WHERE alias LIKE ' . $db->quote('%' . $diachi_quan . '%') . ' )');
    // $result_diachi = $sql_diachi->fetch();

    if (!empty($result_diachi)) {
        $row['province'] = !empty($result_diachi['idprovince']) ? $result_diachi['idprovince'] : 0;
        $row['district'] = !empty($result_diachi['iddistrict']) ? $result_diachi['iddistrict'] : 0;
        $row['ward'] = !empty($result_diachi['id']) ? $result_diachi['id'] : 0;
    } else {
        $sql_diachi = $db->query('SELECT id as idprovince FROM ' . NV_PREFIXLANG . '_location_province WHERE title=' . $db->quote($diachi_tinh) . 'OR alias =' . $db->quote(change_alias($diachi_tinh)));
        $result_diachi = $sql_diachi->fetch();
        $row['province'] = !empty($result_diachi['idprovince']) ? $result_diachi['idprovince'] : 0;
        if (!empty($diachi_quan)) {
            $sql_diachi = $db->query('SELECT id as iddistrict FROM ' . NV_PREFIXLANG . '_location_district WHERE alias LIKE ' . $db->quote('%' . $diachi_quan . '%') . (!empty($row['province']) ? ' AND idprovince=' . intval($row['province']) : ''));
            $result_diachi = $sql_diachi->fetch();
        }
        $row['district'] = !empty($result_diachi['iddistrict']) ? $result_diachi['iddistrict'] : 0;
        if (!empty($diachi_phuong)) {
            $sql_diachi = $db->query('SELECT id as idward, iddistrict, idprovince FROM ' . NV_PREFIXLANG . '_location_ward WHERE alias LIKE ' . $db->quote('%' . $diachi_phuong . '%') . (!empty($row['district']) ? ' AND iddistrict=' . intval($row['district']) : '') . (!empty($row['province']) ? ' AND idprovince=' . intval($row['province']) : ''));
            $result_diachi = $sql_diachi->fetch();
        }
        $row['ward'] = !empty($result_diachi['idward']) ? $result_diachi['idward'] : 0;
        if (!empty($row['ward']) && (empty($row['district']) || empty($row['province']))) {
            $row['district'] = $result_diachi['iddistrict'];
            $row['province'] = $result_diachi['idprovince'];
        }
        if (empty($row['ward']) && !empty($arr_diachi['diachi_phuong']) && strpos(mb_strtolower(nv_compound_unicode($diachi_sonha)), trim($arr_diachi['diachi_phuong'])) === false && strpos(trim($arr_diachi['diachi_quan']), trim($arr_diachi['diachi_phuong'])) === false) {
            $diachi_sonha .= (!empty($diachi_sonha) ? ', ' : '') . trim($arr_diachi['diachi_phuong']);
        }
    }
    $handled_address = [];
    if ($row['ward'] != 0) {
        $handled_address['address'] = $diachi_sonha;
        $handled_address['ward'] = $row['ward'];
        $handled_address['district'] = $row['district'];
        $handled_address['province'] = $row['province'];
    }
    return $handled_address;
}

function get_address($dia_chi, $_tinh_thanh_pho)
{
    global $db, $dbcr;
    $arr_diachi_2 = explode('.', $dia_chi);
    $arr_diachi = [];

    if (sizeof($arr_diachi_2) > 3) {
        // $dia_chi = str_replace('.', ',', $dia_chi);
        $dia_chi = preg_replace('/TP\,|Tp\,/', '', $dia_chi);
        $dia_chi = str_replace('Tp,HCM', 'TP.Hồ Chí Minh', $dia_chi);
        $dia_chi = str_replace('Tx,', 'Thị xã', $dia_chi);
        $dia_chi = str_replace('Q,', 'Quận', $dia_chi);

        $dia_chi = str_replace('-p,', ',Phường ', $dia_chi);
        $dia_chi = str_replace('-tx', ',Thị xã ', $dia_chi);
        $dia_chi = str_replace('-t,', ',tỉnh ', $dia_chi);
        $arr_diachi = explode(',', $dia_chi);
    }

    if (preg_match('/tỉnh|Tỉnh|thành phố|Thành phố/', trim($_tinh_thanh_pho), $s)) {
        $_tinh_thanh_pho = trim(str_replace($s[0], '', $_tinh_thanh_pho));
        $_tinh_thanh_pho = ($_tinh_thanh_pho == 'Hồ Chí Minh') ? 'TP.Hồ Chí Minh' : $_tinh_thanh_pho;
    }
    $diachi_tinh = trim($_tinh_thanh_pho);
    $dia_chi = nv_compound_unicode($dia_chi);
    if ($dia_chi == 'Số 10, tổ dân phố Ninh Hải 4') {
        $dia_chi = 'số 10, tổ dân phố ninh hải 4, phường anh dũng, quận dương kinh';
    }
    $dia_chi = preg_replace("/[\s]*việt[\s]*nam[\s]*$/iu", "", mb_strtolower($dia_chi));
    $dia_chi = str_replace('việt nam', '', mb_strtolower($dia_chi));
    $dia_chi = str_replace('hcm', 'hồ chí minh', mb_strtolower($dia_chi));
    $dia_chi = str_replace('bắc kạn', 'bắc cạn', mb_strtolower($dia_chi));
    $dia_chi = str_replace('tx. thủ dầu một', 'thành phố thủ dầu một', mb_strtolower($dia_chi));

    $dia_chi = trim(preg_replace('/hn/', 'hà nội', mb_strtolower($dia_chi)));
    $dia_chi = trim(preg_replace('/đắk rlấp/', 'đắk R\'Lấp', mb_strtolower($dia_chi)));
    $dia_chi = trim(preg_replace('/eahleo|ea hleo/', 'ea h\'leo', mb_strtolower($dia_chi)));
    $dia_chi = trim(preg_replace('/earal/', 'ea ral', mb_strtolower($dia_chi)));
    $dia_chi = trim(preg_replace('/eadrăng/', 'ea drăng', mb_strtolower($dia_chi)));
    $dia_chi = trim(preg_replace('/sadec/', 'sa đéc', mb_strtolower($dia_chi)));
    $dia_chi = trim(preg_replace('/đăk lăk/', ' đắc lắc ', mb_strtolower($dia_chi)));
    $dia_chi = trim(preg_replace('/bắc kạn/', 'bắc cạn', mb_strtolower($dia_chi)));
    $dia_chi = trim(preg_replace('/bmt/', ' buôn ma thuột ', mb_strtolower($dia_chi)));
    $dia_chi = trim(preg_replace('/thành phố tam điệp/', 'thị xã tam điệp', mb_strtolower($dia_chi)));
    $dia_chi = trim(preg_replace('/thị xã đông triều/', 'huyện đông triều', mb_strtolower($dia_chi)));
    $dia_chi = trim(preg_replace('/t.t.huế/', ' thừa thiên huế ', mb_strtolower($dia_chi)));
    $dia_chi = preg_replace('/kp\./', ' khu phố ', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/[\s]+tt\.[\s]+|thi tran/', ' thị trấn ', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/[\s]+h\.[\s]+|huỵen/', ' huyện ', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/[\s]+tx[\s]+/', ' thị xã ', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/t\.p/', 'thành phố', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/[\s]+t\.|[\s]+t[\s]+|tinihr/', ' tỉnh ', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/tp\,hcm|tp\.hồ chí minh|tp\. hồ chí minh.|tphồ chí minh/', 'thành phố hồ chí minh', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/[\s]+tp\,[\s]+|[\s]+tp\./', ' thành phố ', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/[\s]+q[\s]+|[\s]+q\.|q\.|[\s]+q\,|quân/', ' quận ', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/lạc long  quận/', 'lạc long quân', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/[\s]+p\.|[\s]+p\.|\-p\,/', ' phường ', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/bà rịa, vũng tàu|brvt/', ' bà rịa - vũng tàu ', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/mdrak/', 'm\'drắk', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/bút sơn/', 'hoằng hóa', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/plei kần/', 'plei cần', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/nông trường mộc châu/', 'nt mộc châu', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/thị trấn xuân hòa/', 'xã xuân hòa', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/quận bình thạch/', 'quận bình thạnh', mb_strtolower($dia_chi));
    $dia_chi = preg_replace('/tĩnh quảng nam/', 'tỉnh quảng nam', mb_strtolower($dia_chi));
    for ($i = 1; $i <= 12; $i++) {
        $dia_chi = preg_replace('/q' . $i . '|q\.\s*' . $i . '|quận 0' . $i . '/iu', 'quận ' . $i, mb_strtolower($dia_chi));
    }
    for ($i = 1; $i <= 27; $i++) {
        $dia_chi = preg_replace('/p' . $i . '|p\.\s*' . $i . '|phường 0' . $i . '/iu', 'phường ' . $i, mb_strtolower($dia_chi));
    }

    if (strpos(change_alias($dia_chi), change_alias(mb_strtolower('hồ chí minh'))) === false) {
        $dia_chi = trim(preg_replace('/tp/', 'thành phố', $dia_chi));
    } else {
        $dia_chi = trim(preg_replace('/tphồ chí minh/', 'tp.hồ chí minh', $dia_chi));
    }

    $_tmp_dia_chi = $dia_chi;
    $dia_chi = str_replace(',', ' ', $dia_chi);
    $dia_chi = mb_ereg_replace('[\s]+', ' ', $dia_chi);
    if ($dia_chi == 'số 10  tổ dân phố ninh hải 4') {
        $diachi_quan = 'Dương Kinh ';
        $diachi_phuong = 'Anh Dũng';
        $diachi_sonha = $dia_chi;
    } elseif ($dia_chi == 'lầu 20 tòa nhà a') {
        $diachi_quan = 'Quận 5';
        $diachi_phuong = 'Phường 1';
        $diachi_sonha = $dia_chi;
    } elseif (preg_match('/(.*)(thị trấn|[\s]+phường|\sp\.|xã)(.+)(\sq\.|\sq\.1|tx|thị xã|quận|huyên|huyện|\sh\.|thành phố|\stp\.|\stp\s)(.+)(\st\.|thành phố\.|thành phố|tinh|tỉnh|\stp\.|\stp\s)(.+)/iu', mb_strtolower($dia_chi), $m)) {
        $m[4] = str_replace('tx', 'thị xã', $m[4]);
        $m[3] = str_replace('khối quán hành', 'quán hành', $m[3]);
        $_ckeckzero = explode('0', $m[5]);
        // if (is_int($_ckeckzero[sizeof($_ckeckzero) - 1])) {
        // $dbcr->query("UPDATE nv4_vi_businesslistings_infourl SET url_run='-2', crawls_info+=" . $dbcr->quote("; Error 205") . " WHERE id=" . $id);
        // return false;
        // die('dừng đây');
        // $m[5] = str_replace('0', '', $m[5]);
        // }

        $m[5] = str_replace('nho quang ', 'nho quan', $m[5]);
        if ($m[2] == 'quận' || trim($m[4]) == 'quận') {
            $diachi_quan = str_replace('q.', 'quận', $m[4]) . ' ' . $m[5];
        } else {
            $diachi_quan = $m[5];
        }

        $diachi_quan = mb_ereg_replace('[\s]+', ' ', $diachi_quan);
        $diachi_quan = str_replace('thị xã thủ dầu một', 'thành phố thủ dầu một', $diachi_quan);

        $diachi_phuong = str_replace('p.', 'phường', $m[2]) . ' ' . $m[3];
        $diachi_sonha = trim($m[1]);
        $_check = $db->query('SELECT COUNT(*) FROM ' . NV_PREFIXLANG . '_location_district WHERE idprovince IN (SELECT id FROM ' . NV_PREFIXLANG . '_location_province WHERE title = ' . $db->quote($diachi_tinh) . ') AND alias LIKE ' . $db->quote('%' . change_alias(trim($diachi_quan))))
            ->fetchcolumn();
        if ($_check == 0) {
            $diachi_tinh = trim($m[7]);
            if (strpos(mb_strtolower($diachi_tinh), 'hồ chí minh') !== false) {
                $diachi_tinh = 'tp.hồ chí minh';
            }
        }

        if (!empty($arr_diachi)) {
            if ((trim($arr_diachi[sizeof($arr_diachi) - 2]) == 'Thành phố Hồ Chí Minh' || strpos(mb_strtolower(trim($m[7])), 'hcm') !== false) && $diachi_tinh == 'Hà Nội') {
                $diachi_tinh = 'TP.Hồ Chí Minh';
            }
        }
    } else if (preg_match('/(.*)(\stx\.|\stx\s|thị xã|thị trấn|quận|huyện|\sh\.|\sq\.|thành phố|\stp\.|\stp\s)(.+)(thành phố|tinh|tỉnh|\stp\.|\stp\s)(.+)/iu', mb_strtolower($dia_chi), $m)) {
        if ($m[2] == 'quận') {
            $diachi_quan = $m[2] . ' ' . $m[3];
        } else {
            $diachi_quan = $m[3];
        }
        $_ckeckzero = explode('0', $diachi_quan);
        if ((int) $_ckeckzero[sizeof($_ckeckzero) - 1]) {
            $diachi_quan = str_replace('0', '', $diachi_quan);
        }
        $_ckeckzero = explode('phường', $m[1]);
        if (sizeof($_ckeckzero) > 1) {
            $diachi_phuong = 'phường ' . $_ckeckzero[1];
        } else {
            $diachi_phuong = $m[1];
        }

        $diachi_sonha = trim($m[1]);
        $_check = $db->query('SELECT COUNT(*) FROM ' . NV_PREFIXLANG . '_location_district WHERE idprovince IN (SELECT id FROM ' . NV_PREFIXLANG . '_location_province WHERE title = ' . $db->quote($diachi_tinh) . ') AND alias LIKE ' . $db->quote('%' . change_alias(trim($m[3]))))
            ->fetchcolumn();
        if ($_check == 0) {
            $diachi_tinh = trim($m[5]);
            if (strpos(mb_strtolower($diachi_tinh), 'hồ chí minh') !== false) {
                $diachi_tinh = 'tp.hồ chí minh';
            }
        }
    } else if (preg_match('/(.*)(\sp\.|phuong|phường|xã|thị trấn)(.+)(\stp\.*|thanh pho|\sq\.|\stx\.|\stx|thị xã|quận|huyện|\sh\.|\stp\.|thành phố)(.+)/iu', mb_strtolower($dia_chi), $m)) {
        // lầu 5 tòa nhà tân hoàng long 25/68 nguyễn bỉnh khiêm p.bến nghé q.1 thành phố hồ chí minh
        $m[5] = trim(preg_replace('/thành phố/', '', $m[5]));
        $m[5] = trim(preg_replace('/tỉnh bà rịa vũng tàu/', 'bà rịa - vũng tàu', $m[5]));
        if (trim($m[5]) == 'vĩnh yên vĩnh phúc') {
            $m[5] = 'vĩnh yên';
            $diachi_tinh = 'vĩnh phúc';
        }
        $m[4] = preg_replace('/\stx\.|\stx/', 'thị xã', $m[4]);

        $diachi_quan = '';
        if (strpos(mb_strtolower(trim($m[5])), 'hồ chí minh') === false and strpos(mb_strtolower(trim($m[5])), 'hà nội') === false) {
            if (strpos(change_alias($m[5]), change_alias('tỉnh')) !== false) {
                $diachi_quan = str_replace('q.', 'quận', $m[4]) . ' ' . str_replace('tỉnh ' . mb_strtolower($diachi_tinh), '', trim($m[5]));
                $diachi_quan = str_replace('q.', 'quận', $m[4]) . ' ' . str_replace('tỉnh', '', $m[5]);
            } else {
                $diachi_quan = str_replace('q.', 'quận', $m[4]) . ' ' . str_replace('-' . mb_strtolower(change_alias($diachi_tinh)), '', change_alias($m[5]));
            }
        } elseif (trim($m[3]) == '11 phú nhuận') {
            $diachi_quan = 'phú nhuận';
            $diachi_phuong = 'phường 11';
        } elseif (trim($m[3]) == 'trung mỹ tây') {
            $diachi_quan = 'quận 12';
            $diachi_phuong = 'trung mỹ tây';
        } elseif (trim($m[3]) == 'bình trị đông b bình tân') {
            $diachi_quan = 'bình tân';
            $diachi_phuong = 'bình trị đông b';
        } elseif (trim($m[3]) == 'linh xuân thủ đức') {
            $diachi_quan = 'thủ đức';
            $diachi_phuong = 'linh xuân';
        } elseif (trim($m[3]) == 'bình hưng bình chánh') {
            $diachi_quan = 'bình chánh';
            $diachi_phuong = 'bình hưng';
        } else {
            $m[5] = trim(preg_replace('/thành phố hồ chí minh|hồ chí minh|tp.hồ chí minh|thành phố hà nội/', '', $m[5]));
            $diachi_quan = str_replace('q.', 'quận', $m[4]) . ' ' . str_replace('-' . mb_strtolower(change_alias($diachi_tinh)), '', change_alias($m[5]));
            $diachi_quan = trim(preg_replace('/thành phố hồ chí minh|hồ chí minh|tp.hồ chí minh|thành phố hà nội|thành phố/', '', $diachi_quan));
        }
        $diachi_quan = str_replace('thị xã thủ dầu một', 'thành phố thủ dầu một', $diachi_quan);
        $diachi_quan = str_replace('thành phố bac-can', 'thị xã bac-can', $diachi_quan);

        $diachi_phuong = str_replace('p.', 'phường', $m[2]) . ' ' . (!empty($diachi_phuong) ? $diachi_phuong : trim($m[3]));
        $diachi_sonha = trim($m[1]);
    } else {
        $_tmp_dia_chi = str_replace('–', ',', $_tmp_dia_chi);
        $_tmp_dia_chi = preg_replace('/(thành phố[\s]+hà nội|hà nội)$/', '', $_tmp_dia_chi);
        $_tmp_dia_chi = trim($_tmp_dia_chi);
        $_tmp_dia_chi = trim($_tmp_dia_chi, ',');
        $_arr_diachi_tmp = explode(',', $_tmp_dia_chi);
        if ($_arr_diachi_tmp[sizeof($_arr_diachi_tmp) - 1] == '') {
            unset($_arr_diachi_tmp[sizeof($_arr_diachi_tmp) - 1]);
            $_arr_diachi_tmp = array_values($_arr_diachi_tmp);
        }

        $_tmp_quan = $_arr_diachi_tmp[sizeof($_arr_diachi_tmp) - 1];

        if (preg_match('/tỉnh|thành phố/', mb_strtolower($_tinh_thanh_pho), $s) and sizeof($_arr_diachi_tmp) > 1) {
            if (strpos($_arr_diachi_tmp[sizeof($_arr_diachi_tmp) - 1], $s[0]) === false) {
                $_arr_diachi_tmp[sizeof($_arr_diachi_tmp) - 1] = $s[0] . ' ' . trim($_arr_diachi_tmp[sizeof($_arr_diachi_tmp) - 1]);
            }
        }
        if (strpos(change_alias($_arr_diachi_tmp[sizeof($_arr_diachi_tmp) - 1]), change_alias('hồ chí minh')) !== false) {
            $_arr_diachi_tmp[sizeof($_arr_diachi_tmp) - 1] = 'tp.hồ chí minh';
        }
        if (strpos(mb_strtolower($_tinh_thanh_pho), 'hồ chí minh') !== false) {
            $_tinh_thanh_pho = 'tp.hồ chí minh';
        }

        if (change_alias($_tmp_dia_chi) == change_alias('lô 36/23 khu đô thị hoà vượng, lộc hoà, nam định')) {
            $diachi_quan = 'nam định';
            $diachi_phuong = 'xã lộc hoà';
            $diachi_sonha = 'lô 36/23 khu đô thị hoà vượng';
        } elseif (change_alias($_arr_diachi_tmp[0]) == change_alias('xã cổ đông sơn tây')) {
            $diachi_quan = 'sơn tây';
            $diachi_phuong = 'xã cổ đông';
            $diachi_sonha = '';
        } elseif (strpos(change_alias($_arr_diachi_tmp[sizeof($_arr_diachi_tmp) - 1]), change_alias(mb_strtolower($_tinh_thanh_pho))) !== false || change_alias($_arr_diachi_tmp[sizeof($_arr_diachi_tmp) - 1]) == 'ha-noi') {
            $diachi_quan = $_arr_diachi_tmp[sizeof($_arr_diachi_tmp) - 2];
            if (change_alias($diachi_quan) == 'q3') {
                $diachi_quan = 'quận 3';
            }

            $diachi_quan = str_replace('q.', 'quận ', $diachi_quan);
            // $diachi_quan = trim(preg_replace('/[\s]+q/', 'quan-', $diachi_quan));
            $diachi_quan = trim(preg_replace('/q-/', 'quan-', change_alias($diachi_quan)));

            $diachi_phuong = $_arr_diachi_tmp[sizeof($_arr_diachi_tmp) - 3];
            if (change_alias($diachi_phuong) == 'p6') {
                $diachi_phuong = 'phường 6';
            } else if (change_alias($diachi_phuong) == 'p4') {
                $diachi_phuong = 'phường 4';
            }

            if (strpos(change_alias($diachi_phuong), '-p-') !== false) {
                $diachi_phuong = trim(preg_replace('/p-|[\s]+p/', 'phuong-', change_alias($diachi_phuong)));
            }

            array_splice($_arr_diachi_tmp, sizeof($arr_diachi) - 3);
            $diachi_sonha = sizeof($_arr_diachi_tmp) > 1 ? implode(',', $_arr_diachi_tmp) : $_arr_diachi_tmp[0];
        } else {
            $diachi_quan = trim($_tmp_quan);
            $diachi_phuong = trim($_arr_diachi_tmp[sizeof($_arr_diachi_tmp) - 2]);
            array_splice($_arr_diachi_tmp, sizeof($arr_diachi) - 2);
            $diachi_sonha = sizeof($_arr_diachi_tmp) > 1 ? implode(',', $_arr_diachi_tmp) : $_arr_diachi_tmp[0];
        }
        $_ckeckzero = explode('p', $diachi_phuong);
        if ((int) $_ckeckzero[sizeof($_ckeckzero) - 1]) {
            $diachi_sonha .= (!empty($diachi_sonha) ? ', ' : '') . $diachi_phuong;
            $diachi_phuong = str_replace('p', 'phường', $diachi_quan);
        }
        $_ckeckzero = explode('p', $diachi_phuong);
        if ((int) $_ckeckzero[sizeof($_ckeckzero) - 1]) {
            $diachi_phuong = str_replace('p', 'phường', $diachi_phuong);
        }
    }
    $arr_diachi = array();
    $arr_diachi['diachi_sonha'] = $diachi_sonha;
    $arr_diachi['diachi_phuong'] = $diachi_phuong;
    $arr_diachi['diachi_quan'] = $diachi_quan;
    $arr_diachi['diachi_tinh'] = $diachi_tinh;
    return $arr_diachi;
}

function send_mail_renewal($business_id)
{
    global $db, $config;

    try {
        $stmt = $db->prepare("INSERT INTO `" . $config['prefix'] . "_business_renewal_mail`(`business_id`, `add_time`) VALUES (:business_id, " . NV_CURRENTTIME . ")");
        $stmt->bindValue(':business_id', $business_id, PDO::PARAM_INT);
        $exc = $stmt->execute();
        if ($exc) {
            return $db->lastInsertId();
        }
        return false;
    } catch (PDOException $e) {
        print_r('Lỗi gửi mail');
        echo '<pre>';
        print_r($e);
        echo '</pre>';
        return false;
    }
}
